package ok

type OkexAPIConfig struct {
	BaseAPIUrl    string // 欧意 Base api
	WsUrlPublic   string // OkWsUrlPublic Ok WebSocket公共频道
	WsUrlPrivate  string // OkWsUrlPrivate Ok WebSocket私有频道
	WsUrlBusiness string // OkWsUrlBusiness Ok WebSocket业务频道
}

var DefaultOkexAPI = OkexAPIConfig{
	BaseAPIUrl:    "https://www.okx.com/",
	WsUrlPublic:   "wss://ws.okx.com:8443/ws/v5/public",
	WsUrlPrivate:  "wss://ws.okx.com:8443/ws/v5/private",
	WsUrlBusiness: "wss://ws.okx.com:8443/ws/v5/business",
}

var ProxyOkexAPI = OkexAPIConfig{
	BaseAPIUrl:    "http://okapi.a.indigo888.com/",
	WsUrlPublic:   "ws://okws.a.indigo888.com/ws/v5/public",
	WsUrlPrivate:  "ws://okws.a.indigo888.com/ws/v5/private",
	WsUrlBusiness: "ws://okws.a.indigo888.com/ws/v5/business",
}
