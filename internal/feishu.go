// feishu通知配置

package config

type Group struct {
	Hook   string
	Secret string
}

const (
	// 通知人
	UserLiquid  = "gd1d6b34"
	UserJiaoyin = "efdgdecb"
	UserSmall   = "db9d9g4d"
	UserTanli   = "f16c2bd7"
	User007     = "9de6cgf3"
)

// 通知组
var (
	// 个人测试
	GroupLiquidTest = Group{
		Hook:   "https://open.feishu.cn/open-apis/bot/v2/hook/65f35e44-c6d0-4f45-87a9-886ea5344aa2",
		Secret: "YZRRolc8PD8E7thD0Fnoce",
	}

	// 套利通知
	GroupArbit = Group{
		Hook:   "https://open.feishu.cn/open-apis/bot/v2/hook/f2ed4bbe-4e1d-4e63-9e61-358998b8dfe9",
		Secret: "iMi8KxqxsW6HFRROcRbwQg",
	}

	// 价差通知
	DiffNotify = Group{
		Hook:   "https://open.feishu.cn/open-apis/bot/v2/hook/073ad96d-f40f-4427-9d27-76ae6beba952",
		Secret: "dOWXzWsi7oRLjkc6bfO2te",
	}

	// 零忽略
	GroupZeroIgnore = Group{
		Hook:   "https://open.feishu.cn/open-apis/bot/v2/hook/5590fcb6-e841-43ad-8d50-5825cec0da46",
		Secret: "5VvZNJqWB6jariMTm3cpzd",
	}
)
