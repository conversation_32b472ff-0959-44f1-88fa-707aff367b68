package bn

type BinanceAPIConfig struct {
	PortfolioAPIURL       string // 币安统一账户api
	FuturesAPIURL         string // 币安期货Restapi
	SpotAPIURL            string // 币安现货Restapi
	PortfolioAccountWSURL string // 币安统一账户信息推送url
	SpotAccountWSURL      string // 币安现货账户数据推送url
	FuturesAccountWSURL   string // 币安合约账户信息推送url
	SpotStreamWSURL       string // 币安现货数据推送url
	FuturesStreamWSURL    string // 币安合约数据推送url
}

var DefaultBinanceAPI = BinanceAPIConfig{
	PortfolioAPIURL:       "https://papi.binance.com/",
	FuturesAPIURL:         "https://fapi.binance.com/",
	SpotAPIURL:            "https://api.binance.com/",
	PortfolioAccountWSURL: "wss://fstream.binance.com/pm/ws/",
	SpotAccountWSURL:      "wss://stream.binance.com:9443/ws/",
	FuturesAccountWSURL:   "wss://fstream.binance.com/ws/",
	SpotStreamWSURL:       "wss://stream.binance.com:9443/stream",
	FuturesStreamWSURL:    "wss://fstream.binance.com/stream",
}

var ProxyBinanceAPI = BinanceAPIConfig{
	PortfolioAPIURL:       "http://bnpapi.a.indigo888.com/",
	FuturesAPIURL:         "http://bnfapi.a.indigo888.com/",
	SpotAPIURL:            "http://bnapi.a.indigo888.com/",
	PortfolioAccountWSURL: "ws://bnws.a.indigo888.com/futures/pm/ws/",
	SpotAccountWSURL:      "ws://bnws.a.indigo888.com/spot/ws/",
	FuturesAccountWSURL:   "ws://bnws.a.indigo888.com/futures/ws/",
	SpotStreamWSURL:       "ws://bnws.a.indigo888.com/spot/stream",
	FuturesStreamWSURL:    "ws://bnws.a.indigo888.com/futures/stream",
}
