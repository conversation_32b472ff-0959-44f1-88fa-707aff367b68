package main

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/factory"
	"GoTrader/pkg/utils"
	"context"
	"fmt"
	"time"
)

func main() {
	SymbolName := "BNBUSDT"
	// 任务log
	// logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建 Broker 对象
	// b := broker.NewBroker(
	// 	"qUIkfqo5RV2wO4bu8s6eo8vrErlLHBtHiVoX6ntAVqVSI9QtIvJ6tBn1z2dy7bFq",
	// 	"YLQfajXqLeleFyVpW8dXazoiX74IEk5nQjwKYSFHqtDcpe4Ete3yEh9eUnB1d3Uz",
	// 	"",
	// 	broker.Portfolio,
	// 	nil,
	// )
	b := broker.NewBroker(
		"mcONGSR3nRuoRySWSSu5A6RQ8VE0rYE2B33TS3Vs9EB67PYm7HmALqVjAqASQJaO",
		"kKpcc9v3Kjb01KAL8urbDdKs2VL2ZZuiC1YWQBQBz3t86Ug1q1B4hRDszhzI08sl",
		"",
		"testbn",
		broker.Costom,
		nil,
	)

	dc, ac, err := factory.NewBnClients(ctx, b, true, utils.StdoutLogger, utils.StdoutLogger, utils.StdoutLogger)
	if err != nil {
		fmt.Println(err.Error())
		return
	}

	fd := data.NewSymbolData(SymbolName, data.Futures)
	sd := data.NewSymbolData(SymbolName, data.Spot)

	ac.UpdateFuturesLeverageSingle(b, fd)

	dc.SubscribeDatas([]*data.SymbolData{fd, sd})
	ac.UpdateDatas([]*data.SymbolData{fd, sd})
	ac.UpdateBrokerUMPending(b, []*data.SymbolData{fd})
	ac.UpdateBrokerMarginPending(b, []*data.SymbolData{fd})
	ac.UpdatePositionTiers(b, fd)

	ac.ModifyFuturesLeverage(b, fd, 5)

	// o := order.CreateOrder().SetSymbol(SymbolName).SetClOrdId("test").SetPrice(60).
	// 	SetSize(0.02).SetSide(order.BUY).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)

	// fo := order.CreateFuturesOrderFromCommon(o, order.LONG)
	// _, err = ac.SubmitFuturesOrder(b, fo, fd.Info)

	// if err != nil {
	// 	fmt.Println(err.Error())
	// 	// return
	// }

	// go func() {
	// 	time.Sleep(11 * time.Second)
	// 	ac.CancelFuturesOrder(b, fo)
	// 	// mo := fo.Copy()
	// 	// mo.SetPrice(0.231).SetSize(49)
	// 	// err = ac.ModifyFuturesOrder(b, mo, fd.Info)
	// }()

	// openInt, _ := ac.QueryOpenInterest(fd)

	// accountIfo, _ := ac.QueryAccount(b)
	// fmt.Println(accountIfo)

	// 定时打印broker
	ticker := time.NewTicker(2 * time.Second)
	for range ticker.C {
		// fmt.Printf("fd max price %f, min price %f \n", fd.Info.MaxPrice, fd.Info.MinPrice)
		// fmt.Printf("fd max price percent %f, min price percent %f \n", fd.Info.MaxPricePercent, fd.Info.MinPricePercent)
		// fmt.Printf("fd min order price %f, max order price %f \n", fd.MinOrderPriceStrict(), fd.MaxOrderPriceStrict())
		// fmt.Printf("sd max price %f, min price %f \n", sd.Info.MaxPrice, sd.Info.MinPrice)
		// fmt.Printf("sd max price percent %f, min price percent %f \n", sd.Info.MaxPricePercent, sd.Info.MinPricePercent)
		// fmt.Printf("sd min order price %f, max order price %f \n", sd.MinOrderPriceStrict(), sd.MaxOrderPriceStrict())
		// fmt.Printf("open interest %f \n", openInt)
		// fmt.Printf("futures data info %+v \n", fd.Info)
		// fmt.Printf("spot data info %+v \n", sd.Info)
		// fmt.Printf("futures Commission %+v \n", b.FuturesCommission)
		// fmt.Printf("spot Commission %+v \n", b.SpotCommission)
		// fmt.Printf("futures data fund period %v, funding rate %f,  max funding rate %f, min funding rate %f \n",
		// 	fd.FundingPeriod(), fd.Funding.Rate, b.FundingCap(fd), b.FundingFloor(fd))
		// fmt.Printf("index price %f, price %f \n", fd.Index.Last(), fd.Bid1())
		// fmt.Printf("spot data fund period %v ", sd.FundingPeriod())
		// fmt.Printf("futures Available %f \n", b.GetAvailable(fd))
		// fmt.Printf("spot Available %f \n", b.GetAvailable(sd))
		// fmt.Printf("Account %+v \n", b.Account)
		// fmt.Printf("AssetWallets %+v %+v \n", b.AssetWallets["USDT"], b.AssetWallets[sd.Asset])
		// fmt.Printf("Positions %+v \n", b.Positions[SymbolName])
		// fmt.Printf("FuturesPending %+v \n", b.FuturesPending)
		// fmt.Printf("MarginPending %+v \n", b.MarginPending)
		// fmt.Printf("MaxSize %.6f \n", b.TierMaxSize(fd))
		// fmt.Printf("Leverage %+v \n", b.Leverages[SymbolName])
		// fmt.Printf("ctvals %+v \n", b.CtVals[SymbolName])
		// fmt.Printf("posTiers %+v \n", b.PositionTiers[SymbolName])
		// fmt.Printf("mmr %.f \n", b.GetMaintenanceMarginRate())
		// fmt.Printf("fd tick size %f \n", fd.Info.TickSize)
		// fmt.Printf("sd tick size %f \n", sd.Info.TickSize)
		// fmt.Printf("fd round to step %f \n", utils.RoundToStep(fd.Bid1()*(1-0.005), fd.Info.TickSize))
		// fmt.Printf("sd round to step %f \n", utils.RoundToStep(sd.Ask1()*(1+0.005), sd.Info.TickSize))
		fmt.Printf("fd books %+v \n", fd.BookNAll(20))
		// fmt.Printf("sd asks %+v \n", sd.BookNAll(20))
		// fmt.Printf("fd asks %+v \n", fd.BookNAll(400))
		// fmt.Printf("fd asks agg %+v \n", fd.BookNAllAgg(0, 1))
	}
}
