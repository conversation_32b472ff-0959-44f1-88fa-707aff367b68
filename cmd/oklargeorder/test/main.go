package main

import (
	config "GoTrader/internal"
	"GoTrader/internal/bn"
	"GoTrader/internal/ok"
	api "GoTrader/pkg/api/bn"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	bnclient "GoTrader/pkg/client/bn"
	"GoTrader/pkg/data"
	"GoTrader/pkg/factory"
	"GoTrader/pkg/utils"
	"GoTrader/strategy"
	"GoTrader/strategy/book"
	"context"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"syscall"

	"github.com/natefinch/lumberjack"
	"github.com/redis/go-redis/v9"

	_ "net/http/pprof"
)

func main() {
	go func() {
		runtime.SetMutexProfileFraction(1)
		fmt.Println(http.ListenAndServe("localhost:6060", nil))
	}()
	taskName := "TestOkLargeOrder"

	accountName := "testLiquid"

	// 阈值
	keepAvailable := 0.0

	// 任务log
	taskLogger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	// api客户端log
	clientPath := filepath.Join(config.TestLogDir, taskName, "client.log")
	clientFile := &lumberjack.Logger{
		Filename:   clientPath, // 日志文件的位置
		MaxSize:    10,         // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,          // 保留的最大旧文件数量
		MaxAge:     7,          // 保留旧文件的最大天数
		Compress:   true,       // 是否压缩/归档旧文件
		LocalTime:  true,       // 使用本地时间创建时间戳
	}
	defer clientFile.Close()
	multiWriter := io.MultiWriter(os.Stdout, clientFile)
	clientLogger := slog.New(slog.NewJSONHandler(multiWriter, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	defer func() {
		if r := recover(); r != nil {
			// 如果发生了 panic，打印堆栈信息
			clientLogger.Error("Recovered from panic:", "panic", r)
			clientLogger.Error(string(debug.Stack())) // 打印堆栈信息
		}
	}()

	rdb := redis.NewClient(&redis.Options{
		Addr:     config.TaskAddr,
		Password: config.TaskPassword,
		DB:       1,
	})

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	account := ok.OkAccounts[accountName]

	// 固定品种参数
	fixedSymbolParams := map[string]book.BookParams{
		"TEST_OK": {
			ID:                 "TEST_OK",
			Symbol:             "DOGEUSDT",
			Leverage:           20,
			Exchange:           "OK",
			MinAvailable:       keepAvailable,
			CapitalLimit:       10,
			PriceLevel:         5,
			QuoteCoeff:         0.002,
			OpenIntCoeff:       0.02,
			MinLargeOrderValue: 100000,
			Status:             strategy.Reduce,
		},
	}

	// 策略表
	strategyMap := map[string]*book.LargeOrderBookStrategy{}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建 Broker 对象
	b := broker.NewBroker(account["API_KEY"], account["API_SECRET"], account["PASSPHRASE"], account["DB_INDEX"], broker.Portfolio, nil)

	// 创建客户端
	dc, ac, err := factory.NewOkClients(ctx, b, true, utils.DiscardLogger, nil)

	// 默认api或代理api
	apiConfig := bn.ProxyBinanceAPI

	bnac := bnclient.NewCostomAPIClient(
		apiConfig.FuturesAPIURL,
		apiConfig.SpotAPIURL,
		api.CostomBrokerAPIs,
		http.DefaultClient,
		clientLogger,
	)

	if err != nil {
		clientLogger.Error("error create client", "error", err)
		return
	}

	// 运行任务
	for ID, params := range fixedSymbolParams {
		if _, ok := strategyMap[ID]; !ok {
			symbol := params.Symbol
			// 创建 TradeData 对象
			d := data.NewSymbolData(symbol, data.Futures)

			// 添加至客户端
			ac.UpdateDatas([]*data.SymbolData{d})
			dc.SubscribeDatas([]*data.SymbolData{d})

			// 策略logger
			straPath := filepath.Join(config.TestLogDir, taskName, ID+".log")
			straFile := &lumberjack.Logger{
				Filename:   straPath, // 日志文件的位置
				MaxSize:    10,       // 文件最大尺寸（以MB为单位）
				MaxBackups: 3,        // 保留的最大旧文件数量
				MaxAge:     7,        // 保留旧文件的最大天数
				Compress:   true,     // 是否压缩/归档旧文件
				LocalTime:  true,     // 使用本地时间创建时间戳
			}
			defer straFile.Close()
			straLogger := slog.New(slog.NewJSONHandler(io.MultiWriter(straFile, os.Stdout), &slog.HandlerOptions{
				Level: slog.LevelInfo,
			}))

			strategyMap[ID] = book.NewLargeOrderBookStrategy(d, b, ac, []client.APIClient{bnac}, params, &strategy.RedisCtx{Client: rdb, Table: config.InfoTable}, nil, straLogger)
			strategyMap[ID].Start()
			taskLogger.Info("启动任务", slog.String("symbol", symbol), slog.String("params", params.String()))
			continue
		}
	}

	// 结束进程
	<-signalChan
	taskLogger.Info("Received signal to exit")
	for _, stra := range strategyMap {
		stra.Pause()
	}
}
