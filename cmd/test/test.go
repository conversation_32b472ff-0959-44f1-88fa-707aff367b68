package main

import (
	"GoTrader/internal/bn"
	api "GoTrader/pkg/api/bn"
	bnclient "GoTrader/pkg/client/bn"
	"GoTrader/pkg/data"
	bnhandler "GoTrader/pkg/handler/bn"
	"GoTrader/pkg/utils"
	"GoTrader/pkg/wsclient"
	"context"
	"fmt"
	"net/http"
	"time"
)

func main() {

	Symbols := []string{
		"ETHUSDT",
		"BTCUSDT",
		"MYXUSDT",
		"ETHUSDC",
		"SOLUSDT",
		"BTCUSDC",
		"XRPUSDT",
		"DOGEUSDT",
		"LTCUSDT",
		"ENAUSDT",
		"1000PEPEUSDT",
		"SUIUSDT",
		"PROVEUSDT",
		"PUMPUSDT",
		"SOLUSDC",
		"ILVUSDT",
		"FARTCOINUSDT",
		"MAGICUSDT",
		"XRPUSDC",
		"SPKUSDT",
		"TOWNSUSDT",
		"ADAUSDT",
		"BNBUSDT",
		"PENGUUSDT",
		"HYPEUSDT",
		"AVAXUSDT",
		"1000BONKUSDT",
		"LINKUSDT",
		"WIFUSDT",
		"BCHUSDT",
		"UNIUSDT",
		"CRVUSDT",
		"TRUMPUSDT",
		"RAREUSDT",
		"XLMUSDT",
		"AAVEUSDT",
		"ONDOUSDT",
		"CFXUSDT",
		"HBARUSDT",
		"ETHBTC",
	}

	// 任务log
	logger := utils.StdoutLogger

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 默认api或代理api
	apiConfig := bn.ProxyBinanceAPI

	// 传统api客户端
	ac := bnclient.NewCostomAPIClient(
		apiConfig.FuturesAPIURL,
		apiConfig.SpotAPIURL,
		api.CostomBrokerAPIs,
		http.DefaultClient,
		logger,
	)

	// 创建期货数据源监听客户端
	dc := bnclient.NewDataClient(
		apiConfig.FuturesStreamWSURL,
		[]string{bn.DepthUpdateChan, bn.IndexFundingChan},
		wsclient.DefaultNew(nil, logger),
		logger,
	).
		AddStreamHandler(bnhandler.Depth, bnhandler.DataBindAPIClient(ac, bnhandler.FuturesDepthUpdateTriggerHandler)).
		AddStreamHandler(bnhandler.IndexFunding, bnhandler.IndexFundingRateHandler)

	err := dc.Run(ctx)

	if err != nil {
		fmt.Println(err.Error())
	}

	fds := []*data.SymbolData{}
	for _, s := range Symbols {
		fds = append(fds, data.NewSymbolData(s, data.Futures))
	}

	ac.UpdateDatas(fds)
	dc.SubscribeDatas(fds)

	location, _ := time.LoadLocation("Asia/Shanghai")
	// 定时打印
	ticker := time.NewTicker(2 * time.Second)
	for range ticker.C {
		fmt.Printf("%s \n", time.UnixMilli(int64(fds[0].Time.Last())).In(location).Format("2006-01-02 15:04:05.000 MST"))
	}
}
