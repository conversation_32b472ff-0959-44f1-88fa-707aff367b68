package main

import (
	config "GoTrader/internal"
	"GoTrader/internal/bn"
	"GoTrader/internal/ok"
	"GoTrader/pkg/logs"
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"time"

	"github.com/natefinch/lumberjack"
)

var HOST = "http://**************:8006"

// 账户
var bn_costom_apikey = []string{
	// bn.BnAccounts["TestLiquidCostom"]["API_KEY"],
	bn.BnAccounts["CrossLeft"]["API_KEY"],
	bn.BnAccounts["CrossRight"]["API_KEY"],
	bn.BnAccounts["BnCrossFund"]["API_KEY"],
	bn.BnAccounts["BnCrossDiff"]["API_KEY"],
	bn.BnAccounts["CrossRightTokyo"]["API_KEY"],
}

var bn_portfolio_apikey = []string{
	bn.BnAccounts["Arbit"]["API_KEY"],
}

var ok_apikey = []string{
	// ok.OkAccounts["testLiquid"]["API_KEY"],
	ok.OkAccounts["arbit"]["API_KEY"],
	ok.OkAccounts["fund"]["API_KEY"],
	ok.OkAccounts["btcOnly"]["API_KEY"],
	ok.OkAccounts["crossLeft"]["API_KEY"],
	ok.OkAccounts["crossRight"]["API_KEY"],
	ok.OkAccounts["bnCrossFund"]["API_KEY"],
	ok.OkAccounts["bnCrossDiff"]["API_KEY"],
	ok.OkAccounts["crossLeftTokyo"]["API_KEY"],
}

// API配置
var bn_costom_apis = []string{
	"/bn/costom/income",
	"/bn/costom/um/orders",
}

var bn_portfolio_apis = []string{
	"/bn/income",
	"/bn/um/orders",
	"/bn/margin/orders",
}

var ok_apis = []string{
	"/ok/income",
	"/ok/um/orders",
	"/ok/margin/orders",
}

func main() {

	taskName := "TimedDump"
	logDir := config.LogDir

	// 检查logger
	fileWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "timedump.log"), // 日志文件的位置
		MaxSize:    10,                                              // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                               // 保留的最大旧文件数量
		MaxAge:     3,                                               // 保留旧文件的最大天数
		Compress:   true,                                            // 是否压缩/归档旧文件
		LocalTime:  true,                                            // 使用本地时间创建时间戳
	}
	feishuWriter := logs.NewFeishuWriter(
		[]string{config.UserLiquid},
		config.GroupLiquidTest.Hook,
		config.GroupLiquidTest.Secret,
		taskName,
		slog.LevelWarn,
	)

	logger := slog.New(slog.NewJSONHandler(
		io.MultiWriter(fileWriter, feishuWriter, os.Stdout),
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	httpClient := http.DefaultClient

	var prevEndTs, startTs, endTs int64

	now := time.Now()

	// 保存前n天的参数
	before := flag.Int("before", 0, "dump days before")
	flag.IntVar(before, "b", 0, "dump days before (short)")
	flag.Parse()
	if *before > 0 {
		startTs = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Add(time.Duration(-*before) * 24 * time.Hour).UnixMilli()
		endTs = now.UnixMilli()
		logger.Info("dump days before", "startTime", time.UnixMilli(startTs), "endTime", time.UnixMilli(endTs))
		// 初始化时保存过去n天数据
		dump(httpClient, startTs, endTs, logger)
		prevEndTs = endTs
	}

	// 每1分钟检查一次时间
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		now := time.Now()
		// 30分钟对齐
		rounded := now.Truncate(30 * time.Minute)

		// 过去30分钟, 保留10分钟冗余
		startTs = rounded.Add(-40 * time.Minute).UnixMilli()
		endTs = rounded.UnixMilli()

		// 已经执行过
		if endTs <= prevEndTs {
			continue
		}

		// 冗余时间小于5分钟, 发出警告
		if prevEndTs > 0 && time.UnixMilli(prevEndTs).Sub(time.UnixMilli(startTs)) < 5*time.Minute {
			logger.Warn("past data not dumped for more than 5 minutes", "prevEndTime", time.UnixMilli(prevEndTs), "currentStartTime", time.UnixMilli(startTs))
			startTs = time.UnixMilli(prevEndTs).Add(-5 * time.Minute).UnixMilli()
		}
		logger.Info("dump data", "startTIme", time.UnixMilli(startTs), "endTime", time.UnixMilli(endTs))
		dump(httpClient, startTs, endTs, logger)

		// 更新prevEndTs
		prevEndTs = endTs
	}
}

func dump(client *http.Client, startTs, endTs int64, logger *slog.Logger) {
	// 调用API
	for _, apiKey := range bn_costom_apikey {
		for _, api := range bn_costom_apis {
			callAPI(client, HOST, api, apiKey, startTs, endTs, logger)
		}
	}
	for _, apiKey := range bn_portfolio_apikey {
		for _, api := range bn_portfolio_apis {
			callAPI(client, HOST, api, apiKey, startTs, endTs, logger)
		}
	}
	for _, apiKey := range ok_apikey {
		for _, api := range ok_apis {
			callAPI(client, HOST, api, apiKey, startTs, endTs, logger)
		}
	}
}

// 调用API函数
func callAPI(client *http.Client, host, api, apikey string, startTs, endTs int64, logger *slog.Logger) error {
	// 组装host和api
	base, err := url.Parse(host)
	if err != nil {
		logger.Error("解析host错误", "error", err)
		return err
	}
	apiURL, err := url.Parse(api)
	if err != nil {
		logger.Error("解析api错误", "error", err)
		return err
	}
	fullURL := base.ResolveReference(apiURL).String()

	data := map[string]any{
		"api_key":    apikey,
		"start_time": startTs,
		"end_time":   endTs,
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		logger.Error("JSON 编码失败", "error", err)
		return err
	}

	// 创建 PUT 请求
	req, err := http.NewRequest(http.MethodPut, fullURL, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Error("创建请求失败", "error", err)
		return err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	logger = logger.With("url", fullURL, "data", data)

	// 发送请求
	response, err := client.Do(req)
	if err != nil {
		logger.Error("发送请求失败", "error", err)
		return err
	}
	defer response.Body.Close()

	// 读取响应
	body, err := io.ReadAll(response.Body)
	if err != nil {
		logger.Error("读取响应失败", "error", err)
		return err
	}

	// 检查响应码
	if response.StatusCode != 200 {
		logger.Error("响应码错误", "statusCode", response.StatusCode, "body", string(body))
		return fmt.Errorf("响应码错误: %d", response.StatusCode)
	}

	logger.Info("保存成功", "响应数据", string(body))

	return nil
}
