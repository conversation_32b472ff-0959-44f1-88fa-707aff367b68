package main

import (
	config "GoTrader/internal"
	"GoTrader/pkg/logs"
	"io"
	"log/slog"
	"os"
	"time"

	"github.com/redis/go-redis/v9"
)

// 价差通知程序

func main() {
	// redis
	tokyoRdb := redis.NewClient(&redis.Options{
		Addr:     config.TokyoRedisAddr,
		Password: config.TokyoRedisPassword,
		DB:       config.TokyoDiffDB,
	})

	hkRdb := redis.NewClient(&redis.Options{
		Addr:     config.HKRedisAddr,
		Password: config.HKRedisPassword,
		DB:       config.HKDiffDB,
	})

	feishuWriter := logs.NewFeishuWriter(
		[]string{config.UserSmall, config.User007},
		config.DiffNotify.Hook,
		config.DiffNotify.Secret,
		"价差通知",
		slog.LevelInfo,
	)

	feishuWriterNoUser := logs.NewFeishuWriter(
		[]string{},
		config.DiffNotify.Hook,
		config.DiffNotify.Secret,
		"价差通知",
		slog.LevelInfo,
	)

	logger := slog.New(slog.NewJSONHandler(io.MultiWriter(os.Stdout, feishuWriter), nil))
	loggerNoUser := slog.New(slog.NewJSONHandler(io.MultiWriter(os.Stdout, feishuWriterNoUser), nil))

	// 先执行一次
	queryDiff(tokyoRdb, hkRdb, logger, loggerNoUser)
	// 10分钟执行一次
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		queryDiff(tokyoRdb, hkRdb, logger, loggerNoUser)
	}
}
func queryDiff(tokyoRdb *redis.Client, hkRdb *redis.Client, logger *slog.Logger, loggerNoUser *slog.Logger) {
	// redis
	positiveLargeCrossDiffSymbols := getRedisDiff(tokyoRdb, config.CrossDiffTable, 0.5, false, logger)
	if len(positiveLargeCrossDiffSymbols) != 0 {
		logger.Info("跨所正向大价差的交易对", "symbols", positiveLargeCrossDiffSymbols)
	}
	negativeLargeCrossDiffSymbols := getRedisDiff(tokyoRdb, config.CrossDiffTable, 0.5, true, logger)
	if len(negativeLargeCrossDiffSymbols) != 0 {
		loggerNoUser.Info("跨所反向大价差的交易对", "symbols", negativeLargeCrossDiffSymbols)
	}
	positiveLargeOkDiffSymbols := getRedisDiff(hkRdb, config.OkDiffTable, 0.5, false, logger)
	if len(positiveLargeOkDiffSymbols) != 0 {
		logger.Info("Okex 正向大价差的交易对", "symbols", positiveLargeOkDiffSymbols)
	}
	positiveLargeBnDiffSymbols := getRedisDiff(tokyoRdb, config.BnDiffTable, 0.5, false, logger)
	if len(positiveLargeBnDiffSymbols) != 0 {
		logger.Info("Binance 正向大价差的交易对", "symbols", positiveLargeBnDiffSymbols)
	}
	// logger.Info("检查完毕")
}
