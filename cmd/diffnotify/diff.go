package main

import (
	"context"
	"encoding/json"
	"log/slog"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

type DiffInfo struct {
	Diff        string `json:"价差"`
	FundingCost string `json:"资金费成本"`
}

func getRedisDiff(rdb *redis.Client, table string, large_diff float64, negative bool, logger *slog.Logger) (largeSymbols map[string]DiffInfo) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	crossDiffs, err := rdb.HGetAll(ctx, table).Result()
	if err != nil {
		logger.Info("获取价差失败", "error", err)
		return largeSymbols
	}

	largeSymbols = make(map[string]DiffInfo)
	for symbol, infoJson := range crossDiffs {
		// 检查任务是否过期
		var info map[string]string
		json.Unmarshal([]byte(infoJson), &info)
		diff, _ := strconv.ParseFloat(info["diff_rate"], 64)
		fundCost, _ := strconv.ParseFloat(info["funding_cost"], 64)
		leftTs, _ := strconv.ParseInt(info["left_time"], 10, 64)
		rightTs, _ := strconv.ParseInt(info["right_time"], 10, 64)
		ts := min(leftTs, rightTs)
		// ms时间戳距离当前时间不超过10min
		if negative {
			if (time.Now().UnixMilli()-ts < 10*60*1000) && diff < -large_diff {
				// 保留6位小数
				largeSymbols[symbol] = DiffInfo{
					Diff:        strconv.FormatFloat(diff, 'f', 6, 64),
					FundingCost: strconv.FormatFloat(fundCost*-1, 'f', 6, 64),
				}
			}
			continue
		}
		if (time.Now().UnixMilli()-ts < 10*60*1000) && diff > large_diff {
			// 保留6位小数
			largeSymbols[symbol] = DiffInfo{
				Diff:        strconv.FormatFloat(diff, 'f', 6, 64),
				FundingCost: strconv.FormatFloat(fundCost, 'f', 6, 64),
			}
		}
	}
	return largeSymbols
}
