package main

import (
	config "GoTrader/internal"
	"GoTrader/internal/bn"
	"GoTrader/internal/ok"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/factory"
	"GoTrader/strategy"
	"GoTrader/strategy/arbit"
	"context"
	"io"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"runtime/debug"
	"syscall"

	"github.com/natefinch/lumberjack"
	"github.com/redis/go-redis/v9"
)

func main() {

	taskName := "BnCrossFundTest"

	okAccountName := "testLiquid"
	bnAccountName := "TestLiquidCostom"

	// 阈值
	keepAvailable := 5.0

	// 任务log
	taskLogger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	checkLogger := slog.New(slog.NewJSONHandler(
		io.Discard,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	// client logger
	clientPath := filepath.Join(config.TestLogDir, taskName, "client.log")
	clientFile := &lumberjack.Logger{
		Filename:   clientPath, // 日志文件的位置
		MaxSize:    10,         // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,          // 保留的最大旧文件数量
		MaxAge:     7,          // 保留旧文件的最大天数
		Compress:   true,       // 是否压缩/归档旧文件
		LocalTime:  true,       // 使用本地时间创建时间戳
	}
	defer clientFile.Close()
	multiWriter := io.MultiWriter(os.Stdout, clientFile)
	clientLogger := slog.New(slog.NewJSONHandler(multiWriter, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	defer func() {
		if r := recover(); r != nil {
			// 如果发生了 panic，打印堆栈信息
			clientLogger.Error("Recovered from panic:", "panic", r)
			clientLogger.Error(string(debug.Stack())) // 打印堆栈信息
		}
	}()

	rdb := redis.NewClient(&redis.Options{
		Addr:     config.TaskAddr,
		Password: config.TaskPassword,
		DB:       1,
	})

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	bnAccount := bn.BnAccounts[bnAccountName]
	okAccount := ok.OkAccounts[okAccountName]

	// 固定品种参数
	fixedSymbolParams := map[string]arbit.ArbitrageParams{
		"TEST_CROSS_FUND": {
			ID:              "TEST_CROSS_FUND",
			Symbol:          "TUSDT",
			Leverage:        10,
			AutoSeconds:     -1,
			LeftExchange:    "OK",
			RightExchange:   "BN",
			LeftType:        data.Futures,
			RightType:       data.Futures,
			OpenDiffLow:     -0.5,
			CloseDiff:       -100,
			OnceOpenValue:   20,
			MinAvailable:    keepAvailable,
			Status:          strategy.Reduce,
			CapitalLimitLow: 10,
			Extra: arbit.ExtraParams{
				RightOpenDiff:  -1.5,
				RightCloseDiff: -0.1,
			},
		},
	}

	// 策略表
	strategyMap := map[string]*arbit.CrossFundArbitrage{}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建 Broker 对象
	okb := broker.NewBroker(okAccount["API_KEY"], okAccount["API_SECRET"], okAccount["PASSPHRASE"], okAccount["DB_INDEX"], broker.Portfolio, nil)
	bnb := broker.NewBroker(bnAccount["API_KEY"], bnAccount["API_SECRET"], bnAccount["PASSPHRASE"], bnAccount["DB_INDEX"], broker.Costom, nil)

	// 创建客户端
	bndc, bnac, okdc, okac, err := factory.NewCrossClients(ctx, bnb, okb, true, clientLogger, checkLogger, checkLogger, checkLogger)

	if err != nil {
		clientLogger.Error("error connect cross client", "error", err)
		return
	}

	// 运行任务
	for ID, redisParam := range fixedSymbolParams {
		if _, ok := strategyMap[ID]; !ok {
			symbol := redisParam.Symbol
			// 创建 data
			okfd := data.NewSymbolData(symbol, data.Futures)
			bnfd := data.NewSymbolData(symbol, data.Futures)
			// 更新数据
			err := okac.UpdateDatas([]*data.SymbolData{okfd})
			if err != nil {
				taskLogger.Error("更新数据失败", "error", err)
				continue
			}
			err = bnac.UpdateDatas([]*data.SymbolData{bnfd})
			if err != nil {
				taskLogger.Error("更新数据失败", "error", err)
				continue
			}
			// 订阅数据
			err = okdc.SubscribeDatas([]*data.SymbolData{okfd})
			if err != nil {
				taskLogger.Error("订阅数据失败", "error", err)
				continue
			}
			err = bndc.SubscribeDatas([]*data.SymbolData{bnfd})
			if err != nil {
				taskLogger.Error("订阅数据失败", "error", err)
				continue
			}

			// 创建leg
			lg := &arbit.ArbitLeg{
				Client: okac,
				Broker: okb,
				Data:   okfd,
			}
			rg := &arbit.ArbitLeg{
				Client: bnac,
				Broker: bnb,
				Data:   bnfd,
			}

			// 策略logger
			straPath := filepath.Join(config.TestLogDir, taskName, ID+".log")
			straFile := &lumberjack.Logger{
				Filename:   straPath, // 日志文件的位置
				MaxSize:    10,       // 文件最大尺寸（以MB为单位）
				MaxBackups: 10,       // 保留的最大旧文件数量
				MaxAge:     7,        // 保留旧文件的最大天数
				Compress:   true,     // 是否压缩/归档旧文件
				LocalTime:  true,     // 使用本地时间创建时间戳
			}
			defer straFile.Close()
			straLogger := slog.New(slog.NewJSONHandler(io.MultiWriter(straFile, os.Stdout), &slog.HandlerOptions{
				Level: slog.LevelInfo,
			}))

			redisCtx := &strategy.RedisCtx{Client: rdb, Table: config.InfoTable}
			strategyMap[ID] = arbit.NewCrossFundArbitrage(lg, rg, redisParam, redisCtx, nil, straLogger, nil)
			strategyMap[ID].Start()
			taskLogger.Info("启动任务", slog.String("symbol", symbol), slog.String("params", redisParam.String()))
			continue
		}
	}

	// 结束进程
	<-signalChan
	taskLogger.Info("Received signal to exit")
	for _, stra := range strategyMap {
		stra.Pause()
	}
}
