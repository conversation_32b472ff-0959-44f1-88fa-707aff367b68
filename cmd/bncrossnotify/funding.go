package main

import (
	"context"
	"encoding/json"
	"log/slog"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

type FundingInfo struct {
	Cap      float64 `json:"上限"`
	Floor    float64 `json:"下限"`
	Interval int64   `json:"结算周期"`
}

func getRedisFunding(rdb *redis.Client, table string, logger *slog.Logger) (fundingSymbols map[string]FundingInfo) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	fundingInfo, err := rdb.HGetAll(ctx, table).Result()
	if err != nil {
		logger.Info("获取Bn资金费率失败", "error", err)
		return fundingSymbols
	}

	fundingSymbols = make(map[string]FundingInfo)
	for symbol, infoJson := range fundingInfo {
		// 检查任务是否过期
		var info struct {
			Cap      string `json:"cap"`
			Floor    string `json:"floor"`
			Interval int64  `json:"interval"`
		}
		json.Unmarshal([]byte(infoJson), &info)
		cap, _ := strconv.ParseFloat(info.Cap, 64)
		floor, _ := strconv.ParseFloat(info.Floor, 64)
		interval := info.Interval
		fundingSymbols[symbol] = FundingInfo{
			Cap:      cap,
			Floor:    floor,
			Interval: interval,
		}
	}
	return fundingSymbols
}
