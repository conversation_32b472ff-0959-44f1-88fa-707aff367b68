package main

import (
	config "GoTrader/internal"
	"GoTrader/pkg/logs"
	"io"
	"log/slog"
	"os"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

// 价差通知程序

func main() {
	// redis
	tokyoRdb := redis.NewClient(&redis.Options{
		Addr:     config.TokyoRedisAddr,
		Password: config.TokyoRedisPassword,
		DB:       config.TokyoDiffDB,
	})

	hkRdb := redis.NewClient(&redis.Options{
		Addr:     config.HKRedisAddr,
		Password: config.HKRedisPassword,
		DB:       config.HKDiffDB,
	})

	fundingRdb := redis.NewClient(&redis.Options{
		Addr:     config.TaskAddr,
		Password: config.TaskPassword,
		DB:       config.FundingDB,
	})

	feishuWriter := logs.NewFeishuWriter(
		[]string{},
		config.DiffNotify.Hook,
		config.DiffNotify.Secret,
		"币安跨所套利机会",
		slog.LevelInfo,
	)

	logger := slog.New(slog.NewJSONHandler(io.MultiWriter(os.Stdout, feishuWriter), nil))

	// 先执行一次
	queryDiff(tokyoRdb, hkRdb, fundingRdb, logger)
	// 10分钟执行一次
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		queryDiff(tokyoRdb, hkRdb, fundingRdb, logger)
	}
}
func queryDiff(tokyoRdb *redis.Client, hkRdb *redis.Client, fundingRdb *redis.Client, logger *slog.Logger) {
	// bn资金费信息
	bnFundingSymbols := getRedisFunding(fundingRdb, config.BnFundingInfo, logger)
	// ok资金费信息
	okFundingSymbols := getRedisFunding(fundingRdb, config.OkFundingInfo, logger)
	// bn-ok 跨所价差
	crossDiffSymbols := getRedisDiff(tokyoRdb, config.CrossDiffTable, logger)
	// bn 价差
	bnDiffSymbols := getRedisDiff(tokyoRdb, config.BnDiffTable, logger)
	// ok 价差
	okDiffSymbols := getRedisDiff(hkRdb, config.OkDiffTable, logger)

	for s, crossDiff := range crossDiffSymbols {
		if _, exists := bnFundingSymbols[s]; !exists {
			continue
		}
		bnFunding := bnFundingSymbols[s]
		if _, exists := okFundingSymbols[s]; !exists {
			continue
		}
		okFunding := okFundingSymbols[s]
		if _, exists := bnDiffSymbols[s]; !exists {
			continue
		}
		bnDiff := bnDiffSymbols[s]
		if _, exists := okDiffSymbols[s]; !exists {
			continue
		}
		okDiff := okDiffSymbols[s]

		if bnFunding.Floor/float64(bnFunding.Interval) < okFunding.Floor/float64(okFunding.Interval) &&
			bnDiff.IndexDiff < bnFunding.Floor*100 &&
			okDiff.IndexDiff < okFunding.Floor*100 &&
			crossDiff.Diff < 0.5 &&
			bnFunding.Interval > 1 {
			logger.Info("币安跨所套利开仓机会",
				"symbol", s,
				"bn-ok 跨所价差", strconv.FormatFloat(crossDiff.Diff, 'f', 3, 64),
				"bn期指价差", strconv.FormatFloat(bnDiff.IndexDiff, 'f', 3, 64),
				"ok期指价差", strconv.FormatFloat(okDiff.IndexDiff, 'f', 3, 64),
				"bn资金费下限", bnFunding.Floor,
				"bn资金费周期", bnFunding.Interval,
				"ok资金费下限", okFunding.Floor,
				"ok资金费周期", okFunding.Interval,
			)
		}
	}
}
