package main

import (
	"GoTrader/pkg/utils"
	"flag"
	"fmt"
	"log/slog"
	"os"
	"strings"
	"time"
)

func main() {
	// 定义命令行参数
	var (
		alertType    = flag.String("type", "1", "告警类型: 1=语音, 2=短信")
		users        = flag.String("users", "all", "接收用户: liquid,jiaoyin,tanli,small,007,all 或用逗号分隔的多个用户")
		content      = flag.String("content", "", "告警内容 (必填)")
		channel      = flag.String("channel", "warning", "通知频道: warning,error,strategy,arbitrage")
		tag          = flag.String("tag", "", "防重复标签 (可选)")
		tagTime      = flag.Int("tag-time", 3600, "标签有效期(秒), 默认3600秒")
		timeout      = flag.Int("timeout", 30, "请求超时时间(秒), 默认30秒")
		showHelp     = flag.Bool("help", false, "显示帮助信息")
		showExamples = flag.Bool("examples", false, "显示使用示例")
	)

	flag.Parse()

	// 显示帮助信息
	if *showHelp {
		printHelp()
		return
	}

	// 显示使用示例
	if *showExamples {
		printExamples()
		return
	}

	// 验证必填参数
	if *content == "" {
		fmt.Fprintf(os.Stderr, "错误: content 参数是必填的\n")
		fmt.Fprintf(os.Stderr, "使用 -help 查看帮助信息\n")
		os.Exit(1)
	}

	// 创建日志记录器
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	// 创建告警客户端
	alertClient := utils.NewAlertClientWithTimeout(time.Duration(*timeout)*time.Second, logger)

	// 解析用户列表
	userList := parseUsers(*users)

	// 解析告警类型
	var alertTypeEnum utils.AlertType
	switch *alertType {
	case "1":
		alertTypeEnum = utils.AlertTypeVoice
	case "2":
		alertTypeEnum = utils.AlertTypeSMS
	default:
		fmt.Fprintf(os.Stderr, "错误: 无效的告警类型 %s, 必须是 1 或 2\n", *alertType)
		os.Exit(1)
	}

	// 解析通知频道
	var channelEnum utils.AlertChannel
	switch *channel {
	case "warning":
		channelEnum = utils.ChannelWarning
	case "error":
		channelEnum = utils.ChannelError
	case "strategy":
		channelEnum = utils.ChannelStrategy
	case "arbitrage":
		channelEnum = utils.ChannelArbitrage
	default:
		fmt.Fprintf(os.Stderr, "错误: 无效的通知频道 %s\n", *channel)
		os.Exit(1)
	}

	// 构建告警请求
	req := &utils.AlertRequest{
		Type:    alertTypeEnum,
		User:    userList,
		Content: *content,
		Channel: channelEnum,
		Tag:     *tag,
		TagTime: *tagTime,
	}

	// 发送告警
	fmt.Printf("正在发送告警通知...\n")
	fmt.Printf("类型: %s, 用户: %s, 频道: %s\n", *alertType, userList, *channel)
	fmt.Printf("内容: %s\n", *content)
	if *tag != "" {
		fmt.Printf("标签: %s (有效期: %d秒)\n", *tag, *tagTime)
	}
	fmt.Println()

	resp, err := alertClient.SendAlert(req)
	if err != nil {
		fmt.Fprintf(os.Stderr, "发送告警失败: %v\n", err)
		os.Exit(1)
	}

	// 显示结果
	fmt.Printf("告警发送成功!\n")
	if resp.Success {
		fmt.Printf("响应: %s\n", resp.Message)
	} else {
		fmt.Printf("警告: 服务器返回非成功状态: %s\n", resp.Message)
	}
}

// parseUsers 解析用户列表
func parseUsers(users string) string {
	if users == "all" {
		return string(utils.UserAll)
	}

	// 分割用户列表
	userList := strings.Split(users, ",")
	var validUsers []string

	for _, user := range userList {
		user = strings.TrimSpace(user)
		switch user {
		case "liquid":
			validUsers = append(validUsers, string(utils.UserLiquid))
		case "jiaoyin":
			validUsers = append(validUsers, string(utils.UserJiaoyin))
		case "tanli":
			validUsers = append(validUsers, string(utils.UserTanli))
		case "small":
			validUsers = append(validUsers, string(utils.UserSmall))
		case "007":
			validUsers = append(validUsers, string(utils.User007))
		case "all":
			return string(utils.UserAll)
		default:
			fmt.Fprintf(os.Stderr, "警告: 未知用户 '%s', 将被忽略\n", user)
		}
	}

	if len(validUsers) == 0 {
		return string(utils.UserAll)
	}

	return strings.Join(validUsers, "_")
}

// printHelp 打印帮助信息
func printHelp() {
	fmt.Println("告警通知工具")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  alert [选项]")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  -type string")
	fmt.Println("        告警类型: 1=语音, 2=短信 (默认 \"1\")")
	fmt.Println("  -users string")
	fmt.Println("        接收用户: liquid,jiaoyin,tanli,small,007,all 或用逗号分隔的多个用户 (默认 \"all\")")
	fmt.Println("  -content string")
	fmt.Println("        告警内容 (必填)")
	fmt.Println("  -channel string")
	fmt.Println("        通知频道: warning,error,strategy,arbitrage (默认 \"warning\")")
	fmt.Println("  -tag string")
	fmt.Println("        防重复标签 (可选)")
	fmt.Println("  -tag-time int")
	fmt.Println("        标签有效期(秒) (默认 3600)")
	fmt.Println("  -timeout int")
	fmt.Println("        请求超时时间(秒) (默认 30)")
	fmt.Println("  -help")
	fmt.Println("        显示帮助信息")
	fmt.Println("  -examples")
	fmt.Println("        显示使用示例")
	fmt.Println()
	fmt.Println("频道说明:")
	fmt.Println("  warning   - 盈迪苟异常报警")
	fmt.Println("  error     - 零容忍")
	fmt.Println("  strategy  - A01-交易策略研发沟通")
	fmt.Println("  arbitrage - 量化｜套利机会通知群")
}

// printExamples 打印使用示例
func printExamples() {
	fmt.Println("使用示例:")
	fmt.Println()
	fmt.Println("1. 发送基本语音告警给所有用户:")
	fmt.Println("   alert -content \"系统检测到异常\"")
	fmt.Println()
	fmt.Println("2. 发送短信告警给特定用户:")
	fmt.Println("   alert -type 2 -users \"liquid,jiaoyin\" -content \"价格异常\" -channel error")
	fmt.Println()
	fmt.Println("3. 发送带防重复标签的告警:")
	fmt.Println("   alert -content \"连接断开\" -tag \"connection_lost\" -tag-time 300")
	fmt.Println()
	fmt.Println("4. 发送套利机会通知:")
	fmt.Println("   alert -users \"liquid,007\" -content \"发现套利机会\" -channel arbitrage")
	fmt.Println()
	fmt.Println("5. 发送策略状态更新:")
	fmt.Println("   alert -content \"策略部署完成\" -channel strategy")
	fmt.Println()
	fmt.Println("6. 发送紧急错误告警:")
	fmt.Println("   alert -type 1 -users all -content \"系统崩溃\" -channel error -timeout 60")
}
