package main

import (
	config "GoTrader/internal"
	"GoTrader/pkg/data"
	"GoTrader/pkg/factory"
	"GoTrader/pkg/utils"
	strategy "GoTrader/strategy/arbit"
	"context"
	"encoding/csv"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"github.com/natefinch/lumberjack"
)

func main() {

	taskName := "OkMarketDiff"

	logDir := config.LogDir

	SymbolTaskMap := map[string]*strategy.DiffStore{}

	// 任务log
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	// context
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 客户端
	dc, ac, err := factory.NewOkDataClients(ctx, false, logger, nil)

	if err != nil {
		logger.Error(err.Error())
		return
	}
	// signal
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	// 定时运行一次
	ticker := time.NewTicker(time.Second * 60)

	for {
		futuresSymbols := ac.QueryAllTradingFutures()
		spotSymbols := ac.QueryAllTradingMargin(nil)
		// 交集
		symbols := utils.Intersect(futuresSymbols, spotSymbols)
		// 转换为map
		symbolMap := utils.SliceToMap(symbols)

		logger.Info("symbols", "symbols", symbols)

		// 未存在任务的币种添加任务
		// 预备订阅的数据源
		subds := []*data.SymbolData{}
		for SymbolName, _ := range symbolMap {
			if _, ok := SymbolTaskMap[SymbolName]; !ok {
				// csvWriter
				csvPath := filepath.Join(logDir, taskName, SymbolName+".csv")
				csvFile := &lumberjack.Logger{
					Filename:   csvPath, // 文件的位置
					MaxSize:    10,      // 文件最大尺寸（以MB为单位）
					MaxBackups: 10,      // 保留的最大旧文件数量
					MaxAge:     7,       // 保留旧文件的最大天数
					Compress:   true,    // 是否压缩/归档旧文件
					LocalTime:  true,    // 使用本地时间创建时间戳
				}

				defer csvFile.Close()

				csvWriter := csv.NewWriter(csvFile)
				defer csvWriter.Flush()

				// 统一订阅数据
				fd := data.NewSymbolData(SymbolName, data.Futures)
				sd := data.NewSymbolData(SymbolName, data.Spot)

				subds = append(subds, fd, sd)
				// dc.SubscribeDatas([]*data.SymbolData{fd, sd})
				store := strategy.NewDiffStore(fd, sd, csvWriter, logger)
				logger.Info("Start task", "symbol", SymbolName)
				store.Start()

				SymbolTaskMap[SymbolName] = store
			}
		}

		// 订阅数据
		if len(subds) > 0 {
			dc.SubscribeDatas(subds)
		}

		// 任务中未存在的币种停止任务
		for SymbolName, store := range SymbolTaskMap {
			if _, ok := symbolMap[SymbolName]; !ok {
				logger.Info("Stop task", "symbol", SymbolName)
				store.Stop()
				delete(SymbolTaskMap, SymbolName)
			}
		}

		// 结束进程
		select {
		case <-signalChan:
			logger.Info("Received signal to exit")
			for _, stra := range SymbolTaskMap {
				if stra != nil {
					stra.Stop()
				}
			}
			return
		case <-ticker.C:
			continue
		}
	}
}
