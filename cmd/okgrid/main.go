package main

import (
	config "GoTrader/internal"
	"GoTrader/internal/ok"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/factory"
	"GoTrader/pkg/logs"
	"GoTrader/task/grid"
	"context"
	"io"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"runtime/debug"
	"syscall"
	"time"

	"github.com/natefinch/lumberjack"
	"github.com/redis/go-redis/v9"
)

func main() {

	taskName := "OkGrid"

	accountName := "grid"

	logDir := config.LogDir

	proxy := false

	// 任务log
	taskWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "task.log"), // 日志文件的位置
		MaxSize:    10,                                          // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                           // 保留的最大旧文件数量
		MaxAge:     3,                                           // 保留旧文件的最大天数
		Compress:   true,                                        // 是否压缩/归档旧文件
		LocalTime:  true,                                        // 使用本地时间创建时间戳
	}
	defer taskWriter.Close()
	taskLogger := slog.New(slog.NewJSONHandler(
		io.MultiWriter(taskWriter, os.Stdout),
		nil,
	))

	// api客户端log
	clientFile := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "client.log"), // 日志文件的位置
		MaxSize:    10,                                            // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                             // 保留的最大旧文件数量
		MaxAge:     7,                                             // 保留旧文件的最大天数
		Compress:   true,                                          // 是否压缩/归档旧文件
		LocalTime:  true,                                          // 使用本地时间创建时间戳
	}
	defer clientFile.Close()
	multiWriter := io.MultiWriter(os.Stdout, clientFile)
	clientLogger := slog.New(slog.NewJSONHandler(multiWriter, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	dataWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "data.log"), // 日志文件的位置
		MaxSize:    10,                                          // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                           // 保留的最大旧文件数量
		MaxAge:     3,                                           // 保留旧文件的最大天数
		Compress:   true,                                        // 是否压缩/归档旧文件
		LocalTime:  true,                                        // 使用本地时间创建时间戳
	}
	defer dataWriter.Close()
	dataLogger := slog.New(slog.NewJSONHandler(
		dataWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	brokerWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "broker.log"), // 日志文件的位置
		MaxSize:    10,                                            // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                             // 保留的最大旧文件数量
		MaxAge:     3,                                             // 保留旧文件的最大天数
		Compress:   true,                                          // 是否压缩/归档旧文件
		LocalTime:  true,                                          // 使用本地时间创建时间戳
	}
	defer brokerWriter.Close()
	brokerLogger := slog.New(slog.NewJSONHandler(
		brokerWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	feishuWriter := logs.NewFeishuWriter(
		[]string{config.UserLiquid},
		config.GroupLiquidTest.Hook,
		config.GroupLiquidTest.Secret,
		taskName,
		slog.LevelWarn,
	)

	defer func() {
		if r := recover(); r != nil {
			// 如果发生了 panic，打印堆栈信息
			clientLogger.Error("Recovered from panic:", "panic", r)
			clientLogger.Error(string(debug.Stack())) // 打印堆栈信息
		}
	}()

	rdb := redis.NewClient(&redis.Options{
		Addr:     config.TaskAddr,
		Password: config.TaskPassword,
		DB:       config.TaskDB,
	})

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	defer func() {
		if r := recover(); r != nil {
			// 如果发生了 panic，打印堆栈信息
			taskLogger.Error("Recovered from panic:", "panic", r)
			taskLogger.Error(string(debug.Stack())) // 打印堆栈信息
		}
	}()

	// 定时器
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	account := ok.OkAccounts[accountName]

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建 Broker 对象
	b := broker.NewBroker(account["API_KEY"], account["API_SECRET"], account["PASSPHRASE"], account["DB_INDEX"], broker.Costom, brokerLogger)

	// 创建客户端
	dc, ac, err := factory.NewOkClients(ctx, b, proxy, clientLogger, dataLogger)

	if err != nil {
		clientLogger.Error("error create client", "error", err)
		return
	}
	grid.RunGridTasks(
		ticker,
		signalChan,
		rdb,
		config.GridTaskTable,
		logDir,
		taskName,
		"OK",
		ac,
		dc,
		b,
		feishuWriter,
		taskLogger,
	)

	taskLogger.Info("grid task exit")
}
