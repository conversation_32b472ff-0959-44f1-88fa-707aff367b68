package main

import (
	config "GoTrader/internal"
	"GoTrader/pkg/logs"
	okUtils "GoTrader/pkg/utils/ok"
	"context"
	"encoding/json"
	"io"
	"log/slog"
	"math"
	"os"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

type RedisAccount struct {
	Name  string
	BnKey string
	OkKey string
}

type Position struct {
	Symbol   string
	Size     float64
	AvgPrice float64
}

type Asset struct {
	Symbol  string
	Balance float64
	Price   float64
}

type BnPosition struct {
	Symbol   string `json:"symbol"`
	Size     string `json:"positionAmt"`
	AvgPrice string `json:"entryPrice"`
}

type OkPosition struct {
	InstId   string  `json:"s"`
	Size     float64 `json:"paReal"`
	AvgPrice float64 `json:"avgPx"`
	PosSide  string  `json:"ps"`
}

type BnAsset struct {
	Coin    string `json:"coin"`
	Balance string `json:"balance"`
}

type OkAsset struct {
	Coin    string `json:"ccy"`
	Balance string `json:"cashBal"`
}

type BnAssetPrice struct {
	Coin  string `json:"coin"`
	Price string `json:"price"`
}

type OkAssetPrice struct {
	Coin  string `json:"ccy"`
	Price string `json:"cashBal"`
}

// 币安账户列表
var bnAccounts = []RedisAccount{
	{"币安套利", "account_61", "account_61"},
	{"币安资金费套利", "account_60", "account_60"},
}

// 欧意账户列表
var okAccounts = []RedisAccount{
	{"欧易山寨币套利", "account_68", "account_68"},
	{"欧易主流币套利", "account_69", "account_69"},
	{"欧易资金费套利", "account_72", "account_72"},
}

// 跨所账户列表
var crossAccounts = []RedisAccount{
	{"跨所套利(正向)", "account_67", "account_70"},
	{"跨所套利(反向)", "account_42", "account_71"},
}

// 平衡价值阈值
const BalanceThreshold = 40

func main() {

	// redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     config.TokyoRedisAddr,
		Password: config.TokyoRedisPassword,
		DB:       config.TokyoPositionDB,
	})

	feishuWriter := logs.NewFeishuWriter(
		[]string{config.UserLiquid},
		config.GroupLiquidTest.Hook,
		config.GroupLiquidTest.Secret,
		"仓位平衡通知",
		slog.LevelWarn,
	)
	logger := slog.New(slog.NewJSONHandler(io.MultiWriter(feishuWriter, os.Stdout), nil))

	// 先执行一次
	balanceCheck(rdb, logger)
	// 1分钟执行一次
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		balanceCheck(rdb, logger)
	}
}

func balanceCheck(rdb *redis.Client, logger *slog.Logger) {
	for _, account := range bnAccounts {
		bnPositions := getBnPosition(rdb, account, true, logger)
		bnUnifiedAssets := getBnAsset(rdb, account, logger)
		unbalanced := unifiedUnbalancedSymbol(bnUnifiedAssets, bnPositions)
		if len(unbalanced) != 0 {
			logger.Warn("币安仓位不平衡", "account", account.Name, "symbols", unbalanced)
		}
	}
	for _, account := range okAccounts {
		okPositions := getOkPosition(rdb, account, logger)
		okUnifiedAssets := getOkAsset(rdb, account, logger)
		unbalanced := unifiedUnbalancedSymbol(okUnifiedAssets, okPositions)
		if len(unbalanced) != 0 {
			logger.Warn("欧意仓位不平衡", "account", account.Name, "symbols", unbalanced)
		}
	}
	for _, account := range crossAccounts {
		leftPosMap := getBnPosition(rdb, account, false, logger)
		rightPosMap := getOkPosition(rdb, account, logger)
		unbalanced := crossUnbalancedSymbol(leftPosMap, rightPosMap)
		if len(unbalanced) != 0 {
			logger.Warn("跨所仓位不平衡", "account", account.Name, "symbols", unbalanced)
		}
	}
	logger.Info("检查完毕")
}

func getBnPosition(rdb *redis.Client, account RedisAccount, isUnified bool, logger *slog.Logger) map[string]Position {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	var redisKey string
	if isUnified {
		redisKey = config.BnUnifiedPosition
	} else {
		redisKey = config.BnPosition
	}
	bnPositionJson, err := rdb.HGet(ctx, redisKey, account.BnKey).Result()
	if err != nil {
		logger.Error("币安获取期货仓位失败", "error", err, "account", account.Name)
		return nil
	}
	var bnPositions []BnPosition
	err = json.Unmarshal([]byte(bnPositionJson), &bnPositions)
	if err != nil {
		logger.Error("币安解析期货仓位失败", "error", err)
		return nil
	}

	var posMap = make(map[string]Position)

	for _, position := range bnPositions {
		posObj := Position{}
		posObj.Symbol = position.Symbol
		posObj.Size, _ = strconv.ParseFloat(position.Size, 64)
		posObj.AvgPrice, _ = strconv.ParseFloat(position.AvgPrice, 64)
		posMap[posObj.Symbol] = posObj
	}
	return posMap
}

func getOkPosition(rdb *redis.Client, account RedisAccount, logger *slog.Logger) map[string]Position {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	okPositionJson, err := rdb.HGet(ctx, config.OkPosition, account.OkKey).Result()
	if err != nil {
		logger.Error("欧意获取期货仓位失败", "error", err, "account", account.Name)
		return nil
	}
	var okPositions []OkPosition
	err = json.Unmarshal([]byte(okPositionJson), &okPositions)
	if err != nil {
		logger.Error("欧意解析期货仓位失败", "error", err)
		return nil
	}

	var posMap = make(map[string]Position)

	for _, position := range okPositions {
		posObj := Position{}
		posObj.Symbol = okUtils.InstIdFuturesToSymbol(position.InstId)
		posObj.Size = position.Size
		posObj.AvgPrice = position.AvgPrice
		if position.PosSide == "SHORT" {
			posObj.Size *= -1
		}
		posMap[posObj.Symbol] = posObj
	}
	return posMap
}

func getBnAsset(rdb *redis.Client, account RedisAccount, logger *slog.Logger) map[string]Asset {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	bnAssetJson, err := rdb.HGet(ctx, config.BnBalance, account.BnKey).Result()
	cancel()
	if err != nil {
		logger.Error("币安获取资产失败", "error", err, "account", account.Name)
		return nil
	}
	var bnAsset []BnAsset
	err = json.Unmarshal([]byte(bnAssetJson), &bnAsset)
	if err != nil {
		logger.Error("币安解析资产失败", "error", err)
		return nil
	}

	var assetMap = make(map[string]Asset)

	for _, asset := range bnAsset {
		if asset.Coin == "USDT" || asset.Coin == "BNB" {
			continue
		}
		assetObj := Asset{}
		assetObj.Symbol = asset.Coin + "USDT"
		assetObj.Balance, _ = strconv.ParseFloat(asset.Balance, 64)
		// 查询价格
		ctx, cancel = context.WithTimeout(context.Background(), time.Second*10)
		priceString, err := rdb.HGet(ctx, config.BnAssetPrice, asset.Coin+"USDT").Result()
		cancel()
		if err != nil {
			logger.Error("币安获取资产价格失败", "error", err, "account", account.Name, "symbol", asset.Coin+"USDT")
			return nil
		}
		assetObj.Price, _ = strconv.ParseFloat(priceString, 64)
		assetMap[assetObj.Symbol] = assetObj
	}
	return assetMap
}

func getOkAsset(rdb *redis.Client, account RedisAccount, logger *slog.Logger) map[string]Asset {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	okAssetJson, err := rdb.HGet(ctx, config.OkBalance, account.OkKey).Result()
	if err != nil {
		logger.Error("欧意获取资产失败", "error", err, "account", account.Name)
		return nil
	}
	var okAsset []OkAsset
	err = json.Unmarshal([]byte(okAssetJson), &okAsset)
	if err != nil {
		logger.Error("欧意解析资产失败", "error", err)
		return nil
	}
	var assetMap = make(map[string]Asset)

	for _, asset := range okAsset {
		if asset.Coin == "USDT" || asset.Coin == "BNB" {
			continue
		}
		assetObj := Asset{}
		assetObj.Symbol = asset.Coin + "USDT"
		assetObj.Balance, _ = strconv.ParseFloat(asset.Balance, 64)
		// 查询价格
		ctx, cancel = context.WithTimeout(context.Background(), time.Second*10)
		priceString, err := rdb.HGet(ctx, config.OkAssetPrice, asset.Coin+"-USDT").Result()
		cancel()
		if err != nil {
			logger.Error("欧意获取资产价格失败", "error", err, "account", account.Name, "symbol", asset.Coin+"-USDT")
			return nil
		}
		assetObj.Price, _ = strconv.ParseFloat(priceString, 64)
		assetMap[assetObj.Symbol] = assetObj
	}
	return assetMap
}

func crossUnbalancedSymbol(leftPosMap map[string]Position, rightPosMap map[string]Position) map[string]map[string]float64 {
	unbalanced := make(map[string]map[string]float64)
	keys := UnionKeys(leftPosMap, rightPosMap)
	for _, key := range keys {
		leftPos := leftPosMap[key]
		rightPos := rightPosMap[key]
		// 超过2%
		if leftPos.Size*leftPos.AvgPrice+rightPos.Size*rightPos.AvgPrice > math.Abs(rightPos.Size*rightPos.AvgPrice)*0.2 &&
			leftPos.Size+rightPos.Size > max(rightPos.Size, leftPos.Size)*0.2 {
			unbalanced[key] = map[string]float64{"left": leftPos.Size, "right": rightPos.Size}
		}
	}
	return unbalanced
}

func unifiedUnbalancedSymbol(assetMap map[string]Asset, posMap map[string]Position) map[string]map[string]float64 {
	unbalanced := make(map[string]map[string]float64)
	keys := UnionKeys(assetMap, posMap)
	for _, key := range keys {
		asset := assetMap[key]
		pos := posMap[key]
		// 超过20U
		if asset.Balance*asset.Price+pos.AvgPrice*pos.Size > BalanceThreshold &&
			asset.Balance+pos.Size > max(asset.Balance, math.Abs(pos.Size))*0.2 {
			unbalanced[key] = map[string]float64{"asset": asset.Balance * asset.Price, "pos": pos.AvgPrice * pos.Size}
		}
	}
	return unbalanced
}

func UnionKeys[K comparable, V1 any, V2 any](m1 map[K]V1, m2 map[K]V2) []K {
	union := make(map[K]struct{})
	for k := range m1 {
		union[k] = struct{}{}
	}
	for k := range m2 {
		union[k] = struct{}{}
	}
	keys := make([]K, 0, len(union))
	for k := range union {
		keys = append(keys, k)
	}
	return keys
}
