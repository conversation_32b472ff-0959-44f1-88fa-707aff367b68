package main

import (
	"context"
	"io"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"runtime/debug"
	"syscall"
	"time"

	config "GoTrader/internal"
	"GoTrader/internal/bn"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/factory"
	"GoTrader/pkg/logs"
	task "GoTrader/task/arbit"

	"github.com/natefinch/lumberjack"
	"github.com/redis/go-redis/v9"
)

func main() {

	taskName := "BnArbit"

	accountName := "Arbit"

	// 阈值
	keepAvailable := 150.0

	logDir := config.LogDir

	// 代理
	proxy := false

	// 检查logger
	marketWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "market.log"), // 日志文件的位置
		MaxSize:    10,                                            // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                             // 保留的最大旧文件数量
		MaxAge:     3,                                             // 保留旧文件的最大天数
		Compress:   true,                                          // 是否压缩/归档旧文件
		LocalTime:  true,                                          // 使用本地时间创建时间戳
	}
	marketLogger := slog.New(slog.NewJSONHandler(
		marketWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	dataWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "data.log"), // 日志文件的位置
		MaxSize:    10,                                          // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                           // 保留的最大旧文件数量
		MaxAge:     3,                                           // 保留旧文件的最大天数
		Compress:   true,                                        // 是否压缩/归档旧文件
		LocalTime:  true,                                        // 使用本地时间创建时间戳
	}
	dataLogger := slog.New(slog.NewJSONHandler(
		dataWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	brokerWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "broker.log"), // 日志文件的位置
		MaxSize:    10,                                            // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                             // 保留的最大旧文件数量
		MaxAge:     3,                                             // 保留旧文件的最大天数
		Compress:   true,                                          // 是否压缩/归档旧文件
		LocalTime:  true,                                          // 使用本地时间创建时间戳
	}
	brokerLogger := slog.New(slog.NewJSONHandler(
		brokerWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	clientPath := filepath.Join(logDir, taskName, "client.log")
	clientFileWriter := &lumberjack.Logger{
		Filename:   clientPath, // 日志文件的位置
		MaxSize:    10,         // 文件最大尺寸（以MB为单位）
		MaxBackups: 10,         // 保留的最大旧文件数量
		MaxAge:     7,          // 保留旧文件的最大天数
		Compress:   true,       // 是否压缩/归档旧文件
		LocalTime:  true,       // 使用本地时间创建时间戳
	}
	defer clientFileWriter.Close()

	// 个人测试飞书
	feishuWriter := logs.NewFeishuWriter(
		[]string{config.UserLiquid},
		config.GroupLiquidTest.Hook,
		config.GroupLiquidTest.Secret,
		taskName,
		slog.LevelWarn,
	)

	// 通知飞书
	notifyFeishuWriter := logs.NewFeishuWriter(
		[]string{},
		config.GroupArbit.Hook,
		config.GroupArbit.Secret,
		taskName,
		slog.LevelWarn,
	)

	taskWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "task.log"), // 日志文件的位置
		MaxSize:    10,                                          // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                           // 保留的最大旧文件数量
		MaxAge:     3,                                           // 保留旧文件的最大天数
		Compress:   true,                                        // 是否压缩/归档旧文件
		LocalTime:  true,                                        // 使用本地时间创建时间戳
	}

	// 任务log
	taskLogger := slog.New(slog.NewJSONHandler(
		io.MultiWriter(taskWriter, feishuWriter, os.Stdout),
		nil,
	))

	// 通知log
	notifyLogger := slog.New(slog.NewJSONHandler(
		io.MultiWriter(clientFileWriter, notifyFeishuWriter, os.Stdout),
		nil,
	))

	// client logger
	clientLogger := slog.New(slog.NewJSONHandler(
		io.MultiWriter(clientFileWriter, feishuWriter, os.Stdout),
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	// 取消信号接收channel
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	// 定时器
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	// ctx
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     config.TaskAddr,
		Password: config.TaskPassword,
		DB:       config.TaskDB,
	})

	defer func() {
		if r := recover(); r != nil {
			// 如果发生了 panic，打印堆栈信息
			taskLogger.Error("Recovered from panic:", "panic", r)
			taskLogger.Error(string(debug.Stack())) // 打印堆栈信息
		}
	}()

	// 帐号
	account := bn.BnAccounts[accountName]

	// 创建 Broker 对象
	b := broker.NewBroker(account["API_KEY"], account["API_SECRET"], account["PASSPHRASE"], account["DB_INDEX"], broker.Portfolio, brokerLogger)

	// 创建客户端
	dc, ac, err := factory.NewBnClients(ctx, b, proxy, clientLogger, dataLogger, marketLogger)

	if err != nil {
		taskLogger.Error("NewBnClients failed", "error", err)
		return
	}
	task.RunArbitTasks(
		0.3,
		ticker,
		signalChan,
		rdb,
		config.ArbitTaskTable,
		logDir,
		taskName,
		"BN",
		"BN",
		keepAvailable,
		ac,
		dc,
		b,
		feishuWriter,
		taskLogger,
		notifyLogger,
	)

	taskLogger.Info("bnarbit task exit")
}
