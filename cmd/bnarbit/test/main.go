package main

import (
	config "GoTrader/internal"
	"GoTrader/internal/bn"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/factory"
	"GoTrader/strategy"
	"GoTrader/strategy/arbit"
	"context"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"syscall"

	"github.com/natefinch/lumberjack"
)

func main() {
	go func() {
		runtime.SetMutexProfileFraction(1)
		fmt.Println(http.ListenAndServe("localhost:6060", nil))
	}()

	taskName := "BnSingle"

	accountName := "TestLiquid"

	// 任务log
	taskLogger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	// client logger
	clientPath := filepath.Join(config.TestLogDir, taskName, "client.log")
	clientFile := &lumberjack.Logger{
		Filename:   clientPath, // 日志文件的位置
		MaxSize:    10,         // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,          // 保留的最大旧文件数量
		MaxAge:     7,          // 保留旧文件的最大天数
		Compress:   true,       // 是否压缩/归档旧文件
		LocalTime:  true,       // 使用本地时间创建时间戳
	}
	defer clientFile.Close()
	// feishuWriter := logs.NewFeishuWriter(
	// 	[]string{config.UserLiquid},
	// 	config.GroupLiquidTest.Hook,
	// 	config.GroupLiquidTest.Secret,
	// 	taskName,
	// 	slog.LevelWarn,
	// )
	multiWriter := io.MultiWriter(os.Stdout, clientFile)
	clientLogger := slog.New(slog.NewJSONHandler(multiWriter, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	defer func() {
		if r := recover(); r != nil {
			// 如果发生了 panic，打印堆栈信息
			clientLogger.Error("Recovered from panic:", "panic", r)
			clientLogger.Error(string(debug.Stack())) // 打印堆栈信息
		}
	}()

	// 阈值
	keepAvailable := 50.0

	account := bn.BnAccounts[accountName]

	// 固定品种参数
	fixedSymbolParams := map[string]arbit.ArbitrageParams{
		"TEST_BN": {
			ID:             "TEST_BN",
			Symbol:         "CETUSUSDT",
			AutoSeconds:    30,
			LeftType:       data.Futures,
			RightType:      data.Spot,
			ProfitSpaceLow: 0.02,
			OpenDiffLow:    1,
			CloseDiff:      -1,
			OnceOpenValue:  20,
			MinAvailable:   keepAvailable,
			Status:         strategy.Active,
		},
	}

	// 策略表
	strategyMap := map[string]*arbit.Arbitrage{}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	// 创建 Broker 对象
	b := broker.NewBroker(account["API_KEY"], account["API_SECRET"], account["PASSPHRASE"], account["DB_INDEX"], broker.Portfolio, nil)

	// 创建客户端
	dc, ac, err := factory.NewBnClients(ctx, b, true, clientLogger, nil, nil)

	if err != nil {
		clientLogger.Error("error connect client", "error", err)
		return
	}

	// 运行任务
	for ID, params := range fixedSymbolParams {
		if _, ok := strategyMap[ID]; !ok {
			symbol := params.Symbol
			// 创建 TradeData 对象
			ld := data.NewSymbolData(symbol, params.LeftType)
			rd := data.NewSymbolData(symbol, params.RightType)

			dc.SubscribeDatas([]*data.SymbolData{ld, rd})
			ac.UpdateDatas([]*data.SymbolData{ld, rd})

			// 策略logger
			straPath := filepath.Join(config.TestLogDir, taskName, ID+".log")
			straFile := &lumberjack.Logger{
				Filename:   straPath, // 日志文件的位置
				MaxSize:    10,       // 文件最大尺寸（以MB为单位）
				MaxBackups: 3,        // 保留的最大旧文件数量
				MaxAge:     7,        // 保留旧文件的最大天数
				Compress:   true,     // 是否压缩/归档旧文件
				LocalTime:  true,     // 使用本地时间创建时间戳
			}
			defer straFile.Close()
			straLogger := slog.New(slog.NewJSONHandler(io.MultiWriter(os.Stdout, straFile), &slog.HandlerOptions{
				Level: slog.LevelInfo,
			}))

			lg := &arbit.ArbitLeg{
				Client: ac,
				Broker: b,
				Data:   ld,
			}

			rg := &arbit.ArbitLeg{
				Client: ac,
				Broker: b,
				Data:   rd,
			}

			strategyMap[ID] = arbit.NewArbitrage(lg, rg, params, nil, nil, straLogger, nil)
			strategyMap[ID].Start()
			taskLogger.Info("启动任务", slog.String("symbol", symbol), slog.String("params", params.String()))
			continue
		}
	}

	// 结束进程
	<-signalChan
	taskLogger.Info("Received signal to exit")
	for _, stra := range strategyMap {
		stra.Pause()
	}
}
