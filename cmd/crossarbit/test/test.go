package main

import (
	config "GoTrader/internal"
	"GoTrader/internal/bn"
	"GoTrader/internal/ok"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/factory"
	"GoTrader/strategy"
	"GoTrader/strategy/arbit"
	"context"
	"io"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"runtime/debug"
	"syscall"

	"github.com/natefinch/lumberjack"
	"github.com/redis/go-redis/v9"

	_ "net/http/pprof"
)

func main() {

	taskName := "CrossSingle"

	bnAccountName := "TestLiquidCostom"
	okAccountName := "testLiquid"

	// 阈值
	keepAvailable := 5.0

	// 任务log
	taskLogger := slog.New(slog.NewJSONHandler(os.Stdout, nil))

	checkLogger := slog.New(slog.NewJSONHandler(
		io.Discard,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	// client logger
	clientPath := filepath.Join(config.TestLogDir, taskName, "client.log")
	clientFile := &lumberjack.Logger{
		Filename:   clientPath, // 日志文件的位置
		MaxSize:    10,         // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,          // 保留的最大旧文件数量
		MaxAge:     7,          // 保留旧文件的最大天数
		Compress:   true,       // 是否压缩/归档旧文件
		LocalTime:  true,       // 使用本地时间创建时间戳
	}
	defer clientFile.Close()
	multiWriter := io.MultiWriter(os.Stdout, clientFile)
	clientLogger := slog.New(slog.NewJSONHandler(multiWriter, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	defer func() {
		if r := recover(); r != nil {
			// 如果发生了 panic，打印堆栈信息
			clientLogger.Error("Recovered from panic:", "panic", r)
			clientLogger.Error(string(debug.Stack())) // 打印堆栈信息
		}
	}()

	rdb := redis.NewClient(&redis.Options{
		Addr:     config.TaskAddr,
		Password: config.TaskPassword,
		DB:       1,
	})

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	bnAccount := bn.BnAccounts[bnAccountName]
	okAccount := ok.OkAccounts[okAccountName]

	// 固定品种参数
	fixedSymbolParams := map[string]arbit.ArbitrageParams{
		"TEST_CROSS": {
			ID:               "TEST_CROSS",
			Symbol:           "DOGEUSDT",
			Leverage:         10,
			AutoSeconds:      -1,
			LeftExchange:     "OK",
			RightExchange:    "BN",
			LeftType:         data.Futures,
			RightType:        data.Futures,
			ProfitSpaceLow:   0.5,
			ProfitSpaceHigh:  2.5,
			OpenDiffLow:      -10,
			OpenDiffHigh:     -5,
			CloseDiff:        -20,
			OnceOpenValue:    20,
			MinAvailable:     keepAvailable,
			Status:           strategy.Active,
			CapitalLimitLow:  300,
			CapitalLimitHigh: 1000,
		},
	}

	// 策略表
	strategyMap := map[string]*arbit.Arbitrage{}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建 Broker 对象
	okb := broker.NewBroker(okAccount["API_KEY"], okAccount["API_SECRET"], okAccount["PASSPHRASE"], okAccount["DB_INDEX"], broker.Portfolio, nil)
	bnb := broker.NewBroker(bnAccount["API_KEY"], bnAccount["API_SECRET"], bnAccount["PASSPHRASE"], bnAccount["DB_INDEX"], broker.Costom, nil)

	// 创建客户端
	bndc, bnac, okdc, okac, err := factory.NewCrossClients(ctx, bnb, okb, true, clientLogger, checkLogger, checkLogger, checkLogger)

	if err != nil {
		clientLogger.Error("error connect client", "error", err)
		return
	}

	// 运行任务
	for ID, params := range fixedSymbolParams {
		if _, ok := strategyMap[ID]; !ok {
			symbol := params.Symbol
			// 创建 TradeData 对象
			ld := data.NewSymbolData(symbol, params.LeftType)
			rd := data.NewSymbolData(symbol, params.RightType)

			var lg, rg *arbit.ArbitLeg

			// 添加至客户端
			if params.LeftExchange == "BN" && params.RightExchange == "OK" {
				bnac.UpdateDatas([]*data.SymbolData{ld})
				bndc.SubscribeDatas([]*data.SymbolData{ld})
				okac.UpdateDatas([]*data.SymbolData{rd})
				okdc.SubscribeDatas([]*data.SymbolData{rd})

				lg = &arbit.ArbitLeg{
					Client: bnac,
					Broker: bnb,
					Data:   ld,
				}
				rg = &arbit.ArbitLeg{
					Client: okac,
					Broker: okb,
					Data:   rd,
				}
			}
			if params.LeftExchange == "OK" && params.RightExchange == "BN" {
				okac.UpdateDatas([]*data.SymbolData{ld})
				okdc.SubscribeDatas([]*data.SymbolData{ld})
				bnac.UpdateDatas([]*data.SymbolData{rd})
				bndc.SubscribeDatas([]*data.SymbolData{rd})

				lg = &arbit.ArbitLeg{
					Client: okac,
					Broker: okb,
					Data:   ld,
				}
				rg = &arbit.ArbitLeg{
					Client: bnac,
					Broker: bnb,
					Data:   rd,
				}
			}

			// 策略logger
			straPath := filepath.Join(config.TestLogDir, taskName, ID+".log")
			straFile := &lumberjack.Logger{
				Filename:   straPath, // 日志文件的位置
				MaxSize:    10,       // 文件最大尺寸（以MB为单位）
				MaxBackups: 3,        // 保留的最大旧文件数量
				MaxAge:     7,        // 保留旧文件的最大天数
				Compress:   true,     // 是否压缩/归档旧文件
				LocalTime:  true,     // 使用本地时间创建时间戳
			}
			defer straFile.Close()
			straLogger := slog.New(slog.NewJSONHandler(io.MultiWriter(straFile, os.Stdout), &slog.HandlerOptions{
				Level: slog.LevelInfo,
			}))

			strategyMap[ID] = arbit.NewArbitrage(lg, rg, params, &strategy.RedisCtx{Client: rdb, Table: config.InfoTable}, nil, straLogger, nil)
			strategyMap[ID].Start()
			taskLogger.Info("启动任务", slog.String("symbol", symbol), slog.String("params", params.String()))
			continue
		}
	}

	// 结束进程
	<-signalChan
	taskLogger.Info("Received signal to exit")
	for _, stra := range strategyMap {
		stra.Pause()
	}
}
