package main

import (
	config "GoTrader/internal"
	"GoTrader/internal/ok"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/factory"
	"GoTrader/pkg/logs"
	"GoTrader/strategy"
	"GoTrader/strategy/arbit"
	"context"
	"io"
	"log/slog"
	"os"
	"os/signal"
	"path/filepath"
	"runtime/debug"
	"syscall"

	"github.com/natefinch/lumberjack"
	"github.com/redis/go-redis/v9"
)

func main() {

	taskName := "OkQuote"

	accountName := "btcOnly"

	logDir := config.LogDir

	// 检查logger
	checkWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "market.log"), // 日志文件的位置
		MaxSize:    10,                                            // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                             // 保留的最大旧文件数量
		MaxAge:     3,                                             // 保留旧文件的最大天数
		Compress:   true,                                          // 是否压缩/归档旧文件
		LocalTime:  true,                                          // 使用本地时间创建时间戳
	}
	checkLogger := slog.New(slog.NewJSONHandler(
		checkWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	brokerWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "broker.log"), // 日志文件的位置
		MaxSize:    10,                                            // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                             // 保留的最大旧文件数量
		MaxAge:     3,                                             // 保留旧文件的最大天数
		Compress:   true,                                          // 是否压缩/归档旧文件
		LocalTime:  true,                                          // 使用本地时间创建时间戳
	}
	brokerLogger := slog.New(slog.NewJSONHandler(
		brokerWriter,
		&slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))

	clientPath := filepath.Join(logDir, taskName, "client.log")
	clientFile := &lumberjack.Logger{
		Filename:   clientPath, // 日志文件的位置
		MaxSize:    10,         // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,          // 保留的最大旧文件数量
		MaxAge:     7,          // 保留旧文件的最大天数
		Compress:   true,       // 是否压缩/归档旧文件
		LocalTime:  true,       // 使用本地时间创建时间戳
	}
	defer clientFile.Close()
	feishuWriter := logs.NewFeishuWriter(
		[]string{config.UserLiquid},
		config.GroupLiquidTest.Hook,
		config.GroupLiquidTest.Secret,
		taskName,
		slog.LevelWarn,
	)
	multiWriter := io.MultiWriter(os.Stdout, feishuWriter, clientFile)

	// client logger
	clientLogger := slog.New(slog.NewJSONHandler(multiWriter, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	taskWriter := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, taskName, "task.log"), // 日志文件的位置
		MaxSize:    10,                                          // 文件最大尺寸（以MB为单位）
		MaxBackups: 3,                                           // 保留的最大旧文件数量
		MaxAge:     3,                                           // 保留旧文件的最大天数
		Compress:   true,                                        // 是否压缩/归档旧文件
		LocalTime:  true,                                        // 使用本地时间创建时间戳
	}

	// 任务log
	taskLogger := slog.New(slog.NewJSONHandler(
		io.MultiWriter(taskWriter, feishuWriter, os.Stdout),
		nil,
	))

	// redis
	rdb := redis.NewClient(&redis.Options{
		Addr:     config.TaskAddr,
		Password: config.TaskPassword,
		DB:       config.TaskDB,
	})

	defer func() {
		if r := recover(); r != nil {
			// 如果发生了 panic，打印堆栈信息
			clientLogger.Error("Recovered from panic:", "panic", r, "stack", string(debug.Stack()))
		}
	}()

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)

	// 阈值
	keepAvailable := 500.0

	account := ok.OkAccounts[accountName]

	// 固定品种参数
	fixedSymbolParams := map[string]arbit.ArbitrageParams{
		"OK_BTC_QUOTE": {
			ID:             "OK_BTC_QUOTE",
			Symbol:         "BTCUSDT",
			AutoSeconds:    30,
			LeftType:       data.Futures,
			RightType:      data.Spot,
			ProfitSpaceLow: 0.2,
			OpenDiffLow:    0.1,
			CloseDiff:      -0.1,
			OnceOpenValue:  20,
			MinAvailable:   keepAvailable,
			Status:         strategy.Active,
		},
		"OK_ETH_QUOTE": {
			ID:             "OK_ETH_QUOTE",
			Symbol:         "ETHUSDT",
			AutoSeconds:    30,
			LeftType:       data.Futures,
			RightType:      data.Spot,
			ProfitSpaceLow: 0.2,
			OpenDiffLow:    0.1,
			CloseDiff:      -0.1,
			OnceOpenValue:  20,
			MinAvailable:   keepAvailable,
			Status:         strategy.Active,
		},
		"OK_BNB_QUOTE": {
			ID:             "OK_BNB_QUOTE",
			Symbol:         "BNBUSDT",
			AutoSeconds:    30,
			LeftType:       data.Futures,
			RightType:      data.Spot,
			ProfitSpaceLow: 0.2,
			OpenDiffLow:    0.1,
			CloseDiff:      -0.1,
			OnceOpenValue:  20,
			MinAvailable:   keepAvailable,
			Status:         strategy.Active,
		},
	}

	// 策略表
	strategyMap := map[string]*arbit.Arbitrage{}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建 Broker 对象
	b := broker.NewBroker(account["API_KEY"], account["API_SECRET"], account["PASSPHRASE"], account["DB_INDEX"], broker.Portfolio, brokerLogger)

	// 创建客户端
	dc, ac, err := factory.NewOkClients(ctx, b, false, clientLogger, checkLogger)

	if err != nil {
		clientLogger.Error("error connect client", "error", err)
		return
	}

	// 运行任务
	for ID, params := range fixedSymbolParams {
		if _, ok := strategyMap[ID]; !ok {
			symbol := params.Symbol
			// 创建 TradeData 对象
			ld := data.NewSymbolData(symbol, params.LeftType)
			rd := data.NewSymbolData(symbol, params.RightType)

			// 添加至客户端
			for _, d := range []*data.SymbolData{ld, rd} {
				if d.DataType == data.Futures {
					ac.UpdateFuturesDatas([]*data.SymbolData{d})
				}
				if d.DataType == data.Spot {
					ac.UpdateSpotDatas([]*data.SymbolData{d})
				}
				dc.SubscribeDatas([]*data.SymbolData{d})
			}

			// 策略logger
			straPath := filepath.Join(logDir, taskName, ID+".log")
			straFile := &lumberjack.Logger{
				Filename:   straPath, // 日志文件的位置
				MaxSize:    10,       // 文件最大尺寸（以MB为单位）
				MaxBackups: 10,       // 保留的最大旧文件数量
				MaxAge:     7,        // 保留旧文件的最大天数
				Compress:   true,     // 是否压缩/归档旧文件
				LocalTime:  true,     // 使用本地时间创建时间戳
			}
			defer straFile.Close()
			straLogger := slog.New(slog.NewJSONHandler(io.MultiWriter(straFile, feishuWriter), &slog.HandlerOptions{
				Level: slog.LevelInfo,
			}))

			lg := &arbit.ArbitLeg{
				Client: ac,
				Broker: b,
				Data:   ld,
			}

			rg := &arbit.ArbitLeg{
				Client: ac,
				Broker: b,
				Data:   rd,
			}

			redisParams := &strategy.RedisCtx{Client: rdb, Table: config.InfoTable}
			strategyMap[ID] = arbit.NewArbitrage(lg, rg, params, redisParams, nil, straLogger, nil)
			strategyMap[ID].Start()
			taskLogger.Info("启动任务", slog.String("symbol", symbol), slog.String("params", params.String()))
			continue
		}
	}

	// 结束进程
	<-signalChan
	taskLogger.Info("Received signal to exit")
	for _, stra := range strategyMap {
		stra.Pause()
	}
}
