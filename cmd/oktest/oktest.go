package main

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/factory"
	"GoTrader/pkg/utils"
	"context"
	"fmt"
	"time"
)

func main() {

	SymbolName := "BNBUSDT"

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建 Broker 对象, test
	b := broker.NewBroker(
		"1e9d0a16-06e3-4cb5-b36a-22f809a74b64",
		"C2615EDBCD958D7D48EA763A00BA28B2",
		"!@Name4tst",
		"testok",
		broker.Costom,
		nil,
	)

	dc, ac, err := factory.NewOkClients(ctx, b, true, utils.StdoutLogger, utils.StdoutLogger)
	if err != nil {
		fmt.Println(err.Error())
	}

	fd := data.NewSymbolData(SymbolName, data.Futures)
	sd := data.NewSymbolData(SymbolName, data.Spot)

	ac.UpdateFuturesLeverageSingle(b, fd)

	dc.SubscribeDatas([]*data.SymbolData{fd, sd})
	ac.UpdateDatas([]*data.SymbolData{fd, sd})
	ac.UpdateBrokerUMPending(b, []*data.SymbolData{fd})
	ac.UpdateBrokerMarginPending(b, []*data.SymbolData{sd})
	ac.UpdatePositionTiers(b, fd)

	ac.ModifyFuturesLeverage(b, fd, 5)

	// o := order.Order{
	// 	ClOrdId:     "test",
	// 	Symbol:      SymbolName,
	// 	Side:        order.BUY,
	// 	OrdType:     order.LIMIT,
	// 	TimeInForce: order.GTC,
	// 	Price:       0.23,
	// 	Size:        20,
	// }
	// o := order.CreateOrder().SetSymbol(SymbolName).SetClOrdId("test").SetPrice(0.23).
	// 	SetSize(0.02).SetSide(order.BUY).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)

	// fo := order.CreateFuturesOrderFromCommon(o, order.LONG)
	// _, err = ac.SubmitFuturesOrder(b, fo, fd.Info)

	// if err != nil {
	// 	fmt.Println(err.Error())
	// }

	// go func() {
	// 	time.Sleep(11 * time.Second)
	// 	ac.CancelFuturesOrder(b, fo)
	// 	// mo := fo.Copy()
	// 	// mo.SetPrice(0.231).SetSize(50)
	// 	// err = ac.ModifyFuturesOrder(b, mo, fd.Info)
	// 	// if err != nil {
	// 	// 	fmt.Println(err.Error())
	// 	// 	return
	// 	// }
	// }()

	// openInt, _ := ac.QueryOpenInterest(fd)

	// accountIfo, _ := ac.QueryAccount(b)
	// fmt.Println(accountIfo)

	// 定时打印broker
	ticker := time.NewTicker(2 * time.Second)
	for range ticker.C {
		// fmt.Printf("fd max price %f, min price %f \n", fd.Info.MaxPrice, fd.Info.MinPrice)
		// fmt.Printf("fd max price percent %f, min price percent %f \n", fd.Info.MaxPricePercent, fd.Info.MinPricePercent)
		// fmt.Printf("fd min order price %f, max order price %f \n", fd.MinOrderPriceStrict(), fd.MaxOrderPriceStrict())
		// fmt.Printf("sd max price %f, min price %f \n", sd.Info.MaxPrice, sd.Info.MinPrice)
		// fmt.Printf("sd max price percent %f, min price percent %f \n", sd.Info.MaxPricePercent, sd.Info.MinPricePercent)
		// fmt.Printf("sd min order price %f, max order price %f \n", sd.MinOrderPriceStrict(), sd.MaxOrderPriceStrict())
		// fmt.Printf("open interest %f \n", openInt)
		// fmt.Printf("futures data info %+v \n", fd.Info)
		// fmt.Printf("spot data info %+v \n", sd.Info)
		// fmt.Printf("futures data fund period %v, funding rate %f,  max funding rate %f, min funding rate %f \n",
		// 	fd.FundingPeriod(), fd.Funding.Rate, b.FundingCap(fd), b.FundingFloor(fd))
		// fmt.Printf("index price %f, price %f \n", fd.Index.Last(), fd.Bid1())
		// fmt.Printf("spot data fund period %v ", sd.FundingPeriod())
		// fmt.Printf("futures Commission %+v \n", b.FuturesCommission)
		// fmt.Printf("spot Commission %+v \n", b.SpotCommission)
		// fmt.Printf("futures Available %f \n", b.GetAvailable(fd))
		// fmt.Printf("spot Available %f \n", b.GetAvailable(sd))
		// fmt.Printf("Account %+v \n", b.Account)
		// fmt.Printf("AssetWallets %+v %+v \n", b.AssetWallets["USDT"], b.AssetWallets[fd.Asset])
		// fmt.Printf("Positions %+v \n", b.Positions[SymbolName])
		// fmt.Printf("FuturesPending %+v \n", b.FuturesPending)
		// fmt.Printf("MarginPending %+v \n", b.MarginPending)
		// fmt.Printf("MaxSize %.6f \n", b.TierMaxSize(fd))
		// fmt.Printf("Leverage %+v \n", b.Leverages[SymbolName])
		// fmt.Printf("fd tick size %f \n", fd.Info.TickSize)
		// fmt.Printf("sd tick size %f \n", sd.Info.TickSize)
		// fmt.Printf("fd round to step %f \n", utils.RoundToStep(fd.Bid1()*(1-0.005), fd.Info.TickSize))
		// fmt.Printf("sd round to step %f \n", utils.RoundToStep(sd.Ask1()*(1+0.005), sd.Info.TickSize))
		fmt.Printf("fd books %+v \n", fd.BookNAll(7))
		// fmt.Printf("sd asks %+v \n", sd.BookNAll(7))
		// fmt.Printf("fd asks agg %+v \n", fd.BookNAllAgg(0, 1))
	}
}
