package main

import (
	config "GoTrader/internal"
	"GoTrader/pkg/logs"
	"io"
	"log/slog"
	"os"
	"time"

	"github.com/redis/go-redis/v9"
)

// 监控程序
func main() {
	// redis
	taskRdb := redis.NewClient(&redis.Options{
		Addr:     config.TaskAddr,
		Password: config.TaskPassword,
		DB:       config.TaskDB,
	})

	tokyoRdb := redis.NewClient(&redis.Options{
		Addr:     config.TokyoRedisAddr,
		Password: config.TokyoRedisPassword,
		DB:       config.TokyoDiffDB,
	})

	hkRdb := redis.NewClient(&redis.Options{
		Addr:     config.HKRedisAddr,
		Password: config.HKRedisPassword,
		DB:       config.HKDiffDB,
	})

	feishuWriter := logs.NewFeishuWriter(
		[]string{config.UserLiquid},
		config.GroupLiquidTest.Hook,
		config.GroupLiquidTest.Secret,
		"任务监控",
		slog.LevelWarn,
	)

	logger := slog.New(slog.NewJSONHandler(io.MultiWriter(os.Stdout, feishuWriter), nil))

	// 先执行一次
	monitor(taskRdb, tokyoRdb, hkRdb, logger)
	// 10分钟执行一次
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		monitor(taskRdb, tokyoRdb, hkRdb, logger)
	}
}

func monitor(taskRdb *redis.Client, tokyoRdb *redis.Client, hkRdb *redis.Client, logger *slog.Logger) {
	logger.Info("开始检查")

	noRunTasks, delayTasks := checkArbitTasks(taskRdb, logger)
	logger.Info("获取套利任务状态完毕")

	if len(noRunTasks) != 0 {
		logger.Warn("未运行的套利任务", "tasks", noRunTasks)
	}
	if len(delayTasks) != 0 {
		logger.Warn("更新延迟的套利任务", "tasks", delayTasks)
	}

	crossDelay := checkCrossDiffUpdate(tokyoRdb, logger)
	if crossDelay {
		logger.Warn("跨所价差更新延迟数量过多")
	}

	logger.Info("跨所价差检查完毕")
	okDelay := checkOkDiffUpdate(hkRdb, logger)
	if okDelay {
		logger.Warn("欧意价差更新延迟数量过多")
	}

	bnDelay := checkBnDiffUpdate(tokyoRdb, logger)
	if bnDelay {
		logger.Warn("币安价差更新延迟数量过多")
	}
	logger.Info("币安价差检查完毕")

	logger.Info("检查完毕")
}
