package main

import (
	config "GoTrader/internal"
	"context"
	"encoding/json"
	"log/slog"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

func checkCrossDiffUpdate(rdb *redis.Client, logger *slog.Logger) (delay bool) {
	return checkDiffUpdate(rdb, config.CrossDiffTable, logger)
}

func checkBnDiffUpdate(rdb *redis.Client, logger *slog.Logger) (delay bool) {
	return checkDiffUpdate(rdb, config.BnDiffTable, logger)
}

func checkOkDiffUpdate(rdb *redis.Client, logger *slog.Logger) (delay bool) {
	return checkDiffUpdate(rdb, config.OkDiffTable, logger)
}

func checkDiffUpdate(rdb *redis.Client, table string, logger *slog.Logger) (delay bool) {

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	crossDiffs, err := rdb.HGetAll(ctx, table).Result()
	if err != nil {
		logger.Info("获取价差失败", "error", err)
		return false
	}

	allnums := int64(len(crossDiffs))
	delaynums := int64(0)
	for _, infoJson := range crossDiffs {
		// 检查任务是否过期
		var info map[string]string
		json.Unmarshal([]byte(infoJson), &info)
		leftTs, _ := strconv.ParseInt(info["left_time"], 10, 64)
		rightTs, _ := strconv.ParseInt(info["right_time"], 10, 64)
		ts := min(leftTs, rightTs)
		// ms时间戳距离当前时间超过5min
		if time.Now().UnixMilli()-ts > 5*60*1000 {
			delaynums += 1
		}
	}
	if delaynums > 50 && float64(delaynums/allnums) > 0.5 {
		return true
	}
	return false
}
