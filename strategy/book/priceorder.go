package book

import (
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	"fmt"
	"math"
)

// 仓位管理
// 通过保存的订单, 设置价格和当前价格, 判断是否需要下单, 修改订单, 撤单
type PositionManager struct {
	buyPrice         float64               // 买入价格
	sellPrice        float64               // 卖出价格
	stopTriggerPrice float64               // 止损触发价格
	stopPrice        float64               // 止损价格
	priceTolerance   float64               // 价格容忍偏离度
	sizeTolerance    float64               // 仓位容忍偏离度
	size             float64               // 管理仓位
	reduce           bool                  // 是否只减仓
	close            bool                  // 是否强制平仓
	buyOrder         []*order.FuturesOrder // 买单
	sellOrder        []*order.FuturesOrder // 卖单
}

func (p *PositionManager) String() string {
	return fmt.Sprintf("Position: buyPrice: %.6f, size: %.6f, leftSize: %.6f",
		p.buyPrice, p.size, p.<PERSON>eft())
}

func NewPositionManager(buyPrice, sellPrice, stopTriggerPrice, stopPrice, size, priceTolerance, sizeTolerance float64) (*PositionManager, error) {
	if sellPrice < buyPrice || stopPrice > buyPrice || stopTriggerPrice < stopPrice {
		return nil, fmt.Errorf("invalid price range: buyPrice %f, sellPrice %f, stopTriggerPrice %f, stopPrice %f", buyPrice, sellPrice, stopTriggerPrice, stopPrice)
	}
	return &PositionManager{
		buyPrice:         buyPrice,
		sellPrice:        sellPrice,
		stopTriggerPrice: stopTriggerPrice,
		stopPrice:        stopPrice,
		priceTolerance:   priceTolerance,
		sizeTolerance:    sizeTolerance,
		reduce:           false,
		close:            false,
		size:             size,
	}, nil
}

// 是否为同一个仓位管理
func (p *PositionManager) IsEqual(price, size float64) bool {
	return math.Abs(p.buyPrice-price) < p.priceTolerance && math.Abs(p.size-size) < p.sizeTolerance
}

// 剩余仓位
func (p *PositionManager) SizeLeft() float64 {
	sell := 0.0
	buy := 0.0
	for _, o := range p.sellOrder {
		sell += o.GetExecutedSize()
	}
	for _, o := range p.buyOrder {
		buy += o.GetExecutedSize()
	}
	return buy - sell
}

// 剩余占用
func (p *PositionManager) ValueOccupied(leverage float64) float64 {
	sizeOccupied := p.buyPrice * p.SizeLeft()
	orderOccupied := 0.0
	for _, o := range p.buyOrder {
		if o.Alive() {
			orderOccupied += o.Price * (o.GetSize() - o.GetExecutedSize()) / leverage
		}
	}
	return sizeOccupied + orderOccupied
}

// 活跃买单
func (p *PositionManager) ActiveBuyOrder() []*order.FuturesOrder {
	active := []*order.FuturesOrder{}
	for _, o := range p.buyOrder {
		if o.Alive() {
			active = append(active, o)
		}
	}
	return active
}

// 活跃卖单
func (p *PositionManager) ActiveSellOrder() []*order.FuturesOrder {
	active := []*order.FuturesOrder{}
	for _, o := range p.sellOrder {
		if o.Alive() {
			active = append(active, o)
		}
	}
	return active
}

// 待成交买入仓位
func (p *PositionManager) OrderBuy() float64 {
	active := p.ActiveBuyOrder()
	size := 0.0
	for _, o := range active {
		size += o.GetSize() - o.GetExecutedSize()
	}
	return size
}

// 待成交卖出仓位
func (p *PositionManager) OrderSell() float64 {
	active := p.ActiveSellOrder()
	size := 0.0
	for _, o := range active {
		size += o.GetSize() - o.GetExecutedSize()
	}
	return size
}

// 应当买入仓位
func (p *PositionManager) ShouldBuy() float64 {
	// 减仓模式下不买入
	if p.reduce {
		return 0
	}
	return p.size - p.SizeLeft() - p.OrderBuy()
}

// 应当卖出仓位
func (p *PositionManager) ShouldSell() float64 {
	return p.SizeLeft() - p.OrderSell()
}

func (p *PositionManager) AppendOrder(o *order.FuturesOrder) {
	if o.IsOpen() {
		p.buyOrder = append(p.buyOrder, o)
	} else {
		p.sellOrder = append(p.sellOrder, o)
	}
}

func (p *PositionManager) Active() {
	if p.close {
		return
	}
	p.reduce = false
}

func (p *PositionManager) Reduce() {
	p.reduce = true
}

func (p *PositionManager) Close() {
	p.close = true
}

func (p *PositionManager) IsClear() bool {
	return p.SizeLeft() < p.sizeTolerance && p.reduce && len(p.ActiveBuyOrder()) == 0 && len(p.ActiveSellOrder()) == 0
}

// 根据当前价格判断需要下单, 修改订单, 撤单
func (p *PositionManager) ShouldDo(price float64) (submit *order.FuturesOrder, cancel []*order.FuturesOrder) {
	// 强制平仓时, 撤销所有订单, 下止损单
	if p.close {
		cancel = append(p.ActiveBuyOrder(), p.ActiveSellOrder()...)
		if size := p.SizeLeft(); size > p.sizeTolerance {
			submit = order.CreateFuturesOrder().SetClOrdId("forceClose").SetPrice(utils.RoundToStep(p.stopPrice, p.priceTolerance)).
				SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
			return
		}
	}

	// 只减仓时, 撤销所有买单
	if p.reduce {
		cancel = p.ActiveBuyOrder()
	}

	// 价格高于卖出价格时, 理论上卖单全部成交, 考虑买入仓位对齐
	if price > p.sellPrice {
		if size := p.ShouldBuy(); size > p.sizeTolerance {
			submit = order.CreateFuturesOrder().SetClOrdId("buy").SetPrice(utils.RoundToStep(p.buyPrice, p.priceTolerance)).
				SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.BUY).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
		}
		return
	}

	// 当价格低于卖出价格, 高于买入价格时, 考虑卖出仓位等于当前剩余仓位, 以及买入仓位等于理论仓位
	if price <= p.sellPrice && price >= p.buyPrice {
		if size := p.ShouldSell(); size > p.sizeTolerance {
			submit = order.CreateFuturesOrder().SetClOrdId("sell").SetPrice(utils.RoundToStep(p.sellPrice, p.priceTolerance)).
				SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
			return
		}
		if size := p.ShouldBuy(); size > p.sizeTolerance {
			submit = order.CreateFuturesOrder().SetClOrdId("buy").SetPrice(utils.RoundToStep(p.buyPrice, p.priceTolerance)).
				SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.BUY).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
			return
		}
		return
	}

	// 当价格低于买入价格, 高于止损触发价格时, 考虑卖出仓位等于当前剩余仓位
	if price < p.buyPrice && price > p.stopTriggerPrice {
		if size := p.ShouldSell(); size > p.sizeTolerance {
			submit = order.CreateFuturesOrder().SetClOrdId("sell").SetPrice(utils.RoundToStep(p.sellPrice, p.priceTolerance)).
				SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
		}
		return
	}

	// 价格低于止损触发价格, 高于止损价格时, 理论上开仓订单全部成交, 平仓订单未成交
	// 理论上止损单会立即成交, 如果没有立即成交, 也会优先卖单成交, 系统会自动撤销所有卖单，达成买卖仓位平衡
	if price < p.stopTriggerPrice && price > p.stopPrice {
		// cancel = p.ActiveSellOrder()
		if size := p.SizeLeft(); size > p.sizeTolerance {
			submit = order.CreateFuturesOrder().SetClOrdId("closeTrigger").SetPrice(utils.RoundToStep(p.stopPrice, p.priceTolerance)).
				SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
			return
		}
	}

	//  价格低于止损价格时, 理论上开仓订单全部成交, 下止损单, 市价单平仓
	// 理论上止损单会立即成交, 系统会自动撤销所有卖单，达成买卖仓位平衡
	if price < p.stopPrice {
		if len(p.ActiveBuyOrder()) > 0 || len(p.ActiveSellOrder()) > 0 {
			cancel = append(p.ActiveBuyOrder(), p.ActiveSellOrder()...)
		}
		if size := p.SizeLeft(); size > p.sizeTolerance {
			submit = order.CreateFuturesOrder().SetClOrdId("closeMarket").
				SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.MARKET).SetTimeInForce(order.GTC)
			return
		}
	}

	return
}
