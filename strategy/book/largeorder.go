package book

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	"GoTrader/strategy"
	"fmt"
	"log/slog"
	"math"
	"strconv"
	"time"
)

// 大额订单盘口策略
type LargeOrderBookStrategy struct {
	Data   *data.SymbolData
	broker *broker.Broker
	client client.APIClient

	otherClients []client.APIClient

	// 24h成交额
	quote1d float64
	// 持仓量
	openInt float64

	// 仓位管理
	posManagers []*PositionManager
	// 单个仓位
	stepSize float64

	// 订单簿队列
	bookQueue BookQueue

	// 大单跟踪器
	largeOrderTracker *PriceTracker
	// 成交额定时器
	quoteTimer *utils.DoTimer
	// 最大价值滚动定时器
	maxBookScrollTimer *utils.DoTimer
	// 状态定时器
	statusTimer *utils.DoTimer
	// 通知定时器
	notifyTimer *utils.DoTimer
	// 其他定时器
	otherTimer *utils.DoTimer
	Params     BookParams
	// 任务状态
	status strategy.TaskStatus
	// 停止channel
	stopChan chan struct{}
	// 展示信息, 用于接受策略生成信息
	redisStatusChan chan []byte
	// redis上下文
	redisCtx *strategy.RedisCtx
	// redis hset channel, 用于发送展示信息
	redisHsetChan chan map[string]string
	// 监控心跳
	monitorTicker *time.Ticker
	// 监控channel
	monitorChan chan struct{}
	logger      *slog.Logger
}

func NewLargeOrderBookStrategy(
	data *data.SymbolData, broker *broker.Broker, client client.APIClient, otherClients []client.APIClient,
	params BookParams, redisCtx *strategy.RedisCtx, hsetChan chan map[string]string,
	logger *slog.Logger,
) *LargeOrderBookStrategy {
	logger = logger.With(slog.String("strategy", "LargeOrderBookStrategy")).With(slog.String("symbol", params.GetSymbol())).With(slog.String("ID", params.GetID()))
	return &LargeOrderBookStrategy{
		Data:         data,
		broker:       broker,
		client:       client,
		otherClients: otherClients,
		Params:       params,

		stepSize:    0,
		posManagers: []*PositionManager{},

		largeOrderTracker:  NewPriceTracker(),
		quoteTimer:         &utils.DoTimer{Interval: time.Hour * 4},
		statusTimer:        &utils.DoTimer{Interval: time.Second * 10},
		maxBookScrollTimer: &utils.DoTimer{Interval: time.Hour * 4},
		notifyTimer:        &utils.DoTimer{Interval: time.Hour * 1},
		otherTimer:         &utils.DoTimer{Interval: time.Second * 10},

		status:          params.Status,
		stopChan:        make(chan struct{}),
		redisStatusChan: make(chan []byte),
		redisCtx:        redisCtx,
		redisHsetChan:   hsetChan,
		monitorTicker:   time.NewTicker(time.Second * 300),
		monitorChan:     make(chan struct{}, 1),
		logger:          logger,
	}
}

func (s *LargeOrderBookStrategy) Start() {
	// 初始化broker
	strategy.InitBroker(s.Data, s.broker, s.client, s.Params, s.logger)

	if !strategy.BrokerValidCheck(s.Data, s.broker, s.logger) {
		s.logger.Error("broker有效性检查失败, 停止运行")
		s.Pause()
		return
	}

	// 初始化队列
	s.bookQueue = NewBookQueue(10, 10_000)
	s.maxBookScrollTimer.Do(func() {})

	// 初始仓位和订单作为单独的仓位管理
	size := s.broker.GetDataSize(s.Data)
	buyPrice := s.broker.GetAvgPrice(s.Data)
	posManager := &PositionManager{
		buyPrice:         buyPrice,
		sellPrice:        buyPrice + 10*s.Data.Info.TickSize,
		stopTriggerPrice: buyPrice - 10*s.Data.Info.TickSize,
		stopPrice:        buyPrice - 15*s.Data.Info.TickSize,
		priceTolerance:   0,
		sizeTolerance:    0,
		size:             size,
	}
	for _, o := range s.broker.GetFuturesOrders(s.Data.Symbol) {
		// 撤销买单
		if o.IsOpen() {
			s.logger.Info("撤销买单", "order", o)
			err := s.client.CancelFuturesOrder(s.broker, o)
			if err != nil {
				s.logger.Error("撤销买单失败", "error", err)
			}
		} else {
			posManager.AppendOrder(o)
		}
	}
	// 手动添加一个虚拟订单
	posManager.AppendOrder(&order.FuturesOrder{
		Symbol:       s.Data.Symbol,
		ClOrdId:      "psudoBuyOrder",
		Price:        buyPrice,
		Size:         size,
		ExecutedSize: size,
		Side:         order.BUY,
		PosSide:      order.LONG,
		OrdType:      order.LIMIT,
		TimeInForce:  order.GTC,
		Status:       order.FILLED,
	})
	posManager.Reduce()
	if size > 0 {
		s.posManagers = append(s.posManagers, posManager)
	}
	s.logger.Info("初始化仓位管理", "posManager", posManager)

	// 数据源检测
	go strategy.DataMonitor(s.Data, s.stopChan, s.monitorChan, s.next, s.logger)

	// 心跳检查
	go strategy.Heartbeat(s.stopChan, s.monitorChan, s.monitorTicker, s.logger)

	// 写入redis
	go strategy.WriteRedis(s.redisCtx, s.Params, s.stopChan, s.redisStatusChan, s.redisHsetChan, s.logger)

	s.logger.Info("初始化完毕")
}

func (s *LargeOrderBookStrategy) next() {
	// 强制平仓状态, 无仓位, 无挂单退出
	// TODO 需要处理人为开仓
	if s.status == strategy.Stop && s.broker.GetDataSize(s.Data) == 0 && !s.broker.HasFuturesPending(s.Data.Symbol) {
		s.logger.Info("任务已平掉所有仓位, 关闭任务")
		s.Pause()
		return
	}

	// 获取24h成交额和持仓量
	s.quoteTimer.Do(func() {
		quote, err := s.client.Query24hrQuote(s.Data)
		if err != nil {
			s.logger.Error("查询24h成交额失败", "error", err)
			return
		} else {
			s.logger.Info("查询24h成交额成功", "quote", quote)
		}
		for _, client := range s.otherClients {
			otherQuote, err := client.Query24hrQuote(s.Data)
			if err != nil {
				s.logger.Info("查询其他交易所24h成交额失败", "error", err)
				continue
			} else {
				s.logger.Info("查询其他交易所24h成交额成功", "quote", otherQuote)
			}
			quote += otherQuote
		}
		s.quote1d = quote

		openInt, err := s.client.QueryOpenInterest(s.Data)
		if err != nil {
			s.logger.Error("查询持仓量失败", "error", err)
			return
		} else {
			s.logger.Info("查询持仓量成功", "openInt", openInt)
		}
		s.openInt = openInt
	})

	// 计算单个仓位
	if s.stepSize == 0 {
		// maxLevel := s.Params.PriceLevel
		maxLevel := 1
		stepValue := s.Params.CapitalLimit * float64(s.broker.DataLeverage(s.Data)) / float64(maxLevel*(maxLevel+1)/2)
		price := s.Data.Bid1Price()
		stepSize := utils.RoundToStep(stepValue/price, s.Data.Info.StepSize*s.Data.Info.CtVal)
		if stepValue < s.Data.Info.MinNotional {
			s.statusTimer.Do(func() {
				s.logger.Info("计算仓位过小",
					"capitalLimit", s.Params.CapitalLimit, "maxLevel", maxLevel, "stepSize", stepSize,
					"stepValue", stepValue, "minNotional", s.Data.Info.MinNotional)
			})
			return
		}
		s.stepSize = stepSize
		s.logger.Info("计算单个仓位", "capitalLimit", s.Params.CapitalLimit, "maxLevel", maxLevel, "stepSize", stepSize, "stepValue", stepValue)
	}

	priceLimitMax := s.Data.MaxOrderPriceStrict()
	priceLimitMin := s.Data.MinOrderPriceStrict()

	bidPrice := s.Data.Bid1Price()
	askPrice := s.Data.Ask1Price()

	minValue, _, _ := s.bookQueue.Min()
	largeValueThres := max(min(s.quote1d*s.Params.QuoteCoeff, s.openInt*bidPrice*s.Params.OpenIntCoeff), float64(minValue), s.Params.MinLargeOrderValue) // 大额订单阈值

	// 识别托底订单
	if largeValueThres == 0 {
		s.otherTimer.Do(func() {
			s.logger.Warn("largeValueThres为0, 跳过托底订单识别")
		})
		return
	}

	bottomPrice := math.Inf(1)     // 托底价格
	maxBidBookValue := 0.0         // 最大买单价值
	maxBidBook := data.OrderBook{} // 最大买单
	maxBidBookIndex := 0           // 最大买单索引
	totalBidBookValue := 0.0       // 总买单价值

	bookStep := max(utils.GetMaxPrecisionStep(bidPrice*0.0001), s.Data.Info.TickSize) // 盘口精度

	for i, book := range s.Data.BidNAllAgg(0, bookStep) {
		totalBidBookValue += book.Size * book.Price
		if book.Size*book.Price > max(min(s.quote1d*s.Params.QuoteCoeff, s.openInt*bidPrice*s.Params.OpenIntCoeff), s.Params.MinLargeOrderValue) { // 除去历史订单的大额订单阈值
			// 更新队列
			s.bookQueue.Push(book)
		}
		if book.Size*book.Price > maxBidBookValue {
			maxBidBookValue = book.Size * book.Price
			maxBidBook = book
			maxBidBookIndex = i
		}
		if float64(utils.RoundIntToStep(int(book.Size*book.Price), 10000)) >= largeValueThres && bottomPrice == math.Inf(1) {
			bottomPrice = book.Price
		}
	}

	// 记录卖单
	maxAskBookValue := 0.0         // 最大卖单价值
	maxAskBook := data.OrderBook{} // 最大卖单
	maxAskBookIndex := 0           // 最大卖单索引
	totalAskBookValue := 0.0       // 总卖单价值
	for i, book := range s.Data.AskNAllAgg(0, bookStep) {
		totalAskBookValue += book.Size * book.Price
		if book.Size*book.Price > maxAskBookValue {
			maxAskBookValue = book.Size * book.Price
			maxAskBook = book
			maxAskBookIndex = i
		}
	}

	// 大单跟踪
	s.largeOrderTracker.UpdatePrice(bottomPrice, s.Data.Info.TickSize)

	// 大单持续时间
	keepSeconds := s.largeOrderTracker.KeepSeconds()

	// 无托底订单, 撤销现有订单, 尝试卖掉仓位
	if maxBidBookValue > max(min(s.quote1d*s.Params.QuoteCoeff, s.openInt*bidPrice*s.Params.OpenIntCoeff), s.Params.MinLargeOrderValue) && bidPrice-maxBidBook.Price < maxBidBook.Price*0.001 {
		s.notifyTimer.Do(func() {
			s.logger.Warn("大额买单接近提醒", "盘口精度", bookStep, "价格", maxBidBook.Price, "数量", maxBidBook.Size, "价值", maxBidBookValue, "深度", maxBidBookIndex, "买一价", bidPrice)
		})
	}
	if maxAskBookValue > max(min(s.quote1d*s.Params.QuoteCoeff, s.openInt*bidPrice*s.Params.OpenIntCoeff), s.Params.MinLargeOrderValue) && maxAskBook.Price-askPrice < maxAskBook.Price*0.001 {
		s.notifyTimer.Do(func() {
			s.logger.Warn("大额卖单接近提醒", "盘口精度", bookStep, "价格", maxAskBook.Price, "数量", maxAskBook.Size, "价值", maxAskBookValue, "深度", maxAskBookIndex, "卖一价", askPrice)
		})
	}

	// 当前需开仓仓位
	// maxLevel := s.Params.PriceLevel
	newPosManagers := []PositionManager{}
	// 开仓条件
	// if s.status == Active && s.Params.CapitalLimit < s.broker.GetAvailable(s.Data) && keepSeconds > 2 {
	// 	// 临时修改逻辑, 买一价买入
	// 	price := s.Data.Bid1Price()
	// 	if bottomPrice < price {
	// 		size := s.stepSize
	// 		newPosManagers = append(newPosManagers, PositionManager{
	// 			buyPrice:         utils.RoundToStep(price, s.Data.Info.TickSize),
	// 			sellPrice:        utils.RoundToStep(price+10*s.Data.Info.TickSize, s.Data.Info.TickSize),
	// 			stopTriggerPrice: utils.RoundToStep(price-10*s.Data.Info.TickSize, s.Data.Info.TickSize),
	// 			stopPrice:        utils.RoundToStep(price-20*s.Data.Info.TickSize, s.Data.Info.TickSize),
	// 			priceTolerance:   s.Data.Info.TickSize,
	// 			sizeTolerance:    s.Data.Info.StepSize,
	// 			size:             utils.RoundToStep(size, s.Data.Info.StepSize*s.Data.Info.CtVal),
	// 		})
	// 	}

	// 	// 原有逻辑
	// 	// for i := range maxLevel {
	// 	// 	price := bottomPrice + float64(i+1)*s.Data.Info.TickSize
	// 	// 	if price > priceLimitMax || price < priceLimitMin || price > currPrice {
	// 	// 		continue
	// 	// 	}
	// 	// 	size := float64(maxLevel-i) * s.stepSize
	// 	// 	newPosManagers = append(newPosManagers, PositionManager{
	// 	// 		buyPrice:         utils.RoundToStep(price, s.Data.Info.TickSize),
	// 	// 		sellPrice:        utils.RoundToStep(price+10*s.Data.Info.TickSize, s.Data.Info.TickSize),
	// 	// 		stopTriggerPrice: utils.RoundToStep(price-10*s.Data.Info.TickSize, s.Data.Info.TickSize),
	// 	// 		stopPrice:        utils.RoundToStep(price-15*s.Data.Info.TickSize, s.Data.Info.TickSize),
	// 	// 		priceTolerance:   s.Data.Info.TickSize,
	// 	// 		sizeTolerance:    s.Data.Info.StepSize,
	// 	// 		size:             utils.RoundToStep(size, s.Data.Info.StepSize*s.Data.Info.CtVal),
	// 	// 	})
	// 	// }
	// }

	// 将原有仓位设置为reduce
	for _, posManager := range s.posManagers {
		posManager.Reduce()
	}

	// 新仓位和旧仓位合并
	// 考虑资金使用情况
	leverage := s.broker.DataLeverage(s.Data)
	available := min(s.Params.CapitalLimit, s.broker.GetAvailable(s.Data)) * float64(s.broker.DataLeverage(s.Data))
	occupied := 0.0
	for _, posManager := range s.posManagers {
		occupied += posManager.ValueOccupied(leverage)
	}

	addPosManagers := []*PositionManager{}
	for _, newPosManager := range newPosManagers {
		// 检查是否已有相同对象
		exists := false
		for _, oldPosManager := range s.posManagers {
			// 有相同对象, 激活仓位
			if newPosManager.IsEqual(oldPosManager.buyPrice, oldPosManager.size) {
				oldPosManager.Active()
				exists = true
				break
			}
		}
		// 无相同对象, 添加新仓位
		if !exists && occupied+newPosManager.ValueOccupied(leverage) < available {
			occupied += newPosManager.ValueOccupied(leverage)
			addPosManagers = append(addPosManagers, &newPosManager)
		}
	}

	// 添加新仓位
	s.posManagers = append(s.posManagers, addPosManagers...)

	// 强制清仓
	if s.status == strategy.Stop {
		for _, posManager := range s.posManagers {
			posManager.Close()
		}
	}

	// 对仓位状态执行动作, 撤销订单, 提交订单
	for _, posManager := range s.posManagers {
		submitOrder, cancelOrders := posManager.ShouldDo(bidPrice)
		if submitOrder != nil {
			s.logger.Info("提交订单", "manager", posManager.String())
			_, err := s.client.SubmitFuturesOrder(s.broker, submitOrder.SetSymbol(s.Data.Symbol), s.Data.Info)
			if err != nil {
				s.logger.Error("提交订单失败", "order", submitOrder, "error", err)
			} else {
				s.logger.Info("提交订单成功", "order", submitOrder)
				// 添加订单
				posManager.AppendOrder(submitOrder)
			}
		}
		for _, cancelOrder := range cancelOrders {
			s.logger.Info("撤销订单", "manager", posManager.String())
			err := s.client.CancelFuturesOrder(s.broker, cancelOrder)
			if err != nil {
				s.logger.Error("撤销订单失败", "order", cancelOrder, "error", err)
			} else {
				s.logger.Info("撤销订单成功", "order", cancelOrder)
			}
		}
	}

	// 清理已经完成生命周期的仓位
	activePosManagers := []*PositionManager{}
	for _, posManager := range s.posManagers {
		if !posManager.IsClear() {
			activePosManagers = append(activePosManagers, posManager)
		}
	}
	s.posManagers = activePosManagers

	// 计算管理仓位
	managedPosition := 0.0
	for _, posManager := range s.posManagers {
		managedPosition += posManager.SizeLeft()
	}

	// 当前仓位, 管理仓位
	statusLogger := s.logger.With(
		slog.Group(
			"仓位",
			slog.String("当前仓位", strconv.FormatFloat(s.broker.GetDataSize(s.Data), 'f', 6, 64)),
			slog.String("管理仓位", strconv.FormatFloat(managedPosition, 'f', 6, 64)),
			slog.String("单个仓位", strconv.FormatFloat(s.stepSize, 'f', 6, 64)),
			slog.String("最小价格变动", strconv.FormatFloat(s.Data.Info.TickSize, 'f', 6, 64)),
			slog.String("限制资金", strconv.FormatFloat(s.Params.CapitalLimit, 'f', 6, 64)),
			slog.String("仓位管理", fmt.Sprintf("%+v", s.posManagers)),
			slog.String("可用资金", strconv.FormatFloat(s.broker.GetAvailable(s.Data), 'f', 6, 64)),
		),
		slog.Group(
			"状态",
			slog.String("任务当前状态", s.status.String()),
			slog.Int64("订单层数", s.Params.PriceLevel),
			slog.String("24h成交额系数", strconv.FormatFloat(s.Params.QuoteCoeff, 'f', 6, 64)),
			slog.String("持仓量系数", strconv.FormatFloat(s.Params.OpenIntCoeff, 'f', 6, 64)),
			slog.String("最小大额订单阈值", strconv.FormatFloat(s.Params.MinLargeOrderValue, 'f', 6, 64)),
		),
		slog.Group(
			"托底订单",
			slog.String("托底价格", strconv.FormatFloat(bottomPrice, 'f', 6, 64)),
			slog.String("盘口精度", strconv.FormatFloat(bookStep, 'f', 6, 64)),
			slog.String("限价上限", strconv.FormatFloat(priceLimitMax, 'f', 6, 64)),
			slog.String("限价下限", strconv.FormatFloat(priceLimitMin, 'f', 6, 64)),
			slog.String("最大订单数量", strconv.FormatFloat(maxBidBook.Size, 'f', 6, 64)),
			slog.String("最大订单价格", strconv.FormatFloat(maxBidBook.Price, 'f', 6, 64)),
			slog.String("最大订单价值", strconv.FormatFloat(maxBidBookValue, 'f', 6, 64)),
			slog.String("最大订单索引", strconv.Itoa(maxBidBookIndex)),
			slog.String("总买单价值", strconv.FormatFloat(totalBidBookValue, 'f', 6, 64)),
			slog.String("最大订单占比总买单价值", strconv.FormatFloat(maxBidBookValue/totalBidBookValue, 'f', 6, 64)),
			slog.String("最大卖单数量", strconv.FormatFloat(maxAskBook.Size, 'f', 6, 64)),
			slog.String("最大卖单价格", strconv.FormatFloat(maxAskBook.Price, 'f', 6, 64)),
			slog.String("最大卖单价值", strconv.FormatFloat(maxAskBookValue, 'f', 6, 64)),
			slog.String("最大卖单索引", strconv.Itoa(maxAskBookIndex)),
			slog.String("总卖单价值", strconv.FormatFloat(totalAskBookValue, 'f', 6, 64)),
			slog.String("最大卖单占比总卖单价值", strconv.FormatFloat(maxAskBookValue/totalAskBookValue, 'f', 6, 64)),
			slog.String("当前买一价", strconv.FormatFloat(bidPrice, 'f', 6, 64)),
			slog.String("当前卖一价", strconv.FormatFloat(s.Data.Ask1Price(), 'f', 6, 64)),
			slog.String("24h成交额", strconv.FormatFloat(s.quote1d, 'f', 6, 64)),
			slog.String("持仓量", strconv.FormatFloat(s.openInt, 'f', 6, 64)),
			slog.String("持仓量USD", strconv.FormatFloat(s.openInt*bidPrice, 'f', 6, 64)),
			slog.String("大额订单阈值", strconv.FormatFloat(largeValueThres, 'f', 6, 64)),
			slog.String("前N订单价值", fmt.Sprint(s.bookQueue.AllValues())),
			slog.String("持续时间", strconv.FormatFloat(keepSeconds, 'f', 6, 64)),
			slog.String("买盘盘口", utils.TruncateString(fmt.Sprintf("%+v", s.Data.BidNAllAgg(0, bookStep)), 500)),
		),
	)

	s.statusTimer.Do(func() {
		statusLogger.Info("Status")
	})

	s.maxBookScrollTimer.Do(func() {
		// 滚动最大订单价值
		minValue, minBook, err := s.bookQueue.Min()
		if err != nil {
			s.logger.Info("获取订单为空")
			return
		}
		s.logger.Info("上一轮滚动前N订单价值", "minValue", minValue)
		newQueue := NewBookQueue(10, 10_000)
		newQueue.Push(minBook)
		s.bookQueue = newQueue
	})

}

// 暂停任务运行
func (stra *LargeOrderBookStrategy) Pause() {
	select {
	case <-stra.stopChan:
		stra.logger.Info("stop trigger already sent")
	default:
		close(stra.stopChan)
		stra.logger.Info("stop trigger sent")
	}
}

// 停止任务并平仓
func (stra *LargeOrderBookStrategy) Stop() {
	if stra.status < strategy.Stop {
		stra.logger.Info("status stop")
		stra.status = strategy.Stop
	}
}

// 是否正在运行
func (stra *LargeOrderBookStrategy) Running() bool {
	select {
	case <-stra.stopChan:
		return false
	default:
		return true
	}
}

// 当前状态
func (stra *LargeOrderBookStrategy) Status() strategy.TaskStatus {
	return stra.status
}

// 减仓
func (stra *LargeOrderBookStrategy) Reduce() {
	if stra.status < strategy.Reduce {
		stra.status = strategy.Reduce
	}
}
