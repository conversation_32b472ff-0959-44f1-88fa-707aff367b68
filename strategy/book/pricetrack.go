package book

import (
	"math"
	"time"
)

// PriceTracker 用于跟踪价格及其记录时间，支持误差范围判断
type PriceTracker struct {
	price        float64   // 当前记录的价格
	recordedTime time.Time // 该价格被记录的时间
}

// NewPriceTracker 创建一个新的 PriceTracker 实例
func NewPriceTracker() *PriceTracker {
	return &PriceTracker{
		price:        0,
		recordedTime: time.Now(), // 初始化为当前时间
	}
}

// UpdatePrice 更新价格和记录时间
// 参数:
//   - newPrice: 新输入的价格
//   - tolerance: 允许的误差范围，只有当 |newPrice - oldPrice| > tolerance 时才更新
//
// 规则：
// 1. 如果新价格为 0 或 无穷大（正/负），则更新时间和价格
// 2. 如果新价格与当前记录的价格之差的绝对值 > tolerance，则更新时间和价格
// 3. 否则（价格在误差范围内，且不是特殊情况），不更新
func (pt *PriceTracker) UpdatePrice(newPrice float64, tolerance float64) {
	// Case 1: 新价格为 0 或者为无穷大（正/负），认为需要更新
	isInvalidPrice := newPrice == 0 || math.IsInf(newPrice, 0)
	if isInvalidPrice {
		pt.price = newPrice
		pt.recordedTime = time.Now()
		return
	}

	// Case 2: 检查价格变化是否超出误差范围
	priceDiff := math.Abs(newPrice - pt.price)
	if priceDiff > tolerance {
		pt.price = newPrice
		pt.recordedTime = time.Now()
	}
	// else: 价格在误差范围内，不更新时间和价格
}

// SecondsSinceRecorded 返回距离记录时间的秒数（float64）
func (pt *PriceTracker) KeepSeconds() float64 {
	elapsed := time.Since(pt.recordedTime)
	return elapsed.Seconds()
}
