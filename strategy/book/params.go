package book

import (
	"GoTrader/strategy"
	"strconv"
)

// 策略参数
type BookParams struct {
	ID                 string              // 任务ID
	Symbol             string              // 交易品种
	Leverage           int64               // 杠杆
	Exchange           string              // 交易所
	QuoteCoeff         float64             // 成交额系数
	OpenIntCoeff       float64             // 持仓量系数
	CapitalLimit       float64             // 资金分配上限
	PriceLevel         int64               // 价格档位数量
	MinLargeOrderValue float64             // 最小大额订单阈值
	MinAvailable       float64             // 最小可用余额
	Status             strategy.TaskStatus // 状态
}

func (p BookParams) String() string {
	return "ID: " + p.ID + ", Symbol: " + p.Symbol +
		", Leverage: " + strconv.FormatInt(p.Leverage, 10) +
		", Exchange: " + p.Exchange +
		", QuoteCoeff: " + strconv.FormatFloat(p.QuoteCoeff, 'f', 6, 64) +
		", OpenIntCoeff: " + strconv.FormatFloat(p.<PERSON>Int<PERSON>oeff, 'f', 6, 64) +
		", PriceLevel: " + strconv.FormatInt(p.PriceLevel, 10) +
		", MinAvailable: " + strconv.FormatFloat(p.MinAvailable, 'f', 6, 64) +
		", CapitalLimit: " + strconv.FormatFloat(p.CapitalLimit, 'f', 6, 64) +
		", Status: " + p.Status.String()
}

func (p BookParams) GetID() string {
	return p.ID
}

func (p BookParams) GetLeverage() int64 {
	return p.Leverage
}

func (p BookParams) GetSymbol() string {
	return p.Symbol
}
