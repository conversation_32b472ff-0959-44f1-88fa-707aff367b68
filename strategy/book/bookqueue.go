package book

import (
	"GoTrader/pkg/data"
	"GoTrader/pkg/utils"
	"fmt"
	"math"
	"sort"
)

// 订单簿队列
type BookQueue struct {
	bookMap   map[int]data.OrderBook
	minValue  int
	maxLen    int
	tolerance int
}

func NewBookQueue(maxLen int, tolerance int) BookQueue {
	return BookQueue{
		bookMap:   map[int]data.OrderBook{},
		minValue:  math.MaxInt,
		maxLen:    maxLen,
		tolerance: tolerance,
	}
}

func (bq *BookQueue) getKey(o data.OrderBook) int {
	return utils.FloorIntToStep(int(o.Price*o.Size), bq.tolerance)
}

func (bq *BookQueue) updateMinValue() {
	if len(bq.bookMap) == 0 {
		return
	}
	bq.minValue = math.MaxInt
	for key := range bq.bookMap {
		if key < bq.minValue {
			bq.minValue = key
		}
	}
}

func (bq *BookQueue) Push(o data.OrderBook) {
	key := bq.getKey(o)
	if len(bq.bookMap) >= bq.maxLen && key > bq.minValue {
		delete(bq.bookMap, bq.minValue)
		bq.bookMap[key] = o
		bq.updateMinValue()
		return
	}
	if len(bq.bookMap) < bq.maxLen {
		bq.bookMap[key] = o
		bq.updateMinValue()
	}
}

func (bq *BookQueue) Min() (int, data.OrderBook, error) {
	if len(bq.bookMap) == 0 {
		return 0, data.OrderBook{}, fmt.Errorf("book queue is empty")
	}
	return bq.minValue, bq.bookMap[bq.minValue], nil
}

func (bq *BookQueue) AllValues() []int {
	values := []int{}
	for key := range bq.bookMap {
		values = append(values, key)
	}
	sort.Ints(values)
	return values
}
