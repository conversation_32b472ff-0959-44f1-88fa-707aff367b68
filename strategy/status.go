package strategy

import "fmt"

type TaskStatus int

const (

	// 可开平仓状态
	Active TaskStatus = iota + 1 // 活跃

	// 只平仓状态
	MMRWarning // MMR预警
	Reduce     // 只减仓

	// 平仓状态
	MMRClosing // MMR减仓中

	Stop // 全平仓中, 后续会替换为Closing

	// 中断状态
	Pause // 终止， 后续会替换为Break
)

// 定义正向映射
var statusNames = []string{
	"", // iota 从 1 开始，因此 0 填空字符串
	"active",

	"mmr_warning",
	"reduce",

	"mmr_closing",
	"stop",
	"pause",
}

// 实现 String() 方法（正向转换）
func (s TaskStatus) String() string {
	if int(s) > 0 && int(s) < len(statusNames) {
		return statusNames[s]
	}
	return fmt.Sprintf("unknown status (%d)", s)
}

// 解析字符串到 TaskStatus（反向转换）
func ParseTaskStatus(s string) (TaskStatus, error) {
	for i, name := range statusNames {
		if name == s {
			return TaskStatus(i), nil
		}
	}
	return 0, fmt.<PERSON>rrorf("invalid TaskStatus: %s", s)
}
