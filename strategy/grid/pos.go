package grid

import (
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	"fmt"
	"math"
)

// 仓位管理
// 通过保存的订单, 设置价格和当前价格, 判断是否需要下单, 修改订单, 撤单
type GridPosManager struct {
	openPrice      float64               // 开仓价格
	closePrice     float64               // 平仓价格
	priceTolerance float64               // 价格容忍偏离度
	sizeTolerance  float64               // 仓位容忍偏离度
	size           float64               // 管理仓位
	reduce         bool                  // 是否只减仓
	orders         []*order.FuturesOrder // 订单
}

func (p *GridPosManager) String() string {
	return fmt.Sprintf("openPrice: %.6f, closePrice: %.6f, size: %.6f, currSize: %.6f",
		p.openPrice, p.closePrice, p.size, p.SizeLeft())
}

func NewGridPosManager(buyPrice, sellPrice, size, priceTolerance, sizeTolerance float64) (*GridPosManager, error) {
	if sellPrice < buyPrice {
		return nil, fmt.Errorf("invalid price range: buyPrice %f, sellPrice %f, ", buyPrice, sellPrice)
	}
	return &GridPosManager{
		openPrice:      buyPrice,
		closePrice:     sellPrice,
		priceTolerance: priceTolerance,
		sizeTolerance:  sizeTolerance,
		reduce:         false,
		size:           size,
	}, nil
}

// 是否为同一个仓位管理
func (p *GridPosManager) IsEqual(openPrice, size float64) bool {
	return math.Abs(p.openPrice-openPrice) < p.priceTolerance && math.Abs(p.size-size) < p.sizeTolerance
}

// 剩余仓位
func (p *GridPosManager) SizeLeft() float64 {
	sizeLeft := 0.0
	for _, o := range p.orders {
		size := o.GetExecutedSize()
		if o.IsBuy() {
			sizeLeft += size
		} else {
			sizeLeft -= size
		}
	}
	return sizeLeft
}

// 剩余占用
func (p *GridPosManager) ValueOccupied(currPrice, leverage float64) float64 {
	sizeOccupied := currPrice * math.Abs(p.SizeLeft()) / leverage
	orderOccupied := 0.0
	for _, o := range p.orders {
		if o.Alive() && o.IsOpen() {
			orderOccupied += o.Price * (o.GetSize() - o.GetExecutedSize()) / leverage
		}
	}
	return sizeOccupied + orderOccupied
}

// 添加订单, 整理历史订单
func (p *GridPosManager) AppendOrder(o *order.FuturesOrder) {
	p.orders = append(p.orders, o)
	cumSize := 0.0
	newOrders := []*order.FuturesOrder{}
	for i, o := range p.orders {
		if o.Alive() {
			newOrders = append(newOrders, p.orders[i:]...)
			break
		}
		executedSize := o.GetExecutedSize()
		if o.IsBuy() {
			cumSize += executedSize
		} else {
			cumSize -= executedSize
		}
		if math.Abs(cumSize) < p.sizeTolerance {
			newOrders = []*order.FuturesOrder{}
		}
	}
	p.orders = newOrders
}

func (p *GridPosManager) ActiveOrder() []*order.FuturesOrder {
	active := []*order.FuturesOrder{}
	for _, o := range p.orders {
		if o.Alive() {
			active = append(active, o)
		}
	}
	return active
}

func (p *GridPosManager) ActiveOpenOrder() []*order.FuturesOrder {
	active := []*order.FuturesOrder{}
	for _, o := range p.orders {
		if o.Alive() && o.IsOpen() {
			active = append(active, o)
		}
	}
	return active
}

func (p *GridPosManager) ActiveCloseOrder() []*order.FuturesOrder {
	active := []*order.FuturesOrder{}
	for _, o := range p.orders {
		if o.Alive() && o.IsClose() {
			active = append(active, o)
		}
	}
	return active
}

func (p *GridPosManager) ShouldBuy() float64 {
	return p.size - p.SizeLeft()
}

func (p *GridPosManager) ShouldSell() float64 {
	return p.SizeLeft()
}

func (p *GridPosManager) Active() {
	p.reduce = false
}

func (p *GridPosManager) Reduce() {
	p.reduce = true
}

func (p *GridPosManager) IsClear() bool {
	return p.SizeLeft() < p.sizeTolerance && p.reduce && len(p.ActiveOrder()) == 0
}

// 根据当前价格判断需要下单, 修改订单, 撤单
func (p *GridPosManager) ShouldDo(currPrice float64, close bool, delta float64) (submit *order.FuturesOrder, cancel []*order.FuturesOrder) {
	// 强制平仓时, 撤销所有开仓单, 下止损单
	if close {
		cancel = append(cancel, p.ActiveOpenOrder()...)
		if len(cancel) > 0 {
			return
		}
		size := p.SizeLeft()
		if size > p.sizeTolerance {
			submit = order.CreateFuturesOrder().SetClOrdId("forceClose").SetPrice(utils.RoundToStep(currPrice-delta, p.priceTolerance)).
				SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
		}
		if size < -p.sizeTolerance {
			submit = order.CreateFuturesOrder().SetClOrdId("forceClose").SetPrice(utils.RoundToStep(currPrice+delta, p.priceTolerance)).
				SetSize(utils.RoundToStep(-size, p.sizeTolerance)).SetSide(order.BUY).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
		}
		return
	}

	// 只减仓时, 撤销所有开仓单
	if p.reduce {
		cancel = append(cancel, p.ActiveOpenOrder()...)
	}

	// 做多
	if p.size > 0 {
		posSide := order.LONG
		// 价格高于卖出价格时, 理论上平仓全部成交, 下开仓单
		if currPrice > p.closePrice && !p.reduce {
			if size := p.ShouldBuy(); size > p.sizeTolerance {
				cancel = append(cancel, p.ActiveCloseOrder()...)
				if len(cancel) > 0 {
					return
				}
				submit = order.CreateFuturesOrder().SetClOrdId("open").SetPrice(utils.RoundToStep(p.openPrice, p.priceTolerance)).
					SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.BUY).SetPosSide(posSide).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
			}
			return
		}

		// 价格低于买入价格时, 理论上开仓全部成交, 下平仓单
		if currPrice < p.openPrice {
			if size := p.ShouldSell(); size > p.sizeTolerance {
				cancel = append(cancel, p.ActiveOpenOrder()...)
				if len(cancel) > 0 {
					return
				}
				submit = order.CreateFuturesOrder().SetClOrdId("close").SetPrice(utils.RoundToStep(p.closePrice, p.priceTolerance)).
					SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.SELL).SetPosSide(posSide).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
			}
			return
		}
		return
	}

	// 做空
	if p.size < 0 {
		posSide := order.SHORT
		// 价格低于卖出价格时, 理论上平仓全部成交, 下开仓单
		if currPrice < p.closePrice && !p.reduce {
			if size := p.ShouldBuy(); size > p.sizeTolerance {
				cancel = append(cancel, p.ActiveCloseOrder()...)
				if len(cancel) > 0 {
					return
				}
				submit = order.CreateFuturesOrder().SetClOrdId("open").SetPrice(utils.RoundToStep(p.openPrice, p.priceTolerance)).
					SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.SELL).SetPosSide(posSide).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
			}
			return
		}

		// 价格高于买入价格时, 理论上开仓全部成交, 下平仓单
		if currPrice > p.openPrice {
			if size := p.ShouldSell(); size > p.sizeTolerance {
				cancel = append(cancel, p.ActiveOpenOrder()...)
				if len(cancel) > 0 {
					return
				}
				submit = order.CreateFuturesOrder().SetClOrdId("close").SetPrice(utils.RoundToStep(p.closePrice, p.priceTolerance)).
					SetSize(utils.RoundToStep(size, p.sizeTolerance)).SetSide(order.BUY).SetPosSide(posSide).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC)
			}
			return
		}
		return
	}
	return
}
