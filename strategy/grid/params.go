package grid

import (
	"GoTrader/strategy"
	"strconv"
)

// 策略参数
type GridParams struct {
	ID           string              // 任务ID
	Symbol       string              // 交易品种
	Leverage     int64               // 杠杆
	Exchange     string              // 交易所
	CapitalLimit float64             // 资金分配上限
	PriceLevel   int64               // 价格档位数量
	PriceUpper   float64             // 价格上限
	PriceLower   float64             // 价格下限
	TriggerPrice float64             // 触发价格
	MinAvailable float64             // 最小可用余额
	Status       strategy.TaskStatus // 状态
}

func (p GridParams) String() string {
	return "ID: " + p.ID + ", Symbol: " + p.Symbol +
		", Leverage: " + strconv.FormatInt(p.Leverage, 10) +
		", Exchange: " + p.Exchange +
		", PriceLevel: " + strconv.FormatInt(p.PriceLevel, 10) +
		", CapitalLimit: " + strconv.FormatFloat(p.CapitalLimit, 'f', 6, 64) +
		", MinAvailable: " + strconv.FormatFloat(p.MinAvailable, 'f', 6, 64) +
		", Status: " + p.Status.String()
}

func (p GridParams) GetID() string {
	return p.ID
}

func (p GridParams) GetLeverage() int64 {
	return p.Leverage
}

func (p GridParams) GetSymbol() string {
	return p.Symbol
}
