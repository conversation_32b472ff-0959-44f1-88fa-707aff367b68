package grid

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/order"
	"GoTrader/pkg/queue"
	"GoTrader/pkg/utils"
	"GoTrader/strategy"
	"fmt"
	"log/slog"
	"math"
	"time"
)

// 网格策略
type GridStrategy struct {
	Data   *data.SymbolData
	broker *broker.Broker
	client client.APIClient

	_startFlag  bool
	_startPrice float64

	// 单个仓位
	stepSize float64

	// 价格网格
	priceGrid *queue.Deque[float64]
	// 订单网格
	orderGrid *queue.Deque[*order.FuturesOrder]

	// 价格上限
	priceUpper float64
	// 价格下限
	priceLower float64

	// 中性位置
	midIdx int

	// 状态定时器
	statusTimer *utils.DoTimer

	// 通知定时器
	notifyTimer *utils.DoTimer

	Params GridParams
	// 任务状态
	status strategy.TaskStatus
	// 停止channel
	stopChan chan struct{}
	// 展示信息, 用于接受策略生成信息
	redisStatusChan chan []byte
	// redis上下文
	redisCtx *strategy.RedisCtx
	// redis hset channel, 用于发送展示信息
	redisHsetChan chan map[string]string
	// 监控心跳
	monitorTicker *time.Ticker
	// 监控channel
	monitorChan chan struct{}
	logger      *slog.Logger
}

func NewGridStrategy(
	data *data.SymbolData, broker *broker.Broker, client client.APIClient,
	params GridParams, redisCtx *strategy.RedisCtx, hsetChan chan map[string]string,
	logger *slog.Logger,
) *GridStrategy {
	logger = logger.With(slog.String("strategy", "GridStrategy")).With(slog.String("symbol", params.Symbol)).With(slog.String("ID", params.ID))
	return &GridStrategy{
		Data:   data,
		broker: broker,
		client: client,
		Params: params,

		stepSize:   0,
		priceUpper: params.PriceUpper,
		priceLower: params.PriceLower,

		priceGrid: queue.NewDeque[float64](int(params.PriceLevel), false),
		orderGrid: queue.NewDeque[*order.FuturesOrder](int(params.PriceLevel), false),
		midIdx:    -1,

		statusTimer: &utils.DoTimer{Interval: time.Second * 10},
		notifyTimer: &utils.DoTimer{Interval: time.Minute * 5},

		status:          params.Status,
		stopChan:        make(chan struct{}),
		redisStatusChan: make(chan []byte),
		redisCtx:        redisCtx,
		redisHsetChan:   hsetChan,
		monitorTicker:   time.NewTicker(time.Second * 300),
		monitorChan:     make(chan struct{}, 1),
		logger:          logger,
	}
}

func (s *GridStrategy) Start() {
	// 初始化broker
	strategy.InitBroker(s.Data, s.broker, s.client, s.Params, s.logger)

	if !strategy.BrokerValidCheck(s.Data, s.broker, s.logger) {
		s.logger.Error("broker有效性检查失败, 停止运行")
		s.Pause()
		return
	}

	// 获取初始价格
	s._startPrice = s.Data.Bid1Price()

	// 撤销现有订单
	for _, o := range s.broker.GetFuturesOrders(s.Data.Symbol) {
		s.client.CancelFuturesOrder(s.broker, o)
	}

	// 数据源检测
	go strategy.DataMonitor(s.Data, s.stopChan, s.monitorChan, s.next, s.logger)

	// 心跳检查
	go strategy.Heartbeat(s.stopChan, s.monitorChan, s.monitorTicker, s.logger)

	// 写入redis
	go strategy.WriteRedis(s.redisCtx, s.Params, s.stopChan, s.redisStatusChan, s.redisHsetChan, s.logger)

	s.logger.Info("初始化完毕")
}

func (s *GridStrategy) next() {
	// 强制平仓状态, 无仓位, 无挂单退出
	// TODO 需要处理人为开仓
	if s.status == strategy.Stop && s.broker.GetDataSize(s.Data) == 0 && !s.broker.HasFuturesPending(s.Data.Symbol) {
		s.logger.Info("任务已平掉所有仓位, 关闭任务")
		s.Pause()
		return
	}

	// 检查价格超过设定值
	if s.Data.Ask1Price() > s.Params.PriceUpper || s.Data.Bid1Price() < s.Params.PriceLower {
		s.logger.Warn("价格超过设定值, 停止运行", "价格", s.Data.Ask1Price(), "上限", s.Params.PriceUpper, "下限", s.Params.PriceLower)
		s.Pause()
		return
	}

	if !s.triggerStart() {
		return
	}

	s.updateGrid()

	s.placeOrder()

	s.statusTimer.Do(func() {
		s.logger.Info("Status", "priceGrid", fmt.Sprintf("%+v", s.priceGrid), "midIdx", s.midIdx, "currPrice", s.Data.Bid1Price())
	})

	s.priceNotify()

}

func (s *GridStrategy) triggerStart() bool {
	// 已经触发过
	if s._startFlag {
		return true
	}
	// 已经存在订单或仓位
	if s.broker.GetDataSize(s.Data) != 0 {
		s.initGrid()
		s._startFlag = true
		return true
	}
	// 价格触发
	if s.Params.TriggerPrice == 0 || (s.Data.Bid1Price()-s.Params.TriggerPrice)*(s._startPrice-s.Params.TriggerPrice) < 0 {
		s.initGrid()
		s._startFlag = true
		return true
	}
	return false
}

func (s *GridStrategy) initGrid() {
	// 价格步进
	priceDeltaStep := math.Ceil((s.priceUpper - s.priceLower) / float64(s.Params.PriceLevel) / s.Data.Info.TickSize)

	// 价格网格
	for price := s.priceLower; price <= s.priceUpper; price += priceDeltaStep * s.Data.Info.TickSize {
		s.priceGrid.Append(price)
	}
	// 计算单个仓位, 按照最大价格计算
	maxPrice, _ := s.priceGrid.At(s.priceGrid.Len() - 1)
	s.stepSize = utils.RoundToStep(s.Params.CapitalLimit*s.broker.DataLeverage(s.Data)/float64(s.Params.PriceLevel)/maxPrice, s.Data.Info.StepSize*s.Data.Info.CtVal)
	if s.stepSize < s.Data.Info.MinSize*s.Data.Info.CtVal {
		s.logger.Info("计算仓位过小, 检查金额和价格档位", "stepSize", s.stepSize, "minSize", s.Data.Info.MinSize)
		s.logger.Info("该档位最小金额", "minAmount", maxPrice*s.Data.Info.MinSize*float64(s.Params.PriceLevel)*s.Data.Info.CtVal/s.broker.DataLeverage(s.Data))
		return
	}

	// 中性位置
	currIdx := 0
	currPrice := (s.Data.Bid1Price() + s.Data.Ask1Price()) / 2
	currSize := s.broker.GetDataSize(s.Data)
	for i := range s.priceGrid.Len() {
		p, _ := s.priceGrid.At(i)
		if math.Abs(p-currPrice) <= priceDeltaStep*s.Data.Info.TickSize/2 {
			currIdx = i
			break
		}
	}
	// 未持仓时按照当前价格计算
	if currSize == 0 {
		s.midIdx = currIdx
	}
	// 已持仓时按照持仓量计算
	// 由于可能存在的计算误差, 最终中性价格上可能会多最多一个网格的仓位
	if currSize > 0 {
		s.midIdx = currIdx + int(math.Floor(currSize/s.stepSize))
		if s.midIdx < 0 {
			s.logger.Info("持仓量超过最大网格, 中性价格按照最小值计算")
			s.midIdx = 0
		}
	}
	if currSize < 0 {
		s.midIdx = currIdx - int(math.Floor(-currSize/s.stepSize))
		if s.midIdx >= s.priceGrid.Len() {
			s.logger.Info("持仓量超过最大网格, 中性价格按照最大值计算")
			s.midIdx = s.priceGrid.Len() - 1
		}
	}

	// 已持仓且有触发价格时, 需要调整仓位至触发价格
	if currSize != 0 && s.Params.TriggerPrice != 0 {
		calcSize := float64(currIdx-s.midIdx) * s.stepSize
		s.adjustCurrSize(calcSize, currSize)
	}

	// 初始化订单
	for i := range s.priceGrid.Len() {
		p, _ := s.priceGrid.At(i)
		switch {
		case i < s.midIdx && p < currPrice:
			s.orderGrid.Append(order.CreateFuturesOrder().SetSymbol(s.Data.Symbol).SetClOrdId("buy").
				SetPrice(p).SetSize(s.stepSize).SetSide(order.BUY).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC))
		case i > s.midIdx && p > currPrice:
			s.orderGrid.Append(order.CreateFuturesOrder().SetSymbol(s.Data.Symbol).SetClOrdId("sell").
				SetPrice(p).SetSize(s.stepSize).SetSide(order.SELL).SetPosSide(order.SHORT).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC))

		case i < s.midIdx && p > currPrice:
			s.orderGrid.Append(order.CreateFuturesOrder().SetSymbol(s.Data.Symbol).SetClOrdId("sell").
				SetPrice(p).SetSize(s.stepSize).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC))
		case i > s.midIdx && p < currPrice:
			s.orderGrid.Append(order.CreateFuturesOrder().SetSymbol(s.Data.Symbol).SetClOrdId("buy").
				SetPrice(p).SetSize(s.stepSize).SetSide(order.BUY).SetPosSide(order.SHORT).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC))
		default:
			s.orderGrid.Append(nil)
		}
	}

	s.logger.Info("初始化订单完成")

}

func (s *GridStrategy) updateGrid() {
	newOrderGrid := queue.NewDeque[*order.FuturesOrder](int(s.Params.PriceLevel), false)
	newOrderGrid.Fill(nil)

	// 对订单数量和价格进行限制
	for i := range s.priceGrid.Len() {
		oldOrder, _ := s.orderGrid.At(i)
		// 当前价格位置, 不做处理
		if oldOrder == nil {
			continue
		}
		// 活跃订单或创建订单保持
		if oldOrder.Alive() || oldOrder.IsCreated() {
			newOrderGrid.SetAt(i, oldOrder)
			continue
		}
		// 已完成订单需要对应下单
		if oldOrder.IsBuy() && oldOrder.IsLong() {
			idx := i + 1
			price, _ := s.priceGrid.At(idx)
			newOrderGrid.SetAt(idx, order.CreateFuturesOrder().SetSymbol(s.Data.Symbol).SetClOrdId("sell").
				SetPrice(price).SetSize(s.stepSize).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC))
			continue
		}
		if oldOrder.IsSell() && oldOrder.IsShort() {
			idx := i - 1
			price, _ := s.priceGrid.At(idx)
			newOrderGrid.SetAt(idx, order.CreateFuturesOrder().SetSymbol(s.Data.Symbol).SetClOrdId("buy").
				SetPrice(price).SetSize(s.stepSize).SetSide(order.BUY).SetPosSide(order.SHORT).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC))
			continue
		}
		if oldOrder.IsBuy() && oldOrder.IsShort() {
			idx := i + 1
			price, _ := s.priceGrid.At(idx)
			newOrderGrid.SetAt(idx, order.CreateFuturesOrder().SetSymbol(s.Data.Symbol).SetClOrdId("sell").
				SetPrice(price).SetSize(s.stepSize).SetSide(order.SELL).SetPosSide(order.SHORT).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC))
			continue
		}
		if oldOrder.IsSell() && oldOrder.IsLong() {
			idx := i - 1
			price, _ := s.priceGrid.At(idx)
			newOrderGrid.SetAt(idx, order.CreateFuturesOrder().SetSymbol(s.Data.Symbol).SetClOrdId("buy").
				SetPrice(price).SetSize(s.stepSize).SetSide(order.BUY).SetPosSide(order.LONG).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC))
			continue
		}
	}
	s.orderGrid = newOrderGrid
}

func (s *GridStrategy) placeOrder() {
	// 价格需要满足限价, 数量需要满足上下3个一共6个订单
	currIdx := 0
	currPrice := (s.Data.Bid1Price() + s.Data.Ask1Price()) / 2
	for i := range s.priceGrid.Len() {
		p, _ := s.priceGrid.At(i)
		if p > currPrice {
			currIdx = i
			break
		}
	}

	for i := range s.orderGrid.Len() {
		o, _ := s.orderGrid.At(i)
		// 未创建订单跳过
		if o == nil || o.Status != order.CREATED {
			continue
		}
		// 价格不在当前价格附近
		if i < currIdx-3 || i > currIdx+3 {
			continue
		}
		_, err := s.client.SubmitFuturesOrder(s.broker, o, s.Data.Info)
		if err != nil {
			s.logger.Error("下单失败", "order", o, "error", err)
			continue
		}
		s.logger.Info("下单成功", "order", o)
	}
}

func (s *GridStrategy) adjustCurrSize(calcSize, currSize float64) {
	delta := calcSize - currSize
	if math.Abs(delta) < s.stepSize {
		s.logger.Info("仓位调整过小, 忽略操作", "currSize", currSize, "calcSize", calcSize, "stepSize", s.stepSize)
		return
	}

	// 1️⃣ 判断是否需要先平仓
	if currSize*calcSize < 0 { // 符号相反，先平掉现有仓位
		var side order.Side
		var pos order.PosSide
		var price float64

		if currSize > 0 {
			// 平多仓
			side = order.SELL
			pos = order.LONG
			price = s.Data.BidN(5).Price
		} else {
			// 平空仓
			side = order.BUY
			pos = order.SHORT
			price = s.Data.AskN(5).Price
		}

		size := utils.RoundToStep(math.Abs(currSize), s.Data.Info.StepSize*s.Data.Info.CtVal)

		_, err := s.client.SubmitFuturesOrder(
			s.broker,
			order.CreateFuturesOrder().SetSymbol(s.Data.Symbol).SetClOrdId("adjust").
				SetPrice(price).SetSize(size).SetSide(side).SetPosSide(pos).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC),
			s.Data.Info,
		)
		if err != nil {
			s.logger.Error("平仓失败", "side", side, "size", size, "error", err)
		} else {
			s.logger.Info("平仓成功", "side", side, "size", size)
		}

		// 更新 currSize 和 delta
		currSize = 0
		delta = calcSize
	}

	// 2️⃣ 剩余 delta 做开仓/加仓
	if math.Abs(delta) >= s.stepSize {
		var side order.Side
		var pos order.PosSide
		var price float64

		if delta > 0 {
			// 开多仓或减空仓
			side = order.BUY
			pos = order.LONG
			price = s.Data.AskN(5).Price
		} else {
			// 平多仓或开空仓
			side = order.SELL
			pos = order.SHORT
			price = s.Data.BidN(5).Price
			delta = -delta
		}

		size := utils.RoundToStep(math.Abs(delta), s.Data.Info.StepSize*s.Data.Info.CtVal)

		_, err := s.client.SubmitFuturesOrder(
			s.broker,
			order.CreateFuturesOrder().SetSymbol(s.Data.Symbol).SetClOrdId("adjust").
				SetPrice(price).SetSize(size).SetSide(side).SetPosSide(pos).SetOrdType(order.LIMIT).SetTimeInForce(order.GTC),
			s.Data.Info,
		)
		if err != nil {
			s.logger.Error("开仓/加仓失败", "side", side, "size", size, "error", err)
		} else {
			s.logger.Info("开仓/加仓成功", "side", side, "size", size)
		}
	}
}

func (s *GridStrategy) priceNotify() {
	// 价格逼近上下限时发出通知
	notifyLowerPrice := 0.0
	notifyUpperPrice := 0.0
	if s.Params.PriceLevel > 5 {
		notifyLowerPrice, _ = s.priceGrid.At(1)
		notifyUpperPrice, _ = s.priceGrid.At(int(s.Params.PriceLevel) - 2)
	} else {
		notifyLowerPrice, _ = s.priceGrid.At(0)
		notifyUpperPrice, _ = s.priceGrid.At(int(s.Params.PriceLevel) - 1)
	}

	if s.Data.Ask1Price() > notifyUpperPrice {
		s.notifyTimer.Do(func() {
			s.logger.Warn("价格逼近上限, 请关注", "价格", s.Data.Ask1Price(), "上限", notifyUpperPrice)
		})
		return
	}
	if s.Data.Bid1Price() < notifyLowerPrice {
		s.notifyTimer.Do(func() {
			s.logger.Warn("价格逼近下限, 请关注", "价格", s.Data.Bid1Price(), "下限", notifyLowerPrice)
		})
		return
	}
	if s.Data.Ask1Price() > s.Params.PriceUpper {
		s.notifyTimer.Do(func() {
			s.logger.Warn("价格超过上限, 请关注", "价格", s.Data.Ask1Price(), "上限", s.Params.PriceUpper)
		})
		return
	}
	if s.Data.Bid1Price() < s.Params.PriceLower {
		s.notifyTimer.Do(func() {
			s.logger.Warn("价格跌破下限, 请关注", "价格", s.Data.Bid1Price(), "下限", s.Params.PriceLower)
		})
		return
	}
}

// 暂停任务运行
func (stra *GridStrategy) Pause() {
	select {
	case <-stra.stopChan:
		stra.logger.Info("stop trigger already sent")
	default:
		close(stra.stopChan)
		stra.logger.Info("stop trigger sent")
	}
}

// 停止任务并平仓
func (stra *GridStrategy) Stop() {
	if stra.status < strategy.Stop {
		stra.logger.Info("status stop")
		stra.status = strategy.Stop
	}
}

// 是否正在运行
func (stra *GridStrategy) Running() bool {
	select {
	case <-stra.stopChan:
		return false
	default:
		return true
	}
}

// 当前状态
func (stra *GridStrategy) Status() strategy.TaskStatus {
	return stra.status
}

// 减仓
func (stra *GridStrategy) Reduce() {
	if stra.status < strategy.Reduce {
		stra.status = strategy.Reduce
	}
}
