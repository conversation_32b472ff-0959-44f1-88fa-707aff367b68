package strategy

import (
	"context"
	"log/slog"
	"time"

	"github.com/redis/go-redis/v9"
)

// 通用类型

// redis配置
type RedisCtx struct {
	Client *redis.Client
	Table  string
}

// redis写入协程
func WriteRedis(redisCtx *RedisCtx, params Params, stopChan chan struct{}, redisStatusChan chan []byte, hset<PERSON>han chan map[string]string, logger *slog.Logger) {
	logger.Info("启动写入redis")
	// 写入redis
	if redisCtx != nil {
		func() {
			ticker := time.NewTicker(time.Second)
			defer ticker.Stop()
			for {
				select {
				case <-stopChan:
					logger.Info("Received signal to stop update redis")
					return
				case <-ticker.C:
					writeRedis(redisCtx, params, redisStatusChan, hsetChan, logger)
				}
			}
		}()
	}
}

// 写入redis
func writeRedis(redisCtx *RedisCtx, params Params, redisStatus<PERSON>han chan []byte, hset<PERSON><PERSON> chan map[string]string, logger *slog.Logger) {
	// 从chan中获取数据
	jsonstring := <-redisStatusChan
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	if hsetChan == nil {
		// 单独写
		err := redisCtx.Client.HSet(ctx, redisCtx.Table, map[string]string{
			params.GetID(): string(jsonstring),
		}).Err()

		if err != nil {
			logger.Warn("write redis failed", "error", err)
		}
		return
	}

	// 批量写
	select {
	case hsetChan <- map[string]string{
		params.GetID(): string(jsonstring),
	}:
	case <-ctx.Done():
		logger.Warn("send redis hset channel timeout")
		return
	}
}
