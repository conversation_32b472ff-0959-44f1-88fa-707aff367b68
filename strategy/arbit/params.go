package arbit

import (
	"GoTrader/pkg/data"
	"GoTrader/strategy"
	"strconv"
)

// 策略参数
type ArbitrageParams struct {
	ID               string        // 任务ID
	Symbol           string        // 交易品种
	Leverage         int64         // 杠杆
	AutoSeconds      int64         // 自动调整价差秒数, 小于等于0表示不调整
	LeftType         data.DataType // 左腿数据类型
	LeftExchange     string        // 左腿交易所
	RightType        data.DataType // 右腿数据类型
	RightExchange    string        // 右腿交易所
	OpenDiffLow      float64       // 开仓价差(低档位)
	OpenDiffHigh     float64       // 开仓价差(高档位)
	CloseDiff        float64       // 平仓价差
	OnceOpenValue    float64       // 单次开仓价值
	MinAvailable     float64       // 最小可用余额
	ProfitSpaceLow   float64       // 价差空间(低档位)
	ProfitSpaceHigh  float64       // 价差空间(高档位)
	CapitalLimitLow  float64       // 资金分配上限(低档位)
	CapitalLimitHigh float64       // 资金分配上限(高档位)

	Status strategy.TaskStatus // 状态
	Extra  ExtraParams         // 扩展参数
}

// 扩展参数
type ExtraParams struct {
	RightOpenDiff  float64 // 同所价差开仓
	RightCloseDiff float64 // 同所价差平仓
}

func (p ExtraParams) String() string {
	return "RightOpenDiff: " + strconv.FormatFloat(p.RightOpenDiff, 'f', 6, 64) +
		", RightCloseDiff: " + strconv.FormatFloat(p.RightCloseDiff, 'f', 6, 64)
}

func (p ArbitrageParams) String() string {
	return "ID: " + p.ID + ", Symbol: " + p.Symbol +
		", Leverage: " + strconv.FormatInt(p.Leverage, 10) +
		", AutoSeconds: " + strconv.FormatInt(p.AutoSeconds, 10) +
		", LeftType: " + string(p.LeftType) + ", RightType: " + string(p.RightType) +
		", OpenDiffLow: " + strconv.FormatFloat(p.OpenDiffLow, 'f', 6, 64) +
		", OpenDiffHigh: " + strconv.FormatFloat(p.OpenDiffHigh, 'f', 6, 64) +
		", CloseDiff: " + strconv.FormatFloat(p.CloseDiff, 'f', 6, 64) +
		", OnceOpenValue: " + strconv.FormatFloat(p.OnceOpenValue, 'f', 6, 64) +
		", MinAvailable: " + strconv.FormatFloat(p.MinAvailable, 'f', 6, 64) +
		", ProfitSpaceLow: " + strconv.FormatFloat(p.ProfitSpaceLow, 'f', 6, 64) +
		", ProfitSpaceHigh: " + strconv.FormatFloat(p.ProfitSpaceHigh, 'f', 6, 64) +
		", CapitalLimitLow: " + strconv.FormatFloat(p.CapitalLimitLow, 'f', 6, 64) +
		", CapitalLimitHigh: " + strconv.FormatFloat(p.CapitalLimitHigh, 'f', 6, 64) +
		", Status: " + p.Status.String() +
		", Extra: " + p.Extra.String()
}

func (p ArbitrageParams) GetID() string {
	return p.ID
}

func (p ArbitrageParams) GetLeverage() int64 {
	return p.Leverage
}

func (p ArbitrageParams) GetSymbol() string {
	return p.Symbol
}
