package arbit

import (
	"GoTrader/pkg/data"
	"encoding/csv"
	"log/slog"
	"strconv"
)

type DiffStore struct {
	/*
		价差记录
	*/
	// 左腿
	LeftData *data.SymbolData
	// 右腿
	RightData *data.SymbolData
	// 文件
	csvWriter *csv.Writer
	// 日志
	logger *slog.Logger
	// 停止channel
	stopChan chan struct{}
}

func NewDiffStore(
	ld *data.SymbolData, rd *data.SymbolData,
	csvWriter *csv.Writer,
	logger *slog.Logger,
) *DiffStore {
	return &DiffStore{
		LeftData:  ld,
		RightData: rd,
		csvWriter: csvWriter,
		logger:    logger,
		stopChan:  make(chan struct{}),
	}
}

func (stra *DiffStore) Start() {
	// 检测数据源
	go func() {
		stra.logger.Debug("开始运行")
		for {
			select {
			case <-stra.stopChan:
				stra.logger.Debug("停止运行")
				return
			case <-stra.LeftData.TriggerChan:
				stra.next("left")
			case <-stra.RightData.TriggerChan:
				stra.next("right")
			}
		}
	}()
}

func (stra *DiffStore) Stop() {
	select {
	case <-stra.stopChan:
		stra.logger.Debug("stop trigger already sent")
		return
	default:
		close(stra.stopChan)
		stra.logger.Debug("stop trigger sent")
	}
	stra.csvWriter.Flush()
}

func (stra *DiffStore) next(leg string) {
	// TODO
	leftPrice := stra.LeftData.Bid1Price()
	leftTs := int64(stra.LeftData.Time.Last())
	rightPrice := stra.RightData.Ask1Price()
	rightTs := int64(stra.RightData.Time.Last())
	var ts string
	if leg == "left" {
		// 整数转换为字符串
		ts = strconv.FormatInt(leftTs, 10)
	} else {
		ts = strconv.FormatInt(rightTs, 10)
	}

	// 价差, 绝对值
	diff := strconv.FormatFloat((leftPrice-rightPrice)/rightPrice, 'f', 10, 64)

	dataLine := []string{
		ts,
		diff,
	}
	// 写入csv
	err := stra.csvWriter.Write(dataLine)
	if err != nil {
		stra.logger.Error("写入csv失败", "error", err)
	}
}

// 是否正在运行
func (stra *DiffStore) Running() bool {
	select {
	case <-stra.stopChan:
		return false
	default:
		return true
	}
}
