// 套利
package arbit

import (
	config "GoTrader/internal"
	"GoTrader/strategy"
	"log/slog"
	"time"
)

type ArbitrageBase struct {
	/*
		套利策略Base
	*/
	// 左腿
	LeftLeg *ArbitLeg
	// 右腿
	RightLeg *ArbitLeg
	// 设定参数
	Params ArbitrageParams
	// 任务状态
	status strategy.TaskStatus
	// 停止channel
	stopChan chan struct{}
	// 展示信息, 用于接受策略生成信息
	redisStatusChan chan []byte
	// redis上下文
	redisCtx *strategy.RedisCtx
	// redis hset channel, 用于发送展示信息
	redisHsetChan chan map[string]string
	// 监控心跳
	monitorTicker *time.Ticker
	// 监控channel
	monitorChan chan struct{}
	// 日志
	logger *slog.Logger
	// location
	location *time.Location
}

func (stra *ArbitrageBase) Start() {
	// 更新时区
	stra.location, _ = time.LoadLocation(config.TimeZone)

	// 初始化broker
	InitBroker(stra.Params, stra.LeftLeg, stra.RightLeg, stra.logger)

	if !BrokerValidCheck(stra.LeftLeg, stra.RightLeg, stra.logger) {
		stra.logger.Error("broker有效性检查失败, 停止运行")
		stra.Pause()
		return
	}

	// 数据源检测
	go DataMonitor(stra.LeftLeg, stra.RightLeg, stra.stopChan, stra.monitorChan, stra.next, stra.logger)

	// 心跳检查
	go Heartbeat(stra.LeftLeg, stra.RightLeg, stra.stopChan, stra.monitorChan, stra.monitorTicker, stra.logger)

	// 写入redis
	go strategy.WriteRedis(stra.redisCtx, stra.Params, stra.stopChan, stra.redisStatusChan, stra.redisHsetChan, stra.logger)

	stra.logger.Info("初始化完毕")
}

// 暂停任务运行
func (stra *ArbitrageBase) Pause() {
	select {
	case <-stra.stopChan:
		stra.logger.Info("stop trigger already sent")
	default:
		close(stra.stopChan)
		stra.logger.Info("stop trigger sent")
	}
}

// 减仓
func (stra *ArbitrageBase) Reduce() {
	if stra.status < strategy.Reduce {
		stra.status = strategy.Reduce
	}
}

// 停止任务并平仓
func (stra *ArbitrageBase) Stop() {
	if stra.status < strategy.Stop {
		stra.logger.Info("status stop")
		stra.status = strategy.Stop
	}
}

// 是否正在运行
func (stra *ArbitrageBase) Running() bool {
	select {
	case <-stra.stopChan:
		return false
	default:
		return true
	}
}

// 当前状态
func (stra *ArbitrageBase) Status() strategy.TaskStatus {
	return stra.status
}

func (stra *ArbitrageBase) next() {
	// TODO
}
