package arbit

import (
	"GoTrader/pkg/data"
	"GoTrader/pkg/utils"
	"log/slog"
	"time"
)

// redis状态信息
type RedisStatus struct {
	Diff              string `json:"diff_rate"`
	OpenDiffLow       string `json:"in_rate_tier_1"`
	OpenDiffHigh      string `json:"in_rate_tier_2"`
	CloseDiff         string `json:"out_rate"`
	LeftAccount       string `json:"left_account"`
	LeftNeedTransfer  string `json:"left_need_transfer"`
	RightAccount      string `json:"right_account"`
	RightNeedTransfer string `json:"right_need_transfer"`
	Time              string `json:"time"`
}

// 确定对照组
func BnAsBaseLeg(params *ArbitrageParams, lg, rg *ArbitLeg) (cmpLeg, baseLeg *ArbitLeg) {
	if params.LeftExchange != params.RightExchange && params.LeftExchange == "BN" {
		baseLeg = lg
		cmpLeg = rg
	} else {
		baseLeg = rg
		cmpLeg = lg
	}
	return cmpLeg, baseLeg
}

// 初始化broker
func InitBroker(params ArbitrageParams, lg, rg *ArbitLeg, logger *slog.Logger) {
	// 确定杠杆
	leverage := params.Leverage
	if leverage == 0 {
		leverage = 3
	}
	// 更新lbroker数据源相关
	logger.Info("更新rbroker订单")
	rg.Client.UpdateBrokerUMPending(rg.Broker, []*data.SymbolData{rg.Data})
	rg.Client.UpdateBrokerMarginPending(rg.Broker, []*data.SymbolData{rg.Data})
	rg.Client.UpdateFuturesLeverageSingle(rg.Broker, rg.Data)

	logger.Info("更新rbroker 仓位档位")
	rg.Client.UpdatePositionTiers(rg.Broker, rg.Data)

	// 更新rbroker数据源相关
	if lg.Broker != rg.Broker {
		logger.Info("更新lbroker订单")
		lg.Client.UpdateBrokerUMPending(lg.Broker, []*data.SymbolData{lg.Data})
		lg.Client.UpdateBrokerMarginPending(lg.Broker, []*data.SymbolData{lg.Data})
		lg.Client.UpdateFuturesLeverageSingle(lg.Broker, lg.Data)

		logger.Info("更新lbroker 仓位档位")
		lg.Client.UpdatePositionTiers(lg.Broker, lg.Data)
	}

	// 检查是否有持仓, 判断杠杆更新
	if lg.Broker.GetDataSize(lg.Data) == 0 && rg.Broker.GetDataSize(rg.Data) == 0 {
		logger.Info("设定杠杆", "leverage", leverage)
		if lg.Broker.Leverages[lg.Data.Symbol] != leverage {
			lg.Client.ModifyFuturesLeverage(lg.Broker, lg.Data, leverage)
		}
		if rg.Broker.Leverages[rg.Data.Symbol] != leverage {
			rg.Client.ModifyFuturesLeverage(rg.Broker, rg.Data, leverage)
		}
	} else {
		logger.Info("有持仓跳过修改杠杆")
	}

	// 更新张币系数
	logger.Info("更新张币系数")
	if lg.Broker.CtVals[lg.Data.Symbol] == 0 {
		lg.Client.UpdateCtVal(lg.Broker, lg.Data)
	}
	if rg.Broker.CtVals[rg.Data.Symbol] == 0 {
		rg.Client.UpdateCtVal(rg.Broker, rg.Data)
	}
}

// Broker数据有效性检查
func BrokerValidCheck(lg, rg *ArbitLeg, logger *slog.Logger) bool {

	var ctValExists, leverExists, tiersExists bool
	if lg.Data.DataType == data.Futures {
		// 张币系数检查
		_, ctValExists = lg.Broker.CtVals[lg.Data.Symbol]
		if !ctValExists {
			logger.Error("左腿张币系数不存在", "symbol", lg.Data.Symbol)
			return false
		}
		// 杠杆检查
		_, leverExists = lg.Broker.CtVals[lg.Data.Symbol]
		if !leverExists {
			logger.Error("左腿杠杆不存在", "symbol", lg.Data.Symbol)
			return false
		}

		// 档位检查
		_, tiersExists = lg.Broker.PositionTiers[lg.Data.Symbol]
		if !tiersExists {
			logger.Error("左腿档位不存在", "symbol", lg.Data.Symbol)
			return false
		}
	}
	if rg.Data.DataType == data.Futures {
		_, ctValExists = rg.Broker.CtVals[rg.Data.Symbol]
		if !ctValExists {
			logger.Error("右腿张币系数不存在", "symbol", rg.Data.Symbol)
			return false
		}

		_, leverExists = rg.Broker.CtVals[rg.Data.Symbol]
		if !leverExists {
			logger.Error("右腿杠杆不存在", "symbol", rg.Data.Symbol)
			return false
		}

		_, tiersExists = rg.Broker.PositionTiers[rg.Data.Symbol]
		if !tiersExists {
			logger.Error("右腿档位不存在", "symbol", rg.Data.Symbol)
			return false
		}
	}
	return true
}

// 数据源
func DataMonitor(lg, rg *ArbitLeg, stopChan, monitorChan chan struct{}, nextFunc func(), logger *slog.Logger) {
	// 数据源检测
	logger.Info("开始运行")
	// 先放入第一个数据
	utils.SendNonBlocking(monitorChan, struct{}{})
	for {
		select {
		case <-stopChan:
			logger.Info("停止运行数据源检测")
			return
		case <-lg.Data.TriggerChan:
			nextFunc()
			utils.SendNonBlocking(monitorChan, struct{}{})
		case <-rg.Data.TriggerChan:
			nextFunc()
			utils.SendNonBlocking(monitorChan, struct{}{})
		}
	}
}

// 心跳协程
func Heartbeat(lg, rg *ArbitLeg, stopChan chan struct{}, monitorChan chan struct{}, monitorTicker *time.Ticker, logger *slog.Logger) {
	logger.Info("启动心跳检查")
	// 心跳检查
	for {
		select {
		case <-stopChan:
			logger.Info("停止运行心跳检查")
			return
		case <-monitorTicker.C:
			select {
			case <-monitorChan:
			default:
				logger.Warn("数据源5m未更新")
			}
		}
	}
}
