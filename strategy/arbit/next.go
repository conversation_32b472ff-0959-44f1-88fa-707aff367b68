package arbit

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/order"
	"GoTrader/pkg/queue"
	"GoTrader/pkg/utils"
	"GoTrader/strategy"
	"log/slog"
	"math"
	"time"
)

// 更新价差
func UpdateDiff(lg, rg *ArbitLeg, params ArbitrageParams) (openDiffLow, openDiffHigh, closeDiff float64) {
	// 非自动价差模式, 不更新价差
	if params.AutoSeconds <= 0 {
		return params.OpenDiffLow, params.OpenDiffHigh, params.CloseDiff
	}

	profitSpaceLow := params.ProfitSpaceLow
	profitSpaceHigh := params.ProfitSpaceHigh

	// 资金费成本
	leftCost, rightCost := CalcFundingCost(lg.Data, rg.Data)
	fundingCost := max(leftCost+rightCost, 0)

	// 计算利润滑点
	leftAvgPrice := lg.Broker.GetAvgPrice(lg.Data)
	rightAvgPrice := rg.Broker.GetAvgPrice(rg.Data)

	slip := 0.0
	// 持仓小于5倍开仓仓位时滑点成本为0
	if leftAvgPrice == 0 || rightAvgPrice == 0 {
		slip = 0
	} else {
		// 实际开仓均价
		actualAvgDiff := (leftAvgPrice - rightAvgPrice) / rightAvgPrice * 100
		slip = max(profitSpaceLow-(actualAvgDiff-fundingCost), 0)
	}

	// 开仓按照1.5的资金费成本计算
	openDiffLow = profitSpaceLow + fundingCost*1.5 + slip
	openDiffHigh = profitSpaceHigh + fundingCost*1.5
	closeDiff = fundingCost
	return openDiffLow, openDiffHigh, closeDiff
}

// 计算开仓仓位
func CalcOpenSize(lg, rg *ArbitLeg, params ArbitrageParams, lastOpenSize float64, logger *slog.Logger) float64 {
	// 已经有计算值则不再计算
	if lastOpenSize > 0 {
		return lastOpenSize
	}
	// 未获取有效数据不进行计算
	if lg.Data.Bid1Price() <= 0 || rg.Data.Ask1Price() <= 0 {
		return lastOpenSize
	}
	// 找到最小仓位精度
	stepSize := math.Max(lg.Data.Info.StepSize*lg.Data.Info.CtVal, rg.Data.Info.StepSize*rg.Data.Info.CtVal)
	// 找到最大当前价格
	maxPrice := math.Max(lg.Data.Bid1Price(), rg.Data.Ask1Price())
	// 按照最大当前价格计算仓位
	openSize := params.OnceOpenValue / maxPrice
	// 根据精度调整仓位
	openSize = utils.CeilToStep(openSize, stepSize)
	logger.Info("openSize calculated", "maxPrice", maxPrice, "openSize", openSize, "stepSize", stepSize)
	if !(openSize*maxPrice > lg.Data.Info.MinNotional && openSize*maxPrice > rg.Data.Info.MinNotional) {
		logger.Info("openSize is not large enough", "openSize", openSize, "LeftLeg.Data.MinNotional", lg.Data.Info.MinNotional, "RightLeg.Data.MinNotional", rg.Data.Info.MinNotional)
		return lastOpenSize
	}
	return openSize
}

// 计算价差
func CalcDiffs(leftPrice, rightPrice float64, diffs *queue.QueueFloat64) (diff, topDiff, bottomDiff float64) {
	// 价差, 绝对值 百分比
	diff = (leftPrice - rightPrice) / rightPrice * 100
	diffs.EnQueue(diff)
	// 价差前n名
	topDiff = diffs.TopN(3)
	// 价差后n名
	bottomDiff = diffs.BottomN(3)
	return diff, topDiff, bottomDiff
}

// 计算当前资金单次开仓量
func CalcOnceOpenSize(lg, rg *ArbitLeg, params ArbitrageParams,
	capitalLimit,
	openSize, leftPrice, rightPrice, leftLeverage, rightLeverage float64) (leftMaxSize, rightMaxSize, maxSize, onceOpenSize float64) {
	leftSize := lg.Broker.GetDataSize(lg.Data)
	rightSize := rg.Broker.GetDataSize(rg.Data)
	// 当前资金限制开仓量
	leftMaxSize = math.Abs(leftSize) + math.Max((lg.Broker.GetAvailable(lg.Data)-params.MinAvailable)/leftPrice*leftLeverage, 0)
	rightMaxSize = math.Abs(rightSize) + math.Max((rg.Broker.GetAvailable(rg.Data)-params.MinAvailable)/rightPrice*rightLeverage, 0)
	// 杠杆限制开仓量
	leftMaxNotionalSize := lg.Broker.TierMaxNotional(lg.Data) / leftPrice
	rightMaxNotionalSize := rg.Broker.TierMaxNotional(rg.Data) / rightPrice
	// 分配资金限制开仓量
	leftCapitalMaxSize := capitalLimit / (leftLeverage + rightLeverage) * rightLeverage / leftPrice * leftLeverage
	rightCapitalMaxSize := capitalLimit / (leftLeverage + rightLeverage) * leftLeverage / rightPrice * rightLeverage

	// 计算最大开仓量
	maxSize = min(leftMaxSize, rightMaxSize, lg.Broker.TierMaxSize(lg.Data), rg.Broker.TierMaxSize(rg.Data), leftMaxNotionalSize, rightMaxNotionalSize, leftCapitalMaxSize, rightCapitalMaxSize)

	// 单次开仓量
	onceOpenSize = min(openSize, maxSize)
	return leftMaxSize, rightMaxSize, maxSize, onceOpenSize
}

// 定时执行
func TimedDo(ticker *time.Ticker, f func()) {
	select {
	case <-ticker.C:
		f()
	default:
	}
}

// 取消无效订单
func CancelInValidOrder(lg, rg *ArbitLeg, logger *slog.Logger) {
	for _, o := range lg.Broker.GetFuturesOrders(lg.Data.Symbol) {
		if !o.IsValid() {
			logger.Info("撤销过期期货订单", "order", o)
			lg.Client.CancelFuturesOrder(lg.Broker, o)
		}
	}
	for _, o := range lg.Broker.GetMarginOrders(lg.Data.Symbol) {
		if !o.IsValid() {
			logger.Info("撤销过期杠杆订单", "order", o)
			lg.Client.CancelMarginOrder(lg.Broker, o)
		}
	}
	if lg.Broker != rg.Broker {
		for _, o := range lg.Broker.GetFuturesOrders(lg.Data.Symbol) {
			if !o.IsValid() {
				logger.Info("撤销过期期货订单", "order", o)
				lg.Client.CancelFuturesOrder(lg.Broker, o)
			}
		}
		for _, o := range lg.Broker.GetMarginOrders(lg.Data.Symbol) {
			if !o.IsValid() {
				logger.Info("撤销过期杠杆订单", "order", o)
				lg.Client.CancelMarginOrder(lg.Broker, o)
			}
		}
	}
}

// 持仓风险判断
func MaintainMarginReduce(lg, rg *ArbitLeg, lastStatus strategy.TaskStatus, logger *slog.Logger) (status strategy.TaskStatus) {
	// 当前品种持仓
	hasPos := lg.Broker.GetDataSize(lg.Data) != 0 || rg.Broker.GetDataSize(rg.Data) != 0
	lmmr := lg.Broker.GetMaintenanceMarginRate()
	rmmr := rg.Broker.GetMaintenanceMarginRate()
	mmr := min(lmmr, rmmr)
	switch {
	case mmr < 3.5:
		if lastStatus < strategy.MMRClosing && hasPos {
			logger.Warn("MMR过低, 强平风险过高, 尝试主动减仓",
				"lmmr", lmmr, "rmmr", rmmr,
				"leftmaintMargin", lg.Broker.Account.MaintMargin,
				"leftEquity", lg.Broker.Account.Equity,
				"rightmaintMargin", rg.Broker.Account.MaintMargin,
				"rightEquity", rg.Broker.Account.Equity,
			)
			return strategy.MMRClosing
		}

	case mmr >= 5 && mmr <= 6.5:
		if lastStatus == strategy.MMRClosing && hasPos {
			logger.Warn("MMR较低, 强平风险较大, 从主动减仓状态恢复为只减仓状态",
				"lmmr", lmmr, "rmmr", rmmr,
				"leftmaintMargin", lg.Broker.Account.MaintMargin,
				"leftEquity", lg.Broker.Account.Equity,
				"rightmaintMargin", rg.Broker.Account.MaintMargin,
				"rightEquity", rg.Broker.Account.Equity,
			)
			return strategy.MMRWarning
		}
		if lastStatus < strategy.MMRWarning && hasPos {
			logger.Warn("MMR过低, 强平风险较大, 从可开仓状态转换为只减仓状态",
				"lmmr", lmmr, "rmmr", rmmr,
				"leftmaintMargin", lg.Broker.Account.MaintMargin,
				"leftEquity", lg.Broker.Account.Equity,
				"rightmaintMargin", rg.Broker.Account.MaintMargin,
				"rightEquity", rg.Broker.Account.Equity,
			)
			return strategy.MMRWarning
		}

	case mmr >= 7.5:
		if lastStatus == strategy.MMRWarning || lastStatus == strategy.MMRClosing {
			logger.Warn("MMR较高, 无强平风险, 恢复可开仓状态",
				"lmmr", lmmr, "rmmr", rmmr,
				"leftmaintMargin", lg.Broker.Account.MaintMargin,
				"leftEquity", lg.Broker.Account.Equity,
				"rightmaintMargin", rg.Broker.Account.MaintMargin,
				"rightEquity", rg.Broker.Account.Equity,
			)
			return strategy.Active
		}
	}
	return lastStatus
}

// 左腿仓位有效性检查
func LeftPosCheck(lg *ArbitLeg, logger *slog.Logger) {
	lpos := lg.Broker.GetDataSize(lg.Data)
	if lpos <= 0 {
		return
	}
	// 有订单时, 等待结算
	if lg.Broker.HasPending(lg.Data.Symbol) {
		return
	}
	// 仓位差小于最小开仓量
	if math.Abs(lpos) < lg.Data.Info.StepSize*lg.Data.Info.CtVal {
		return
	}
	// 左腿存在多仓, 需要平仓
	// 下期货平仓单
	// 可能补充仓位过小, 导致下单失败, 检查最小订单价值, 如果过小则忽略
	size := math.Abs(utils.RoundToStep(lpos, lg.Data.Info.StepSize*lg.Data.Info.CtVal))
	orderPrice := min(lg.Data.BidN(5).Price, lg.Data.MaxOrderPriceStrict())
	if orderPrice*size < lg.Data.Info.MinNotional ||
		size < lg.Data.Info.StepSize*lg.Data.Info.CtVal ||
		size < lg.Data.Info.MinSize*lg.Data.Info.CtVal {
		return
	}
	logger.Warn("左腿存在多仓, 平仓下单", "价格", orderPrice, "tick", lg.Data.Info.TickSize, "仓位", size)
	err := SubmitDataOrder(lg.Client, lg.Broker, lg.Data, "LongClose", orderPrice, size, order.SELL, nil, logger)
	if err != nil {
		// 打印错误
		logger.Error("左腿多仓平仓下单失败", "reason", err)
		return
	}
	// 打印订单
	logger.Warn("左腿多仓平仓下单成功")
}

// 右腿仓位检查
func RightPosCheck(
	rg *ArbitLeg,
	logger *slog.Logger,
) {
	rpos := rg.Broker.GetDataSize(rg.Data)
	if rpos >= 0 {
		return
	}
	// 有订单时, 等待结算
	if rg.Broker.HasPending(rg.Data.Symbol) {
		return
	}
	// 仓位差小于最小开仓量
	if math.Abs(rpos) < rg.Data.Info.StepSize*rg.Data.Info.CtVal {
		return
	}
	// 右腿存在空仓, 需要平仓
	// 下期货平仓单
	size := math.Abs(utils.RoundToStep(rpos, rg.Data.Info.StepSize*rg.Data.Info.CtVal))
	orderPrice := max(rg.Data.AskN(5).Price, rg.Data.MinOrderPriceStrict())
	if orderPrice*size < rg.Data.Info.MinNotional ||
		size < rg.Data.Info.StepSize*rg.Data.Info.CtVal ||
		size < rg.Data.Info.MinSize*rg.Data.Info.CtVal {
		return
	}
	logger.Warn("右腿存在空仓, 平仓下单", "价格", orderPrice, "tick", rg.Data.Info.TickSize, "仓位", size)
	err := SubmitDataOrder(rg.Client, rg.Broker, rg.Data, "ShortClose", orderPrice, size, order.BUY, nil, logger)
	if err != nil {
		// 打印错误
		logger.Error("右腿空仓平仓下单失败", "reason", err)
		return
	}
	// 打印订单
	logger.Warn("右腿空仓平仓下单成功")
}

// 计算资金费成本
func CalcFundingCost(ld, rd *data.SymbolData) (leftCost, rightCost float64) {
	nts := time.Now().UnixMilli()
	leftFunding := ld.Funding.Rate * 100
	rightFunding := rd.Funding.Rate * 100

	leftFundingTs := ld.Funding.Time
	rightFundingTs := rd.Funding.Time

	leftPeriod := ld.FundingPeriod()
	rightPeriod := rd.FundingPeriod()

	maxTs := max(leftFundingTs, rightFundingTs)

	// 左腿做空
	leftRound := (maxTs-nts)/int64(leftPeriod.Hours()*1000*60*60) + 1
	// 右腿为现货时， 轮次固定为一轮
	if rd.DataType == data.Spot {
		leftRound = 1
	}
	leftCost = -1 * float64(leftRound) * leftFunding
	// 右腿做多
	rightRound := (maxTs-nts)/int64(rightPeriod.Hours()*1000*60*60) + 1
	// 右腿为现货时， 轮次固定为0轮
	if rd.DataType == data.Spot {
		rightRound = 0
	}
	rightCost = float64(rightRound) * rightFunding
	return leftCost, rightCost
}

func UpdateProfitSpace(p ArbitrageParams, lindexDiff, rindexDiff, lfundFloor, rfundFloor float64, logger *slog.Logger) float64 {
	if lindexDiff < lfundFloor || rindexDiff < rfundFloor {
		return 3
	} else {
		return p.ProfitSpaceLow
	}
}

// 计算溢价指数
func calcPremiumIndex(b *broker.Broker, fd *data.SymbolData) float64 {
	if fd.DataType != data.Futures {
		return 0
	}
	imn := b.PositionTiers[fd.Symbol][0].InitialLeverage * 200
	sellInitUSDT := 0.0
	sellQuantity := 0.0
	for _, book := range fd.AskNAll(100) {
		if book.Price*book.Size > imn-sellInitUSDT {
			sellQuantity += (imn - sellInitUSDT) / book.Price
			break
		}
		sellQuantity += book.Size
		sellInitUSDT += book.Price * book.Size
	}
	sellImnPrice := imn / sellQuantity

	buyInitUSDT := 0.0
	buyQuantity := 0.0
	for _, book := range fd.BidNAll(100) {
		if book.Price*book.Size > imn-buyInitUSDT {
			buyQuantity += (imn - buyInitUSDT) / book.Price
			break
		}
		buyQuantity += book.Size
		buyInitUSDT += book.Price * book.Size
	}
	buyImnPrice := imn / buyQuantity
	indexPrice := fd.Index.Last()

	premiumIndex := (max(0, buyImnPrice-indexPrice) - max(0, indexPrice-sellImnPrice)) / indexPrice
	return premiumIndex
}

// 预测资金费
func PredictedFunding(b *broker.Broker, fd *data.SymbolData) (float64, float64) {
	if fd.DataType != data.Futures {
		return 0, fd.Funding.Rate
	}
	premiumIndex := calcPremiumIndex(b, fd)
	funding := fd.Funding.Rate
	fundingTime := fd.Funding.Time
	prevFundingTime := fd.Funding.PrevTime

	currN := float64((time.Now().UnixMilli() - prevFundingTime) / 1000 / 60)
	maxN := float64((fundingTime - prevFundingTime) / 1000 / 60)

	predictedFunding := (funding*currN*(currN+1) + premiumIndex*(maxN*(maxN+1)-currN*(currN+1))) / (maxN * (maxN + 1))
	return premiumIndex, predictedFunding
}
