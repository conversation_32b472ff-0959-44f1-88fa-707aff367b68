package strategy

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/utils"
	"log/slog"
	"time"
)

// 初始化broker
func InitBroker(d *data.SymbolData, b *broker.Broker, c client.APIClient, params Params, logger *slog.Logger) {
	// 确定杠杆
	leverage := params.GetLeverage()
	if leverage == 0 {
		leverage = 3
	}
	// 更新broker数据源相关
	logger.Info("更新broker订单")
	c.UpdateBrokerUMPending(b, []*data.SymbolData{d})

	logger.Info("更新broker 仓位档位")
	c.UpdatePositionTiers(b, d)

	// 更新broker数据源相关
	logger.Info("更新broker订单")
	c.UpdateBrokerUMPending(b, []*data.SymbolData{d})
	c.UpdateFuturesLeverageSingle(b, d)

	logger.Info("更新broker 仓位档位")
	c.UpdatePositionTiers(b, d)

	// 检查是否有持仓, 判断杠杆更新
	if b.GetDataSize(d) == 0 {
		logger.Info("设定杠杆", "leverage", leverage)
		if b.Leverages[d.Symbol] != leverage {
			c.ModifyFuturesLeverage(b, d, leverage)
		}
	} else {
		logger.Info("有持仓跳过修改杠杆")
	}

	// 更新张币系数
	logger.Info("更新张币系数")
	if b.CtVals[d.Symbol] == 0 {
		c.UpdateCtVal(b, d)
	}
}

// Broker数据有效性检查
func BrokerValidCheck(d *data.SymbolData, b *broker.Broker, logger *slog.Logger) bool {

	var ctValExists, leverExists, tiersExists bool
	if d.DataType == data.Futures {
		// 张币系数检查
		_, ctValExists = b.CtVals[d.Symbol]
		if !ctValExists {
			logger.Error("张币系数不存在", "symbol", d.Symbol)
			return false
		}
		// 杠杆检查
		_, leverExists = b.CtVals[d.Symbol]
		if !leverExists {
			logger.Error("杠杆不存在", "symbol", d.Symbol)
			return false
		}

		// 档位检查
		_, tiersExists = b.PositionTiers[d.Symbol]
		if !tiersExists {
			logger.Error("档位不存在", "symbol", d.Symbol)
			return false
		}
	}
	return true
}

// 数据源
func DataMonitor(d *data.SymbolData, stopChan, monitorChan chan struct{}, nextFunc func(), logger *slog.Logger) {
	// 数据源检测
	logger.Info("开始运行")
	// 先放入第一个数据
	utils.SendNonBlocking(monitorChan, struct{}{})
	for {
		select {
		case <-stopChan:
			logger.Info("停止运行数据源检测")
			return
		case <-d.TriggerChan:
			nextFunc()
			utils.SendNonBlocking(monitorChan, struct{}{})
		}
	}
}

// 心跳协程
func Heartbeat(stopChan chan struct{}, monitorChan chan struct{}, monitorTicker *time.Ticker, logger *slog.Logger) {
	logger.Info("启动心跳检查")
	// 心跳检查
	for {
		select {
		case <-stopChan:
			logger.Info("停止运行心跳检查")
			return
		case <-monitorTicker.C:
			select {
			case <-monitorChan:
			default:
				logger.Warn("数据源5m未更新")
			}
		}
	}
}
