package trade

type MarginTrade struct {
	Symbol          string
	Price           float64
	Size            float64
	Time            int64
	Commission      float64
	CommissionAsset string
	IsMaker         bool
	IsBuy           bool
}

type FuturesTrade struct {
	Symbol          string
	Price           float64
	Size            float64
	Time            int64
	Commission      float64
	CommissionAsset string
	IsMaker         bool
	IsBuy           bool
	IsLong          bool
	Pnl             float64
}
