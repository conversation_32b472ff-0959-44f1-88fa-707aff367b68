package order

import (
	"fmt"
	"math"
	"time"
)

// 通用Order, 主要用于创建未知期现货类型订单时保存必要信息, 必须转化为实际订单类型才能使用
type Order struct {
	OrdId       string
	ClOrdId     string
	Symbol      string
	Price       float64
	Size        float64
	Side        Side
	PosSide     PosSide
	OrdType     OrdType
	PriceMatch  PriceMatch
	TimeInForce TimeInForce
	CreatedTime int64
	Status      OrdStatus
	ValidTime   int64
}

func (o *Order) SetClOrdId(s string) *Order {
	o.ClOrdId = s
	return o
}

func (o *Order) SetSymbol(s string) *Order {
	o.Symbol = s
	return o
}

func (o *Order) SetSide(s Side) *Order {
	o.Side = s
	return o
}

func (o *Order) SetOrdType(s OrdType) *Order {
	o.OrdType = s
	return o
}

func (o *Order) SetSize(s float64) *Order {
	o.Size = math.Abs(s)
	return o
}

func (o *Order) SetTimeInForce(s TimeInForce) *Order {
	o.TimeInForce = s
	return o
}

func (o *Order) SetPrice(s float64) *Order {
	o.Price = s
	return o
}

func (o *Order) SetValidSec(s int64) *Order {
	o.ValidTime = s * 1000
	return o
}

func (o *Order) IsOpen() bool {
	return o.Side == BUY
}

func (o *Order) IsClose() bool {
	return !o.IsOpen()
}

func CreateOrder() *Order {
	return &Order{
		CreatedTime: time.Now().UnixMilli(),
		Status:      CREATED,
	}
}

func (o *Order) IsValid() bool {
	if o.ValidTime == 0 || time.Now().UnixMilli()-o.CreatedTime < o.ValidTime {
		return true
	}
	return false
}

func (o *Order) String() string {
	return fmt.Sprintf(
		`CommonOrder{OrdId: %s, ClOrdId: %s, Symbol: %s, Price: %f, Size: %f, Side: %s, OrdType: %s, TimeInForce: %s, CreatedTime: %d, Status: %s, ValidTime: %d}`,
		o.OrdId,
		o.ClOrdId,
		o.Symbol,
		o.Price,
		o.Size,
		o.Side,
		o.OrdType,
		o.TimeInForce,
		o.CreatedTime,
		o.Status,
		o.ValidTime,
	)
}
