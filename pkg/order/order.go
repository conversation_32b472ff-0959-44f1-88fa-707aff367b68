package order

import (
	"fmt"
	"math"
	"time"
)

// 定义枚举类型
type Side string

const (
	BUY  Side = "BUY"
	SELL Side = "SELL"
)

type PosSide string

const (
	LONG  PosSide = "LONG"
	SHORT PosSide = "SHORT"
	BOTH  PosSide = "BOTH"
)

type TimeInForce string

const (
	GTC TimeInForce = "GTC"
	IOC TimeInForce = "IOC"
	FOK TimeInForce = "FOK"
)

type OrdType string

const (
	LIMIT                OrdType = "LIMIT"                //限价单
	MARKET               OrdType = "MARKET"               // 市价单
	STOP                 OrdType = "STOP"                 // 止损限价单
	STOP_MARKET          OrdType = "STOP_MARKET"          // 止损市价单
	TAKE_PROFIT          OrdType = "TAKE_PROFIT"          // 止盈限价单
	TAKE_PROFIT_MARKET   OrdType = "TAKE_PROFIT_MARKET"   // 止盈市价单
	TRAILING_STOP_MARKET OrdType = "TRAILING_STOP_MARKET" // 跟踪止损单
)

type OrdStatus string

const (
	UNKNOWN OrdStatus = "UNKNOWN"

	// 应用层状态, 交易所订单不会出现该状态
	CREATED OrdStatus = "CREATED" // 创建但未受理

	// 订单存活状态
	NEW              OrdStatus = "NEW"              // 创建并已经受理
	PARTIALLY_FILLED OrdStatus = "PARTIALLY_FILLED" // 部分成交, 等待继续成交

	// 订单结束状态
	FILLED           OrdStatus = "FILLED"   // 完成成交
	CANCELED         OrdStatus = "CANCELED" // 已经取消
	EXPIRED          OrdStatus = "EXPIRED"
	REJECTED         OrdStatus = "REJECTED"
	EXPIRED_IN_MATCH OrdStatus = "EXPIRED_IN_MATCH"
)

type PriceMatch string

const (
	OPPONENT    PriceMatch = "OPPONENT"
	OPPONENT_5  PriceMatch = "OPPONENT_5"
	OPPONENT_10 PriceMatch = "OPPONENT_10"
	OPPONENT_20 PriceMatch = "OPPONENT_20"
	QUEUE       PriceMatch = "QUEUE"
	QUEUE_5     PriceMatch = "QUEUE_5"
	QUEUE_10    PriceMatch = "QUEUE_10"
	QUEUE_20    PriceMatch = "QUEUE_20"
)

type OrderValidater interface {
	IsValid() bool
}

// 定义一个订单类型
type FuturesOrder struct {
	ClOrdId           string
	OrdId             string
	Symbol            string
	Price             float64
	Size              float64
	Side              Side
	PosSide           PosSide
	OrdType           OrdType
	TimeInForce       TimeInForce
	PriceMatch        PriceMatch
	AvgPrice          float64
	LastExecutedPrice float64 // 最新成交价格
	ExecutedSize      float64
	LastExecutedSize  float64
	CreatedTime       int64
	ExecutedTime      int64
	Status            OrdStatus
	Maker             bool
	CommAsset         string
	Commission        float64
	Profit            float64
	ValidTime         int64
}

func (o *FuturesOrder) SetClOrdId(s string) *FuturesOrder {
	o.ClOrdId = s
	return o
}

func (o *FuturesOrder) GetClOrdId() string {
	return o.ClOrdId
}

func (o *FuturesOrder) SetSymbol(s string) *FuturesOrder {
	o.Symbol = s
	return o
}

func (o *FuturesOrder) SetSide(s Side) *FuturesOrder {
	o.Side = s
	return o
}

func (o *FuturesOrder) SetPosSide(s PosSide) *FuturesOrder {
	o.PosSide = s
	return o
}

func (o *FuturesOrder) SetOrdType(s OrdType) *FuturesOrder {
	o.OrdType = s
	return o
}

func (o *FuturesOrder) SetSize(s float64) *FuturesOrder {
	o.Size = math.Abs(s)
	return o
}

func (o *FuturesOrder) SetTimeInForce(s TimeInForce) *FuturesOrder {
	o.TimeInForce = s
	return o
}

func (o *FuturesOrder) SetPrice(s float64) *FuturesOrder {
	o.Price = s
	return o
}

func (o *FuturesOrder) SetPriceMatch(s PriceMatch) *FuturesOrder {
	o.PriceMatch = s
	return o
}

func (o *FuturesOrder) SetValidSec(s int64) *FuturesOrder {
	o.ValidTime = s * 1000
	return o
}

func (o *FuturesOrder) IsOpen() bool {
	return (o.Side == BUY && o.PosSide == LONG) || (o.Side == SELL && o.PosSide == SHORT)
}

func (o *FuturesOrder) IsClose() bool {
	return !o.IsOpen()
}

func (o *FuturesOrder) IsBuy() bool {
	return o.Side == BUY
}

func (o *FuturesOrder) IsSell() bool {
	return o.Side == SELL
}

func (o *FuturesOrder) IsLong() bool {
	return o.PosSide == LONG
}

func (o *FuturesOrder) IsShort() bool {
	return o.PosSide == SHORT
}

func CreateFuturesOrder() *FuturesOrder {
	return &FuturesOrder{
		CreatedTime: time.Now().UnixMilli(),
		Status:      CREATED,
	}
}

func (o *FuturesOrder) Copy() FuturesOrder {
	return *o
}

// 从通用结构生成期货订单
func CreateFuturesOrderFromCommon(o *Order, posSide PosSide) *FuturesOrder {
	return &FuturesOrder{
		ClOrdId:     o.ClOrdId,
		OrdId:       o.OrdId,
		Symbol:      o.Symbol,
		Price:       o.Price,
		Size:        o.Size,
		Side:        o.Side,
		PosSide:     posSide,
		OrdType:     o.OrdType,
		TimeInForce: o.TimeInForce,
		PriceMatch:  o.PriceMatch,
		CreatedTime: o.CreatedTime,
		Status:      o.Status,
		ValidTime:   o.ValidTime,
	}
}

type MarginOrder struct {
	OrdId             string
	ClOrdId           string
	Symbol            string
	Price             float64
	Size              float64
	Side              Side
	OrdType           OrdType
	TimeInForce       TimeInForce
	ExecutedSize      float64 // 累积成交数量
	LastExecutedSize  float64 // 最新成交数量
	CreatedTime       int64
	ExecutedTime      int64
	AvgPrice          float64
	LastExecutedPrice float64 // 最新成交价格
	Status            OrdStatus
	Maker             bool
	CommAsset         string
	Commission        float64
	ValidTime         int64
}

func (o *MarginOrder) SetClOrdId(s string) *MarginOrder {
	o.ClOrdId = s
	return o
}

func (o *MarginOrder) GetClOrdId() string {
	return o.ClOrdId
}

func (o *MarginOrder) SetSymbol(s string) *MarginOrder {
	o.Symbol = s
	return o
}

func (o *MarginOrder) SetSide(s Side) *MarginOrder {
	o.Side = s
	return o
}

func (o *MarginOrder) SetOrdType(s OrdType) *MarginOrder {
	o.OrdType = s
	return o
}

func (o *MarginOrder) SetSize(s float64) *MarginOrder {
	o.Size = math.Abs(s)
	return o
}

func (o *MarginOrder) SetTimeInForce(s TimeInForce) *MarginOrder {
	o.TimeInForce = s
	return o
}

func (o *MarginOrder) SetPrice(s float64) *MarginOrder {
	o.Price = s
	return o
}

func (o *MarginOrder) SetValidSec(s int64) *MarginOrder {
	o.ValidTime = s * 1000
	return o
}

func (o *MarginOrder) IsOpen() bool {
	return o.Side == BUY
}

func (o *MarginOrder) IsClose() bool {
	return !o.IsOpen()
}

func CreateMarginOrder() *MarginOrder {
	return &MarginOrder{
		CreatedTime: time.Now().UnixMilli(),
		Status:      CREATED,
	}
}

func (o *FuturesOrder) String() string {
	return fmt.Sprintf(
		`FuturesOrder{OrdId: %s, ClOrdId: %s, Symbol: %s, Price: %f, Size: %f, Side: %s, PosSide: %s, OrdType: %s, TimeInForce: %s, 
		PriceMatch: %s, AvgPrice: %f, ExecutedSize: %f, CreatedTime: %d, ExecutedTime: %d, 
		Status: %s, Maker: %t, CommAsset: %s, Commission: %f, Profit: %f, ValidTime: %d}`,
		o.OrdId,
		o.ClOrdId,
		o.Symbol,
		o.Price,
		o.Size,
		o.Side,
		o.PosSide,
		o.OrdType,
		o.TimeInForce,
		o.PriceMatch,
		o.AvgPrice,
		o.ExecutedSize,
		o.CreatedTime,
		o.ExecutedTime,
		o.Status,
		o.Maker,
		o.CommAsset,
		o.Commission,
		o.Profit,
		o.ValidTime,
	)
}

func (o *FuturesOrder) GetStatus() OrdStatus {
	return o.Status
}
func (o *MarginOrder) GetStatus() OrdStatus {
	return o.Status
}

func (o *MarginOrder) String() string {
	return fmt.Sprintf(
		`MarginOrder{OrdId: %s, ClOrdId: %s, Symbol: %s, Price: %f, Size: %f, Side: %s, OrdType: %s, TimeInForce: %s, 
		ExecutedSize: %f, CreatedTime: %d, ExecutedTime: %d, 
		Status: %s, Maker: %t, CommAsset: %s, Commission: %f, ValidTime: %d}`,
		o.OrdId,
		o.ClOrdId,
		o.Symbol,
		o.Price,
		o.Size,
		o.Side,
		o.OrdType,
		o.TimeInForce,
		o.ExecutedSize,
		o.CreatedTime,
		o.ExecutedTime,
		o.Status,
		o.Maker,
		o.CommAsset,
		o.Commission,
		o.ValidTime,
	)
}

// 从通用结构生成杠杆订单
func CreateMarginOrderFromCommon(o *Order) *MarginOrder {
	return &MarginOrder{
		ClOrdId:     o.ClOrdId,
		OrdId:       o.OrdId,
		Symbol:      o.Symbol,
		Price:       o.Price,
		Size:        o.Size,
		Side:        o.Side,
		OrdType:     o.OrdType,
		TimeInForce: o.TimeInForce,
		CreatedTime: o.CreatedTime,
		Status:      o.Status,
		ValidTime:   o.ValidTime,
	}
}

func (o *MarginOrder) Copy() MarginOrder {
	return *o
}

func (o *FuturesOrder) IsValid() bool {
	if o.ValidTime == 0 || time.Now().UnixMilli()-o.CreatedTime < o.ValidTime {
		return true
	}
	return false
}

func (o *MarginOrder) IsValid() bool {
	if o.ValidTime == 0 || time.Now().UnixMilli()-o.CreatedTime < o.ValidTime {
		return true
	}
	return false
}

func (o *FuturesOrder) Alive() bool {
	return o.Status == NEW || o.Status == PARTIALLY_FILLED
}

func (o *MarginOrder) Alive() bool {
	return o.Status == NEW || o.Status == PARTIALLY_FILLED
}

func (o *FuturesOrder) IsCreated() bool {
	return o.Status == CREATED
}

func (o *MarginOrder) IsCreated() bool {
	return o.Status == CREATED
}

func (o *FuturesOrder) ToNew() {
	if o.Status == CREATED {
		o.Status = NEW
	}
}

func (o *FuturesOrder) ToReject() {
	if o.Status == CREATED {
		o.Status = REJECTED
	}
}

func (o *MarginOrder) ToNew() {
	if o.Status == CREATED {
		o.Status = NEW
	}
}

func (o *MarginOrder) ToReject() {
	if o.Status == CREATED {
		o.Status = REJECTED
	}
}

func (o *FuturesOrder) GetExecutedSize() float64 {
	return o.ExecutedSize
}

func (o *MarginOrder) GetExecutedSize() float64 {
	return o.ExecutedSize
}

func (o *FuturesOrder) GetSize() float64 {
	return o.Size
}

func (o *MarginOrder) GetSize() float64 {
	return o.Size
}
