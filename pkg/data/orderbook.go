package data

import (
	"strconv"
	"sync"
)

// 定义订单簿
type OrderBook struct {
	Price float64
	Size  float64
}

func (o OrderBook) String() string {
	return "(" + strconv.FormatFloat(o.Price, 'f', -1, 64) + "," + strconv.FormatFloat(o.Size, 'f', -1, 64) + ")"
}

type OrderBooks struct {
	Bids         []*OrderBook
	Asks         []*OrderBook
	PrevUpdateId int64
	UpdateId     int64
	Snap         bool
	MaxSize      int
	mu           *sync.RWMutex
}

func NewOrderBooks() *OrderBooks {
	return &OrderBooks{
		Bids:         []*OrderBook{},
		Asks:         []*OrderBook{},
		PrevUpdateId: 0,
		UpdateId:     0,
		Snap:         false,
		MaxSize:      500,
		mu:           &sync.RWMutex{},
	}
}

func (o *OrderBooks) Lock() {
	o.mu.Lock()
}

func (o *OrderBooks) RLock() {
	o.mu.RLock()
}

func (o *OrderBooks) Unlock() {
	o.mu.Unlock()
}

func (o *OrderBooks) RUnlock() {
	o.mu.RUnlock()
}
