package data

import "strings"

const (
	MAX_PRICE float64 = 100_000_000
	MIN_PRICE float64 = 0
)

type ContractStatus string

const (
	PENDING_TRADING ContractStatus = "PENDING_TRADING" // 待上市
	TRADING         ContractStatus = "TRADING"         // 交易中
	PRE_DELIVERING  ContractStatus = "PRE_DELIVERING"  // 预交割
	DELIVERING      ContractStatus = "DELIVERING"      // 交割中
	DELIVERED       ContractStatus = "DELIVERED"       // 已交割
	PRE_SETTLE      ContractStatus = "PRE_SETTLE"      // 预结算
	SETTLING        ContractStatus = "SETTLING"        // 结算中
	CLOSE           ContractStatus = "CLOSE"           // 已下架
)

// 交易所信息集合, 主要用于提交订单时计算参数
// 数量相关的参数均为张数. 币安这种没有张数的交易所默认合约面值为1;
type ExchangeInfo struct {
	Symbol            string         // 品种
	Status            ContractStatus // 品种状态
	MinPrice          float64        // 最小价格
	MaxPrice          float64        // 最大价格
	MinPricePercent   float64        // 最小价格百分比
	MaxPricePercent   float64        // 最大价格百分比
	PriceUpdateTime   int64          // 价格更新时间
	TickSize          float64        // 最小价格变化
	StepSize          float64        // 最小数量变化
	PricePrecision    int            // 价格精度
	QuantityPrecision int            // 数量精度
	CtVal             float64        // 合约面值, 现货默认为1
	MinNotional       float64        // 最小名义价值
	MinSize           float64        // 最小下单数量
}

func NewExchangeInfo(s string) *ExchangeInfo {
	return &ExchangeInfo{
		Symbol:          strings.ToUpper(s),
		Status:          CLOSE,
		MinPrice:        0,
		MaxPrice:        MAX_PRICE,
		MinPricePercent: 0,
		MaxPricePercent: 10, // 默认10倍
		CtVal:           1,  // 默认合约面值为1
	}
}
