package data

// 订阅请求结构
type SubscribeRequest struct {
	Method string   `json:"method"`
	Params []string `json:"params"`
	ID     int      `json:"id"`
}

type ArgItem struct {
	Channel  string `json:"channel"`
	InstId   string `json:"instId"`
	InstType string `json:"instType"`
}

type ArgItemPrivate struct {
	Channel  string `json:"channel"`
	InstType string `json:"instType"`
	// ExtraParams UpdateInterval `json:"extraParams"`
}

type SubscribeRequestOk struct {
	Method string    `json:"op"`
	Params []ArgItem `json:"args"`
}

type SubscribeRequestPrivateOk struct {
	Method string           `json:"op"`
	Params []ArgItemPrivate `json:"args"`
}

type UpdateInterval struct {
	UpdateInterval string `json:"updateInterval"`
}

// 定义归集交易价格
type AggTrade struct {
	EventTime int64
	Symbol    string
	Price     string
	Volume    string
	TradeTime int64
	Maker     bool
	CurrTime  int64
}
