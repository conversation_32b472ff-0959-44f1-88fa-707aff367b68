package data

import (
	"GoTrader/pkg/queue"
	"GoTrader/pkg/utils"
	"log/slog"
	"strings"
	"sync"
	"time"
)

type DataType string

const (
	Spot     DataType = "spot"
	Futures  DataType = "futures"
	Delivery DataType = "delivery"
)

const (
	maxUnixTimestamp int64 = int64(1<<63-1) / 1000_000
)

// 单品种数据源
type SymbolData struct {
	Symbol      string              // 品种, 大写, 如BTCUSDT
	Asset       string              // 基础资产, 大写, 如BTC
	DataType    DataType            // 数据类型, 期货, 现货
	Price       *queue.QueueFloat64 // 价格队列
	Index       *queue.QueueFloat64 // 指数队列
	Volume      *queue.QueueFloat64 // 成交量队列
	Time        *queue.QueueFloat64 // 时间队列
	Info        *ExchangeInfo       // 交易对信息
	Funding     *Funding            // 资金费率信息
	OrderBooks  *OrderBooks         // 订单簿
	mu          *sync.RWMutex       // 读写锁
	Trigger<PERSON>han chan struct{}       // 数据更新触发通道
}

func NewSymbolData(s string, t DataType) *SymbolData {
	symbol := strings.ToUpper(s)
	asset := strings.TrimSuffix(symbol, "USDT")
	return &SymbolData{
		Symbol:      symbol,
		Asset:       asset,
		DataType:    t,
		Price:       queue.NewQueueFloat64(20),
		Index:       queue.NewQueueFloat64(20),
		Volume:      queue.NewQueueFloat64(20),
		Time:        queue.NewQueueFloat64(20),
		Info:        NewExchangeInfo(symbol),
		Funding:     NewFunding(),
		OrderBooks:  NewOrderBooks(),
		mu:          &sync.RWMutex{},
		TriggerChan: make(chan struct{}),
	}
}

func (d *SymbolData) TriggerUpdate(logger *slog.Logger) {
	select {
	case d.TriggerChan <- struct{}{}:
		// logger.Info("数据源已接收数据", "symbol", d.Symbol, "dataType", d.DataType)
		// 成功发送数据
	default:
		// 通道没有接收方或者缓冲已满，丢弃数据
		// logger.Info("数据源未接收数据", "symbol", d.Symbol, "dataType", d.DataType)
	}
}

func (d *SymbolData) Lock() {
	d.mu.Lock()
}

func (d *SymbolData) RLock() {
	d.mu.RLock()
}

func (d *SymbolData) Unlock() {
	d.mu.Unlock()
}

func (d *SymbolData) RUnlock() {
	d.mu.RUnlock()
}

func (d *SymbolData) BidN(n int) OrderBook {
	d.OrderBooks.RLock()
	defer d.OrderBooks.RUnlock()
	if n == 0 || len(d.OrderBooks.Bids) == 0 {
		return OrderBook{}
	}
	if n > len(d.OrderBooks.Bids) {
		return *d.OrderBooks.Bids[len(d.OrderBooks.Bids)-1]
	}
	return *d.OrderBooks.Bids[n-1]
}

func (d *SymbolData) AskN(n int) OrderBook {
	d.OrderBooks.RLock()
	defer d.OrderBooks.RUnlock()
	if n == 0 || len(d.OrderBooks.Asks) == 0 {
		return OrderBook{}
	}
	if n > len(d.OrderBooks.Asks) {
		return *d.OrderBooks.Asks[len(d.OrderBooks.Asks)-1]
	}
	return *d.OrderBooks.Asks[n-1]
}

func (d *SymbolData) BidNAll(n int) []OrderBook {
	d.OrderBooks.RLock()
	defer d.OrderBooks.RUnlock()
	if n == 0 {
		n = d.OrderBooks.MaxSize
	}
	books := make([]OrderBook, 0)
	for i := range n {
		if i >= len(d.OrderBooks.Bids) {
			break
		}
		books = append(books, *d.OrderBooks.Bids[i])
	}
	return books
}

func (d *SymbolData) AskNAll(n int) []OrderBook {
	d.OrderBooks.RLock()
	defer d.OrderBooks.RUnlock()
	if n == 0 {
		n = d.OrderBooks.MaxSize
	}
	books := make([]OrderBook, 0)
	for i := range n {
		if i >= len(d.OrderBooks.Asks) {
			break
		}
		books = append(books, *d.OrderBooks.Asks[i])
	}
	return books
}

func (d *SymbolData) BookNAll(n int) []OrderBook {
	books := make([]OrderBook, 0)
	books = append(books, utils.ReverseSlice(d.BidNAll(n))...)
	books = append(books, d.AskNAll(n)...)
	return books
}

// Bid1Price 返回最新的买一价
func (d *SymbolData) Bid1Price() float64 {
	return d.BidN(1).Price
}

// Ask1Price 返回最新的卖一价
func (d *SymbolData) Ask1Price() float64 {
	return d.AskN(1).Price
}

// 资金费率周期
func (d *SymbolData) FundingPeriod() time.Duration {
	p := time.Duration(utils.RoundIntToStep(d.Funding.Time-d.Funding.PrevTime, 1000*60)) * time.Millisecond
	return p
}

// 聚合后的盘口
func (d *SymbolData) BidNAllAgg(n int, step float64) []OrderBook {
	books := d.BidNAll(n)
	return aggOrderBooks(books, step)
}

func (d *SymbolData) AskNAllAgg(n int, step float64) []OrderBook {
	books := d.AskNAll(n)
	return aggOrderBooks(books, step)
}

func (d *SymbolData) BookNAllAgg(n int, step float64) []OrderBook {
	books := d.BookNAll(n)
	return aggOrderBooks(books, step)
}

// 最小下单价格, 用卖一价计算更严格
func (d *SymbolData) MinOrderPriceStrict() float64 {
	if d.Ask1Price() == 0 {
		return utils.RoundToStep(max(d.Info.MinPrice, d.Price.Last()*d.Info.MinPricePercent), d.Info.TickSize)
	}
	return utils.RoundToStep(max(d.Info.MinPrice, d.Ask1Price()*d.Info.MinPricePercent), d.Info.TickSize)
}

// 最大下单价格, 用买一价计算更严格
func (d *SymbolData) MaxOrderPriceStrict() float64 {
	if d.Bid1Price() == 0 {
		return utils.RoundToStep(min(d.Info.MaxPrice, d.Price.Last()*d.Info.MaxPricePercent), d.Info.TickSize)
	}
	return utils.RoundToStep(min(d.Info.MaxPrice, d.Bid1Price()*d.Info.MaxPricePercent), d.Info.TickSize)
}

func aggOrderBooks(books []OrderBook, step float64) []OrderBook {
	aggPriceIntList := make([]int, 0)
	aggSizeList := make([]float64, 0)
	for _, book := range books {
		aggPriceInt := int(book.Price / step)
		// 新的聚合价格
		if len(aggPriceIntList) == 0 || aggPriceIntList[len(aggPriceIntList)-1] != aggPriceInt {
			aggPriceIntList = append(aggPriceIntList, aggPriceInt)
			aggSizeList = append(aggSizeList, book.Size)
		} else { // 聚合到已有价格
			aggSizeList[len(aggSizeList)-1] += book.Size
		}

	}
	aggBooks := make([]OrderBook, 0)
	for i := range aggPriceIntList {
		aggBooks = append(aggBooks, OrderBook{
			Price: float64(aggPriceIntList[i]) * step,
			Size:  aggSizeList[i],
		})
	}
	return aggBooks
}
