package queue

import (
	"math"
	"reflect"
	"testing"
)

func TestQueueFloat64Basic(t *testing.T) {
	q := NewQueueFloat64(3)

	// 队列初始长度为0
	if q.<PERSON>ze() != 0 {
		t.<PERSON><PERSON><PERSON>("Size = %d, want 0", q.<PERSON><PERSON>())
	}

	// EnQueue元素
	q.EnQueue(1.1)
	q.EnQueue(2.2)
	q.EnQueue(3.3)

	if q.<PERSON><PERSON>() != 3 {
		t.<PERSON><PERSON><PERSON>("Size = %d, want 3", q.<PERSON><PERSON>())
	}

	// 队列满时再入队，最老元素覆盖
	q.EnQueue(4.4)
	if q.<PERSON><PERSON>() != 3 {
		t.<PERSON><PERSON>("Size = %d, want 3", q.<PERSON><PERSON>())
	}

	last := q.Last()
	if last != 4.4 {
		t.<PERSON><PERSON><PERSON>("Last = %f, want 4.4", last)
	}

	// 测试 LastIndex
	last0 := q.LastIndex(0)
	last1 := q.LastIndex(1)
	last2 := q.LastIndex(2)
	expected := []float64{4.4, 3.3, 2.2}
	got := []float64{last0, last1, last2}
	for i := range expected {
		if math.Abs(got[i]-expected[i]) > 1e-9 {
			t.Errorf("LastIndex[%d] = %f, want %f", i, got[i], expected[i])
		}
	}

	// 测试 LastN
	lastN := q.LastN(2)
	wantN := []float64{4.4, 3.3}
	if !reflect.DeepEqual(lastN, wantN) {
		t.Errorf("LastN = %v, want %v", lastN, wantN)
	}

	// 测试 TopN
	top0 := q.TopN(0)
	if top0 != 4.4 {
		t.Errorf("TopN(0) = %f, want 4.4", top0)
	}
	top1 := q.TopN(1)
	if top1 != 3.3 {
		t.Errorf("TopN(1) = %f, want 3.3", top1)
	}

	// 测试 BottomN
	bottom0 := q.BottomN(0)
	if bottom0 != 2.2 {
		t.Errorf("BottomN(0) = %f, want 2.2", bottom0)
	}
	bottom1 := q.BottomN(1)
	if bottom1 != 3.3 {
		t.Errorf("BottomN(1) = %f, want 3.3", bottom1)
	}
}

func TestQueueFloat64NaN(t *testing.T) {
	q := NewQueueFloat64(3)

	// 队列为空时 Last 返回 NaN
	if !math.IsNaN(q.Last()) {
		t.Errorf("Last on empty queue should be NaN")
	}

	// LastIndex 超出范围返回 NaN
	if !math.IsNaN(q.LastIndex(1)) {
		t.Errorf("LastIndex on empty queue should be NaN")
	}

	// LastN 空队列返回空切片
	lastN := q.LastN(3)
	if len(lastN) != 0 {
		t.Errorf("LastN on empty queue = %v, want []", lastN)
	}
}
