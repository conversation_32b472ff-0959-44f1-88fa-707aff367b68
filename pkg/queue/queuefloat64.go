package queue

import (
	"math"
	"sort"
)

/*
固定长度队列, 先进先出
*/
type QueueFloat64 struct {
	data      []float64 // 底层数据切片
	front     int       // 队头索引
	rear      int       // 队尾索引
	size      int       // 当前队列元素数量
	maxLength int       // 队列的最大容量
}

// 新建一个队列
// NewQueueFloat64 创建一个新的队列，最大长度在此时确定
func NewQueueFloat64(length int) *QueueFloat64 {
	// 初始化一个具有固定容量的切片
	q := &QueueFloat64{
		data:      make([]float64, length), // 初始化固定容量的切片
		maxLength: length,
		front:     -1,
		rear:      -1,
		size:      0,
	}
	for i := range q.data {
		q.data[i] = math.NaN() // 显式将每个元素设为 NaN
	}
	return q
}

func (q *QueueFloat64) EnQueue(item float64) {
	q.forward()
	q.data[q.rear] = item
}

func (q *QueueFloat64) Size() int {
	return q.size
}

func (q *QueueFloat64) Last() float64 {
	if q.rear < 0 {
		return math.NaN()
	}
	return q.data[q.rear]
}

// 返回最近第n个数
func (q *QueueFloat64) LastIndex(index int) float64 {
	if q.rear < 0 {
		return math.NaN()
	}
	if index > q.size {
		return math.NaN()
	}
	return q.data[(q.rear-index+q.maxLength)%q.maxLength]
}

// 返回最近的n个数
func (q *QueueFloat64) LastN(n int) []float64 {
	data := []float64{}
	for i := range n {
		num := q.LastIndex(i)
		if math.IsNaN(num) {
			break
		}
		data = append(data, num)
	}
	return data

}

func (q *QueueFloat64) forward() {
	if q.size == q.maxLength {
		q.front = (q.front + 1) % q.maxLength
		q.rear = (q.rear + 1) % q.maxLength
	} else {
		q.rear = (q.rear + 1) % q.maxLength
		q.size++
	}
}

func (q *QueueFloat64) GetCopyData() []float64 {
	return append([]float64{}, q.data...)
}

// 前n名
func (q *QueueFloat64) TopN(n int) float64 {
	data := q.GetCopyData()
	sort.Sort(sort.Reverse(sort.Float64Slice(data)))
	if n >= len(data) {
		return data[len(data)-1]
	}
	return data[n]
}

// 后n名
func (q *QueueFloat64) BottomN(n int) float64 {
	data := q.GetCopyData()
	sort.Float64s(data)
	if n >= len(data) {
		return data[len(data)-1]
	}
	return data[n]
}
