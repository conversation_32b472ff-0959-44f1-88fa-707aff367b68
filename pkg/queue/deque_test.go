package queue

import (
	"testing"
	"time"
)

// Test基本操作
func TestDequeBasic(t *testing.T) {
	dq := NewDeque[int](3, false) // 非阻塞模式，最大长度3

	// Append
	dq.Append(1)
	dq.Append(2)
	dq.Append(3)
	if dq.Len() != 3 {
		t.<PERSON><PERSON>("Len = %d, want 3", dq.Len())
	}

	// 队列满时Append会覆盖左边元素
	dq.Append(4) // 队列: [2,3,4]
	items := dq.Items()
	want := []int{2, 3, 4}
	for i, v := range want {
		if items[i] != v {
			t.<PERSON>rrorf("Items[%d] = %d, want %d", i, items[i], v)
		}
	}

	// AppendLeft
	dq.AppendLeft(1) // 队列: [1,2,3]
	items = dq.Items()
	want = []int{1, 2, 3}
	for i, v := range want {
		if items[i] != v {
			t.<PERSON><PERSON><PERSON>("AppendLeft Items[%d] = %d, want %d", i, items[i], v)
		}
	}

	// PopLeft
	val, ok := dq.PopLeft()
	if !ok || val != 1 {
		t.Errorf("PopLeft = %d, %v, want 1,true", val, ok)
	}

	// PopRight
	val, ok = dq.PopRight()
	if !ok || val != 3 {
		t.Errorf("PopRight = %d, %v, want 3,true", val, ok)
	}
}

// Test阻塞模式
func TestDequeBlocking(t *testing.T) {
	dq := NewDeque[int](2, true) // 阻塞模式

	done := make(chan struct{})

	// 测试阻塞Append
	go func() {
		dq.Append(1)
		dq.Append(2)
		// 第三个Append会阻塞，直到PopLeft释放
		go func() {
			time.Sleep(100 * time.Millisecond)
			dq.PopLeft()
		}()
		dq.Append(3)
		close(done)
	}()

	select {
	case <-done:
		// 测试通过
	case <-time.After(time.Second):
		t.Errorf("Append blocked too long")
	}

	// 队列最终元素应该是 [2,3]
	items := dq.Items()
	want := []int{2, 3}
	for i, v := range want {
		if items[i] != v {
			t.Errorf("Blocking Items[%d] = %d, want %d", i, items[i], v)
		}
	}
}

func TestDequeAt(t *testing.T) {
	dq := NewDeque[int](5, false) // 非阻塞模式

	dq.Append(10) // 队列: [10]
	dq.Append(20) // 队列: [10,20]
	dq.Append(30) // 队列: [10,20,30]

	// 测试左起索引 At
	val, ok := dq.At(0)
	if !ok || val != 10 {
		t.Errorf("At(0) = %v, %v, want 10,true", val, ok)
	}
	val, ok = dq.At(2)
	if !ok || val != 30 {
		t.Errorf("At(2) = %v, %v, want 30,true", val, ok)
	}
	_, ok = dq.At(3) // 越界
	if ok {
		t.Errorf("At(3) should be invalid")
	}

	// 测试右起索引 AtRight
	val, ok = dq.AtRight(0)
	if !ok || val != 30 {
		t.Errorf("AtRight(0) = %v, %v, want 30,true", val, ok)
	}
	val, ok = dq.AtRight(2)
	if !ok || val != 10 {
		t.Errorf("AtRight(2) = %v, %v, want 10,true", val, ok)
	}
	_, ok = dq.AtRight(3) // 越界
	if ok {
		t.Errorf("AtRight(3) should be invalid")
	}

	// 测试中间索引
	val, ok = dq.At(1)
	if !ok || val != 20 {
		t.Errorf("At(1) = %v, %v, want 20,true", val, ok)
	}
	val, ok = dq.AtRight(1)
	if !ok || val != 20 {
		t.Errorf("AtRight(1) = %v, %v, want 20,true", val, ok)
	}
}

func TestDequeSetAt(t *testing.T) {
	d := NewDeque[int](5, false)

	// 插入 3 个元素
	d.Append(10)
	d.Append(20)
	d.Append(30)

	// 替换中间的元素
	ok := d.SetAt(1, 200)
	if !ok {
		t.Errorf("SetAt failed, expected success")
	}

	val, _ := d.At(1)
	if val != 200 {
		t.Errorf("SetAt wrong value, got %d, want %d", val, 200)
	}

	// 越界替换
	ok = d.SetAt(5, 500)
	if ok {
		t.Errorf("SetAt should fail on out of range index")
	}
}
