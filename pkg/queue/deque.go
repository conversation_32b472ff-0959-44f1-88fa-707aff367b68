package queue

import (
	"fmt"
	"strings"
	"sync"
)

// Deque 泛型双端队列，支持固定长度和阻塞模式
type Deque[T any] struct {
	mu       sync.Mutex
	notFull  *sync.Cond
	notEmpty *sync.Cond

	data  []T
	head  int
	tail  int
	size  int
	cap   int
	block bool // 阻塞模式
}

// String 实现 fmt.Stringer 接口，方便调试打印
func (d *Deque[T]) String() string {
	if d.size == 0 {
		return "Deque[]"
	}
	var sb strings.Builder
	sb.WriteString("Deque[")
	for i := 0; i < d.size; i++ {
		if i > 0 {
			sb.WriteString(", ")
		}
		e, _ := d.At(i)

		sb.WriteString(fmt.Sprintf("%v", e))
	}
	sb.WriteString("]")
	return sb.String()
}

// NewDeque 创建队列，maxSize > 0，block=true表示阻塞等待
func NewDeque[T any](maxSize int, block bool) *Deque[T] {
	if maxSize <= 0 {
		panic("maxSize must be > 0")
	}
	d := &Deque[T]{
		data:  make([]T, maxSize),
		cap:   maxSize,
		block: block,
	}
	d.notFull = sync.NewCond(&d.mu)
	d.notEmpty = sync.NewCond(&d.mu)
	return d
}

func (d *Deque[T]) Fill(value T) {
	for i := 0; i < d.cap; i++ {
		d.data[i] = value
	}
	d.head = 0
	d.tail = d.cap - 1
	d.size = d.cap
}

// Append 右入队
func (d *Deque[T]) Append(item T) {
	d.mu.Lock()
	defer d.mu.Unlock()

	// 阻塞模式：队列满时等待
	for d.block && d.size == d.cap {
		d.notFull.Wait()
	}

	if d.size == d.cap && !d.block {
		// 非阻塞模式，覆盖最左边元素
		d.head = (d.head + 1) % d.cap
		d.size--
	}

	d.data[d.tail] = item
	d.tail = (d.tail + 1) % d.cap
	d.size++

	d.notEmpty.Signal()
}

// AppendLeft 左入队
func (d *Deque[T]) AppendLeft(item T) {
	d.mu.Lock()
	defer d.mu.Unlock()

	// 阻塞模式：队列满时等待
	for d.block && d.size == d.cap {
		d.notFull.Wait()
	}

	if d.size == d.cap && !d.block {
		// 非阻塞模式，覆盖最右边元素
		d.tail = (d.tail - 1 + d.cap) % d.cap
		d.size--
	}

	d.head = (d.head - 1 + d.cap) % d.cap
	d.data[d.head] = item
	d.size++

	d.notEmpty.Signal()
}

// PopLeft 左出队
func (d *Deque[T]) PopLeft() (T, bool) {
	d.mu.Lock()
	defer d.mu.Unlock()

	for d.block && d.size == 0 {
		d.notEmpty.Wait()
	}

	if d.size == 0 {
		var zero T
		return zero, false
	}

	item := d.data[d.head]
	d.head = (d.head + 1) % d.cap
	d.size--

	d.notFull.Signal()
	return item, true
}

// PopRight 右出队
func (d *Deque[T]) PopRight() (T, bool) {
	d.mu.Lock()
	defer d.mu.Unlock()

	for d.block && d.size == 0 {
		d.notEmpty.Wait()
	}

	if d.size == 0 {
		var zero T
		return zero, false
	}

	d.tail = (d.tail - 1 + d.cap) % d.cap
	item := d.data[d.tail]
	d.size--

	d.notFull.Signal()
	return item, true
}

// Len 当前元素数量
func (d *Deque[T]) Len() int {
	d.mu.Lock()
	defer d.mu.Unlock()
	return d.size
}

// Items 返回队列元素副本（从左到右）
func (d *Deque[T]) Items() []T {
	d.mu.Lock()
	defer d.mu.Unlock()

	result := make([]T, d.size)
	for i := 0; i < d.size; i++ {
		idx := (d.head + i) % d.cap
		result[i] = d.data[idx]
	}
	return result
}

func (d *Deque[T]) At(index int) (T, bool) {
	d.mu.Lock()
	defer d.mu.Unlock()

	if index < 0 || index >= d.size {
		var zero T
		return zero, false
	}

	idx := (d.head + index) % d.cap
	return d.data[idx], true
}

func (d *Deque[T]) AtRight(index int) (T, bool) {
	d.mu.Lock()
	defer d.mu.Unlock()

	if index < 0 || index >= d.size {
		var zero T
		return zero, false
	}

	idx := (d.tail - 1 - index + d.cap) % d.cap
	return d.data[idx], true
}

func (d *Deque[T]) SetAt(index int, val T) bool {
	if index < 0 || index >= d.size {
		return false
	}
	realIndex := (d.head + index) % d.cap
	d.data[realIndex] = val
	return true
}
