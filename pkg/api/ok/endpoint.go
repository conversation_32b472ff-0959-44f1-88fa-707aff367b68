package ok

import (
	"GoTrader/pkg/api"
)

// 账户相关api
var (
	APIMarginOrder        = api.APIMethod{Method: "POST", Path: "/api/v5/trade/order"}       // 杠杆账户下单
	APIFuturesOrder       = api.APIMethod{Method: "POST", Path: "/api/v5/trade/order"}       // 合约账户下单
	APIModifyFuturesOrder = api.APIMethod{Method: "POST", Path: "/api/v5/trade/amend-order"} // 修改期货订单

	APIQueryFuturesOrder     = api.APIMethod{Method: "GET", Path: "/api/v5/trade/order"}           // 查询期货订单
	APISpotOrder             = api.APIMethod{Method: "POST", Path: "/api/v3/order "}               // 现货账户下单
	APIGetBalance            = api.APIMethod{Method: "GET", Path: "/api/v5/account/balance"}       // 请求账户余额
	APIGetPending            = api.APIMethod{Method: "GET", Path: "/api/v5/trade/orders-pending"}  // 查询全部挂单
	APICancelOrder           = api.APIMethod{Method: "POST", Path: "/api/v5/trade/cancel-order"}   // 请求撤销订单
	APIGetUMLeverageInfo     = api.APIMethod{Method: "GET", Path: "/api/v5/account/leverage-info"} // 获取期货交易杠杆配置
	APIModifyFuturesLeverage = api.APIMethod{Method: "POST", Path: "/api/v5/account/set-leverage"} // 修改期货杠杆
	APIGetPositions          = api.APIMethod{Method: "GET", Path: "/api/v5/account/positions"}     // 请求账户持仓
	APIGetAccountConfig      = api.APIMethod{Method: "GET", Path: "/api/v5/account/config"}        // 获取账户配置
	APIGetCommissionRate     = api.APIMethod{Method: "GET", Path: "/api/v5/account/trade-fee"}     // 获取手续费率
)

// 数据相关api
var (
	APIGetFuturesFunding      = api.APIMethod{Method: "GET", Path: "/api/v5/public/funding-rate"}         // 获取资金费率, 只有单品种
	APIGetFuturesFundingHist  = api.APIMethod{Method: "GET", Path: "/api/v5/public/funding-rate-history"} // 获取历史资金费率
	APIGetExchangeInfo        = api.APIMethod{Method: "GET", Path: "/api/v5/public/instruments"}          // 获取期货交易规则
	APIGetPositionTiers       = api.APIMethod{Method: "GET", Path: "/api/v5/public/position-tiers"}       // 获取期货仓位档位
	APIGetKlines              = api.APIMethod{Method: "GET", Path: "/api/v5/market/candles"}              // 获取K线数据
	APIGetFuturesOpenInterest = api.APIMethod{Method: "GET", Path: "/api/v5/public/open-interest"}        // 获取持仓量
)
