package api

type APIMethod struct {
	// 请求方法
	Method string
	Path   string
}

// Bn broker需要用到的api集合
type BnBrokerAPIgroup struct {
	MarginOrder        APIMethod // 杠杆账户下单
	FutureOrder        APIMethod // 合约账户下单
	ModifyFuturesOrder APIMethod // 修改期货订单

	GetListenKey APIMethod // 生成listenkey
	DelListenKey APIMethod // 删除listenkey
	PutListenKey APIMethod // 延长listenkey

	GetSpotListenKey APIMethod // 生成现货listenkey
	DelSpotListenKey APIMethod // 删除现货listenkey
	PutSpotListenKey APIMethod // 延长现货listenkey

	GetBalance            APIMethod // 请求账户余额
	GetAccount            APIMethod // 请求账户信息
	GetUMAccount          APIMethod // 请求UM账户信息
	GetUMPending          APIMethod // 查询UM全部挂单
	GetMarginPending      APIMethod // 查询杠杆全部挂单
	CancelAllFuturesOrder APIMethod // 请求撤销所有期货订单
	CancelAllMarginOrder  APIMethod // 请求撤销所有杠杆订单
	CancelFuturesOrder    APIMethod // 请求撤销期货订单
	CancelMarginOrder     APIMethod // 请求撤销杠杆订单
	GetFuturesTrades      APIMethod // 查询合约成交记录
	GetMarginTrades       APIMethod // 查询杠杆成交记录
	GetUMConfig           APIMethod // 获取期货交易配置
	GetUMIncome           APIMethod // 查询UM损益流水
	ModifyFuturesLeverage APIMethod // 修改期货杠杆

	GetFuturesExchangeInfo   APIMethod // 获取期货交易规则
	GetSpotExchangeInfo      APIMethod // 获取现货交易规则
	GetFuturesFunding        APIMethod // 获取资金费率
	GetFuturesFundingHist    APIMethod // 获取历史资金费率
	GetFuturesFundingInfo    APIMethod // 获取资金费率信息
	GetMarginPairs           APIMethod // 获取杠杆交易品种
	GetFuturesPrice          APIMethod // 获取期货当前价格
	GetSpotPrice             APIMethod // 获取现货当前价格
	GetFuturesDepth          APIMethod // 查询期货深度
	GetPositionTiers         APIMethod // 获取期货仓位档位
	GetFuturesCommissionRate APIMethod // 获取期货手续费率
	GetSpotCommissionRate    APIMethod // 获取现货手续费率
	GetSpotDepth             APIMethod // 查询现货深度
	GetFuturesKlines         APIMethod // 获取期货K线数据
	GetSpotKlines            APIMethod // 获取现货K线数据
	GetFuturesOpenInterest   APIMethod // 获取期货持仓量

	QueryFuturesOrder APIMethod // 更新期货订单
	QueryMarginOrder  APIMethod // 更新杠杆订单
}
