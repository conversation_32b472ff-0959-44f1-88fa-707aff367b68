package bn

import (
	"GoTrader/pkg/api"
)

// 统一账户相关api
var (
	APIMarginOrder = api.APIMethod{Method: "POST", Path: "/papi/v1/margin/order"} // 杠杆账户下单
	APIFutureOrder = api.APIMethod{Method: "POST", Path: "/papi/v1/um/order"}     // 合约账户下单

	APIModifyFuturesOrder = api.APIMethod{Method: "PUT", Path: "/papi/v1/um/order"} // 修改期货订单

	APIGetListenKey = api.APIMethod{Method: "POST", Path: "/papi/v1/listenKey"}   // 生成listenkey
	APIDelListenKey = api.APIMethod{Method: "DELETE", Path: "/papi/v1/listenKey"} // 删除listenkey
	APIPutListenKey = api.APIMethod{Method: "PUT", Path: "/papi/v1/listenKey"}    // 延长listenkey

	APIGetBalance               = api.APIMethod{Method: "GET", Path: "/papi/v1/balance"}                 // 请求账户余额
	APIGetAccount               = api.APIMethod{Method: "GET", Path: "/papi/v1/account"}                 // 请求账户信息
	APIGetUMAccount             = api.APIMethod{Method: "GET", Path: "/papi/v2/um/account"}              // 请求UM账户信息
	APIGetUMPending             = api.APIMethod{Method: "GET", Path: "/papi/v1/um/openOrders"}           // 查询UM全部挂单
	APIGetMarginPending         = api.APIMethod{Method: "GET", Path: "/papi/v1/margin/openOrders"}       // 查询杠杆全部挂单
	APICancelAllFuturesOrder    = api.APIMethod{Method: "DELETE", Path: "/papi/v1/um/allOpenOrders"}     // 请求撤销所有期货订单
	APICancelAllMarginOrder     = api.APIMethod{Method: "DELETE", Path: "/papi/v1/margin/allOpenOrders"} // 请求撤销所有杠杆订单
	APICancelFuturesOrder       = api.APIMethod{Method: "DELETE", Path: "/papi/v1/um/order"}             // 请求撤销期货订单
	APICancelMarginOrder        = api.APIMethod{Method: "DELETE", Path: "/papi/v1/margin/order"}         // 请求撤销杠杆订单
	APIGetFuturesTrades         = api.APIMethod{Method: "GET", Path: "/papi/v1/um/userTrades"}           // 查询合约成交记录
	APIGetMarginTrades          = api.APIMethod{Method: "GET", Path: "/papi/v1/margin/myTrades"}         // 查询杠杆成交记录
	APIGetUMConfig              = api.APIMethod{Method: "GET", Path: "/papi/v1/um/symbolConfig"}         // 获取期货交易配置
	APIGetUMIncome              = api.APIMethod{Method: "GET", Path: "/papi/v1/um/income"}               // 查询UM损益流水
	APIGetUMPositionTiers       = api.APIMethod{Method: "GET", Path: "/papi/v1/um/leverageBracket"}      // 获取期货仓位档位
	APIModifyFuturesLeverage    = api.APIMethod{Method: "POST", Path: "/papi/v1/um/leverage"}            // 修改期货杠杆
	APIGetFuturesCommissionRate = api.APIMethod{Method: "GET", Path: "/papi/v1/um/commissionRate"}       // 获取期货手续费率
	APIQueryUMOrder             = api.APIMethod{Method: "GET", Path: "/papi/v1/um/order"}                // 查询UM订单
	APIQueryMarginOrder         = api.APIMethod{Method: "GET", Path: "/papi/v1/margin/order"}            // 查询杠杆订单
)

// 传统账户相关api
var (
	APICostomSpotOrder   = api.APIMethod{Method: "POST", Path: "/api/v3/order"}  // 现货下单
	APICostomFutureOrder = api.APIMethod{Method: "POST", Path: "/fapi/v1/order"} // 合约下单

	APICostomModifyFuturesOrder = api.APIMethod{Method: "PUT", Path: "/fapi/v1/order"}       // 修改期货订单
	APICostomBatchFuturesOrders = api.APIMethod{Method: "PUT", Path: "/fapi/v1/batchOrders"} // 批量修改期货订单

	APICostomGetListenKey = api.APIMethod{Method: "POST", Path: "/fapi/v1/listenKey"}   // 生成listenkey
	APICostomDelListenKey = api.APIMethod{Method: "DELETE", Path: "/fapi/v1/listenKey"} // 删除listenkey
	APICostomPutListenKey = api.APIMethod{Method: "PUT", Path: "/fapi/v1/listenKey"}    // 延长listenkey

	APICostomGetSpotListenKey = api.APIMethod{Method: "POST", Path: "/api/v3/userDataStream"}   // 生成现货listenkey
	APICostomDelSpotListenKey = api.APIMethod{Method: "DELETE", Path: "/api/v3/userDataStream"} // 删除现货listenkey
	APICostomPutSpotListenKey = api.APIMethod{Method: "PUT", Path: "/api/v3/userDataStream"}    // 延长现货listenkey

	APICostomGetBalance               = api.APIMethod{Method: "GET", Path: "/fapi/v3/balance"}          // 请求账户余额
	APICostomGetAccount               = api.APIMethod{Method: "GET", Path: "/api/v3/account"}           // 请求现货账户信息
	APICostomGetUMAccount             = api.APIMethod{Method: "GET", Path: "/fapi/v3/account"}          // 请求期货账户信息
	APICostomGetUMPending             = api.APIMethod{Method: "GET", Path: "/fapi/v1/openOrders"}       // 查询UM全部挂单
	APICostomGetMarginPending         = api.APIMethod{Method: "GET", Path: "/api/v3/openOrders"}        // 查询现货全部挂单
	APICostomCancelAllFuturesOrder    = api.APIMethod{Method: "DELETE", Path: "/fapi/v1/allOpenOrders"} // 请求撤销所有期货订单
	APICostomCancelAllSpotOrder       = api.APIMethod{Method: "DELETE", Path: "/api/v3/orders"}         // 请求撤销所有现货订单
	APICostomCancelFuturesOrder       = api.APIMethod{Method: "DELETE", Path: "/fapi/v1/order"}         // 请求撤销期货订单
	APICostomCancelSpotOrder          = api.APIMethod{Method: "DELETE", Path: "/api/v3/order"}          // 请求撤销现货订单
	APICostomGetUMConfig              = api.APIMethod{Method: "GET", Path: "/fapi/v1/symbolConfig"}     // 获取期货交易配置
	APICostomGetUMIncome              = api.APIMethod{Method: "GET", Path: "/fapi/v1/income"}           // 查询UM损益流水
	APICostomModifyFuturesLeverage    = api.APIMethod{Method: "POST", Path: "/fapi/v1/leverage"}        // 修改期货杠杆
	APICostomGetPositionTiers         = api.APIMethod{Method: "GET", Path: "/fapi/v1/leverageBracket"}  // 获取期货仓位档位
	APICostomGetFuturesCommissionRate = api.APIMethod{Method: "GET", Path: "/fapi/v1/commissionRate"}   // 获取期货手续费率
	APIQueryFuturesOrder              = api.APIMethod{Method: "GET", Path: "/fapi/v1/order"}            // 获取期货订单信息
	APIQuerySpotOrder                 = api.APIMethod{Method: "GET", Path: "/api/v3/order"}             // 获取现货订单信息
)

// 通用api
var (
	APIGetFuturesExchangeInfo   = api.APIMethod{Method: "GET", Path: "/fapi/v1/exchangeInfo"}                      // 获取期货交易规则
	APIGetSpotExchangeInfo      = api.APIMethod{Method: "GET", Path: "/api/v3/exchangeInfo"}                       // 获取现货交易规则
	APIGetFuturesFunding        = api.APIMethod{Method: "GET", Path: "/fapi/v1/premiumIndex"}                      // 获取资金费率
	APIGetFuturesFundingHist    = api.APIMethod{Method: "GET", Path: "/fapi/v1/fundingRate"}                       // 获取历史资金费率
	APIGetFuturesFundingInfo    = api.APIMethod{Method: "GET", Path: "/fapi/v1/fundingInfo"}                       // 获取资金费率信息
	APIGetMarginPairs           = api.APIMethod{Method: "GET", Path: "/sapi/v1/margin/allPairs"}                   // 获取杠杆交易品种
	APIGetMarginCollateralRatio = api.APIMethod{Method: "GET", Path: "/sapi/v1/margin/crossMarginCollateralRatio"} // 获取杠杆质押率
	APIGetFuturesPrice          = api.APIMethod{Method: "GET", Path: "/fapi/v1/ticker/price"}                      // 获取期货当前价格
	APIGetSpotPrice             = api.APIMethod{Method: "GET", Path: "/api/v3/ticker/price"}                       // 获取现货当前价格
	APIGetSpotCommissionRate    = api.APIMethod{Method: "GET", Path: "/api/v3/account/commission"}                 // 获取现货手续费率
	APIGetFuturesDepth          = api.APIMethod{Method: "GET", Path: "/fapi/v1/depth"}                             // 查询期货深度
	APIGetSpotDepth             = api.APIMethod{Method: "GET", Path: "/api/v3/depth"}                              // 查询现货深度
	APIGetFuturesKlines         = api.APIMethod{Method: "GET", Path: "/fapi/v1/klines"}                            // 获取期货K线数据
	APIGetSpotKlines            = api.APIMethod{Method: "GET", Path: "/api/v3/klines"}                             // 获取现货K线数据
	APIGetFuturesOpenInterest   = api.APIMethod{Method: "GET", Path: "/fapi/v1/openInterest"}                      // 获取期货持仓量
)
