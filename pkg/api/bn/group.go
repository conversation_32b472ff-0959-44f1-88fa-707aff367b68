package bn

import (
	"GoTrader/pkg/api"
)

var PortfolioBrokerAPIs = api.BnBrokerAPIgroup{
	MarginOrder:        APIMarginOrder,
	FutureOrder:        APIFutureOrder,
	ModifyFuturesOrder: APIModifyFuturesOrder,

	GetListenKey:          APIGetListenKey,
	DelListenKey:          APIDelListenKey,
	PutListenKey:          APIPutListenKey,
	GetBalance:            APIGetBalance,
	GetAccount:            APIGetAccount,
	GetUMAccount:          APIGetUMAccount,
	GetUMPending:          APIGetUMPending,
	GetMarginPending:      APIGetMarginPending,
	CancelAllFuturesOrder: APICancelAllFuturesOrder,
	CancelAllMarginOrder:  APICancelAllMarginOrder,
	CancelFuturesOrder:    APICancelFuturesOrder,
	CancelMarginOrder:     APICancelMarginOrder,
	GetFuturesTrades:      APIGetFuturesTrades,
	GetMarginTrades:       APIGetMarginTrades,
	GetUMConfig:           APIGetUMConfig,
	GetUMIncome:           APIGetUMIncome,
	ModifyFuturesLeverage: APIModifyFuturesLeverage,
	GetPositionTiers:      APIGetUMPositionTiers,

	GetFuturesExchangeInfo:   APIGetFuturesExchangeInfo,
	GetSpotExchangeInfo:      APIGetSpotExchangeInfo,
	GetFuturesFunding:        APIGetFuturesFunding,
	GetFuturesFundingHist:    APIGetFuturesFundingHist,
	GetFuturesFundingInfo:    APIGetFuturesFundingInfo,
	GetMarginPairs:           APIGetMarginPairs,
	GetFuturesPrice:          APIGetFuturesPrice,
	GetSpotPrice:             APIGetSpotPrice,
	GetFuturesCommissionRate: APIGetFuturesCommissionRate,
	GetSpotCommissionRate:    APIGetSpotCommissionRate,
	GetFuturesDepth:          APIGetFuturesDepth,
	GetSpotDepth:             APIGetSpotDepth,
	GetFuturesKlines:         APIGetFuturesKlines,
	GetSpotKlines:            APIGetSpotKlines,
	GetFuturesOpenInterest:   APIGetFuturesOpenInterest,
	QueryFuturesOrder:        APIQueryUMOrder,
	QueryMarginOrder:         APIQueryMarginOrder,
}

// 传统账户api集合, 杠杆交易替换为现货交易
var CostomBrokerAPIs = api.BnBrokerAPIgroup{
	MarginOrder:        APICostomSpotOrder,
	FutureOrder:        APICostomFutureOrder,
	ModifyFuturesOrder: APICostomModifyFuturesOrder,

	GetListenKey: APICostomGetListenKey,
	DelListenKey: APICostomDelListenKey,
	PutListenKey: APICostomPutListenKey,

	GetSpotListenKey: APICostomGetSpotListenKey,
	DelSpotListenKey: APICostomDelSpotListenKey,
	PutSpotListenKey: APICostomPutListenKey,

	GetBalance:            APICostomGetBalance,
	GetAccount:            APICostomGetAccount,
	GetUMAccount:          APICostomGetUMAccount,
	GetUMPending:          APICostomGetUMPending,
	GetMarginPending:      APICostomGetMarginPending,
	CancelAllFuturesOrder: APICostomCancelAllFuturesOrder,
	CancelAllMarginOrder:  APICostomCancelAllSpotOrder,
	CancelFuturesOrder:    APICostomCancelFuturesOrder,
	CancelMarginOrder:     APICostomCancelSpotOrder,
	GetUMConfig:           APICostomGetUMConfig,
	GetUMIncome:           APICostomGetUMIncome,
	ModifyFuturesLeverage: APICostomModifyFuturesLeverage,
	GetPositionTiers:      APICostomGetPositionTiers,

	GetFuturesExchangeInfo:   APIGetFuturesExchangeInfo,
	GetSpotExchangeInfo:      APIGetSpotExchangeInfo,
	GetFuturesFunding:        APIGetFuturesFunding,
	GetFuturesFundingHist:    APIGetFuturesFundingHist,
	GetFuturesFundingInfo:    APIGetFuturesFundingInfo,
	GetMarginPairs:           APIGetMarginPairs,
	GetFuturesPrice:          APIGetFuturesPrice,
	GetSpotPrice:             APIGetSpotPrice,
	GetFuturesCommissionRate: APICostomGetFuturesCommissionRate,
	GetSpotCommissionRate:    APIGetSpotCommissionRate,
	GetFuturesDepth:          APIGetFuturesDepth,
	GetSpotDepth:             APIGetSpotDepth,
	GetFuturesKlines:         APIGetFuturesKlines,
	GetSpotKlines:            APIGetSpotKlines,
	GetFuturesOpenInterest:   APIGetFuturesOpenInterest,
	QueryFuturesOrder:        APIQueryFuturesOrder,
	QueryMarginOrder:         APIQuerySpotOrder,
}
