package logs

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"regexp"
	"time"
)

// 通知标记
const Notify = "@"

// 中文通知格式
type NotifyCNMsg struct {
	Timestamp string `json:"timestamp"`
	Sign      string `json:"sign"`
	MsgType   string `json:"msg_type"`
	Content   struct {
		Post struct {
			ZhCN struct {
				Title   string                `json:"title"`
				Content [][]map[string]string `json:"content"`
			} `json:"zh_cn"`
		} `json:"post"`
	} `json:"content"`
}

func NewNotifyCNPostMsg(title string) *NotifyCNMsg {
	return &NotifyCNMsg{
		Timestamp: "",
		Sign:      "",
		MsgType:   "post",
		Content: struct {
			Post struct {
				ZhCN struct {
					Title   string                `json:"title"`
					Content [][]map[string]string `json:"content"`
				} `json:"zh_cn"`
			} `json:"post"`
		}{
			Post: struct {
				ZhCN struct {
					Title   string                `json:"title"`
					Content [][]map[string]string `json:"content"`
				} `json:"zh_cn"`
			}{
				ZhCN: struct {
					Title   string                `json:"title"`
					Content [][]map[string]string `json:"content"`
				}{
					Title: title,
					// Content: make([][]map[string]string, 0),
					// 初始化一个长度为1的二维切片
					Content: [][]map[string]string{{}},
				},
			},
		},
	}
}

func (m *NotifyCNMsg) AddContent(content []map[string]string) *NotifyCNMsg {
	m.Content.Post.ZhCN.Content = append(m.Content.Post.ZhCN.Content, content)
	return m
}

func (m *NotifyCNMsg) AddText(text string) *NotifyCNMsg {
	m.Content.Post.ZhCN.Content[len(m.Content.Post.ZhCN.Content)-1] = append(m.Content.Post.ZhCN.Content[len(m.Content.Post.ZhCN.Content)-1], map[string]string{"tag": "text", "text": text})
	return m
}

func (m *NotifyCNMsg) AddNotify(personnalId string) *NotifyCNMsg {
	m.Content.Post.ZhCN.Content[len(m.Content.Post.ZhCN.Content)-1] = append(m.Content.Post.ZhCN.Content[len(m.Content.Post.ZhCN.Content)-1], map[string]string{"tag": "at", "user_id": personnalId})
	return m
}

func (m *NotifyCNMsg) AddSign(secret string) *NotifyCNMsg {
	timestamp := time.Now().Unix()
	sign, _ := GenSign(secret, timestamp)
	m.Timestamp = fmt.Sprintf("%v", timestamp)
	m.Sign = sign
	return m
}

// 飞书签名生成
func GenSign(secret string, timestamp int64) (string, error) {
	//timestamp + key 做sha256, 再进行base64 encode
	stringToSign := fmt.Sprintf("%v", timestamp) + "\n" + secret

	var data []byte
	h := hmac.New(sha256.New, []byte(stringToSign))
	_, err := h.Write(data)
	if err != nil {
		return "", err
	}

	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return signature, nil
}

// 飞书Writer
type FeishuWriter struct {
	// 通知人
	PersonnalIds []string
	// 通知组hook
	GroupHook string
	// 签名
	Secret string
	// 主题
	Title string
	// 日志等级
	Level slog.Level
}

func NewFeishuWriter(ids []string, group string, secret string, title string, level slog.Level) *FeishuWriter {
	return &FeishuWriter{
		PersonnalIds: ids,
		GroupHook:    group,
		Secret:       secret,
		Title:        title,
		Level:        level,
	}
}

func (w *FeishuWriter) Write(p []byte) (n int, err error) {
	// 将日志记录转换为飞书消息格式
	level := slog.LevelInfo
	re := regexp.MustCompile(`"level":"(\w+)"`)
	match := re.FindStringSubmatch(string(p))
	if len(match) > 1 {
		// 将字符串转换为日志级别
		level.UnmarshalText([]byte(match[1]))
	}
	if level < w.Level {
		return len(p), nil
	}
	msg := NewNotifyCNPostMsg(w.Title)
	for _, id := range w.PersonnalIds {
		msg.AddNotify(id)
	}
	msg.AddText(string(p))
	msg.AddSign(w.Secret)
	// 转换为json
	jsonMsg, _ := json.Marshal(msg)
	// 将json发送到hook中
	request, err := http.NewRequest("POST", w.GroupHook, bytes.NewBuffer(jsonMsg))
	if err != nil {
		fmt.Printf("Error creating request: %v \n", err)
		return len(p), nil
	}
	request.Header.Add("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(request)
	if err != nil {
		fmt.Printf("Error sending request: %v \n", err)
		return len(p), nil
	}
	defer resp.Body.Close()
	return len(p), nil
}
