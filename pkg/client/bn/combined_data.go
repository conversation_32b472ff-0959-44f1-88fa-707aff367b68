package bn

import (
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/handler"
	"context"
	"log/slog"
)

var _ client.DataClient = (*BnCombinedDataClient)(nil) // 确保实现了DataClient接口

// 币安数据源客户端
type BnCombinedDataClient struct {
	futuresClient client.DataClient
	spotClient    client.DataClient
}

func NewCombinedDataClient(fd client.DataClient, sd client.DataClient) *BnCombinedDataClient {
	c := &BnCombinedDataClient{
		futuresClient: fd,
		spotClient:    sd,
	}
	return c
}

// 启动数据源
func (c *BnCombinedDataClient) Run(ctx context.Context) (err error) {
	err = c.futuresClient.Run(ctx)
	if err != nil {
		return err
	}
	err = c.spotClient.Run(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (c *BnCombinedDataClient) AddStreamHandler(event handler.WSStreamType, handler func([]byte, *data.SymbolData, *slog.Logger) error) client.DataClient {
	c.futuresClient.AddStreamHandler(event, handler)
	c.spotClient.AddStreamHandler(event, handler)
	return c
}

// 订阅数据源
func (c *BnCombinedDataClient) SubscribeDatas(ds []*data.SymbolData) (err error) {
	fds := []*data.SymbolData{}
	sds := []*data.SymbolData{}
	for _, d := range ds {
		switch d.DataType {
		case data.Futures:
			fds = append(fds, d)
		case data.Spot:
			sds = append(sds, d)
		default:
			continue
		}
	}
	err = c.futuresClient.SubscribeDatas(fds)
	if err != nil {
		return err
	}
	err = c.spotClient.SubscribeDatas(sds)
	if err != nil {
		return err
	}
	return nil
}

// 取消订阅数据
func (c *BnCombinedDataClient) UnsubscribeDatas(ds []*data.SymbolData) (err error) {
	fds := []*data.SymbolData{}
	sds := []*data.SymbolData{}
	for _, d := range ds {
		switch d.DataType {
		case data.Futures:
			fds = append(fds, d)
		case data.Spot:
			sds = append(sds, d)
		default:
			continue
		}
	}
	err = c.futuresClient.UnsubscribeDatas(fds)
	if err != nil {
		return err
	}
	err = c.spotClient.UnsubscribeDatas(sds)
	if err != nil {
		return err
	}
	return nil
}
