package bn

// 订单请求
type OrderRequest struct {
	Symbol      string `json:"symbol,omitempty"`
	Side        string `json:"side,omitempty"`
	PosSide     string `json:"positionSide,omitempty"`
	OrdType     string `json:"type,omitempty"`
	Size        string `json:"quantity,omitempty"`
	Price       string `json:"price,omitempty"`
	OrdId       string `json:"newClientOrderId,omitempty"`
	TimeInForce string `json:"timeInForce,omitempty"`
	ApiKey      string `json:"apiKey,omitempty"`
	TimeStamp   int64  `json:"timestamp,omitempty"`
	Signature   string `json:"signature,omitempty"`
	PriceMatch  string `json:"priceMatch,omitempty"`
}

type ModifyOrderRequest struct {
	ClientOrderId string `json:"origClientOrderId"`
	Symbol        string `json:"symbol"`
	Side          string `json:"side"`
	Size          string `json:"quantity"`
	Price         string `json:"price"`
}

type ModifyOrdersRequest []ModifyOrderRequest
