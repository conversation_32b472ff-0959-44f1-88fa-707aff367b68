package bn

import (
	"GoTrader/pkg/handler"
	"GoTrader/pkg/handler/bn"
	"strings"
)

// 账户信息推送
type BrokerWebSocketEvent struct {
	Event     string `json:"e"`
	EventTime int64  `json:"E"`
}

func (e *BrokerWebSocketEvent) GetEvent() handler.WSEventType {
	return handler.WSEventType(e.Event)
}

// 全市场信息推送
type MarketStreamEvent struct {
	Stream string `json:"stream"`
}

func (e *MarketStreamEvent) GetEvent() handler.WSStreamType {
	return handler.WSStreamType(e.Stream)
}

// 数据订阅信息推送
type StreamEvent struct {
	Stream string `json:"stream"`
	Data   struct {
		Event  string `json:"e"`
		Symbol string `json:"s"`
		Time   int64  `json:"E"`
	} `json:"data"`
}

func (e *StreamEvent) GetEvent() handler.WSStreamType {
	event := handler.WSStreamType(e.Data.Event)
	// 深度信息特殊处理
	if event == "" && e.Stream != "" {
		if strings.Split(e.Stream, "@")[1] == "depth5" || strings.Split(e.Stream, "@")[1] == "depth" {
			return bn.Depth
		}
	}
	return event
}

func (e *StreamEvent) GetSymbol() string {
	symbol := e.Data.Symbol
	// 深度信息特殊处理
	if symbol == "" && e.Stream != "" {
		if strings.Split(e.Stream, "@")[1] == "depth5" || strings.Split(e.Stream, "@")[1] == "depth" {
			return strings.ToUpper(strings.Split(e.Stream, "@")[0])
		}
	}
	return symbol
}
