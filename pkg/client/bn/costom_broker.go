package bn

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/handler"
	"context"
	"log/slog"
)

var _ client.BrokerClient = (*BnCostomBrokerClient)(nil) // 确保实现了DataClient接口

// 用于监听传统broker的client
// 包含两个监听对象
type BnCostomBrokerClient struct {
	futuresClient client.BrokerClient // 期货监听对象
	spotClient    client.BrokerClient // 现货监听对象
}

// 创建客户端
func NewCostomBrokerClient(fc client.BrokerClient, sc client.BrokerClient) client.BrokerClient {
	c := &BnCostomBrokerClient{
		futuresClient: fc,
		spotClient:    sc,
	}
	return c
}

// 注册Broker事件处理函数
func (c *BnCostomBrokerClient) AddEventHandler(event handler.WSEventType, handler func([]byte, *broker.Broker, *slog.Logger) error) client.BrokerClient {
	c.futuresClient.AddEventHandler(event, handler)
	c.spotClient.AddEventHandler(event, handler)
	return c
}

// 添加broker
func (c *BnCostomBrokerClient) AddBroker(b *broker.Broker) client.BrokerClient {
	c.futuresClient.AddBroker(b)
	c.spotClient.AddBroker(b)
	return c
}

// 启动broker数据源
func (c *BnCostomBrokerClient) Run(ctx context.Context) (err error) {
	err = c.futuresClient.Run(ctx)
	if err != nil {
		return err
	}
	err = c.spotClient.Run(ctx)
	if err != nil {
		return err
	}
	return nil
}
