package bn

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/order"
	"strconv"
)

// 账户资产响应
type BalanceResponse []struct {
	Asset  string `json:"asset"`
	Total  string `json:"totalWalletBalance"`
	Free   string `json:"crossMarginFree"`
	Locked string `json:"crossMarginLocked"`
	UM     string `json:"umWalletBalance"`
	CM     string `json:"cmWalletBalance"`
}

// 账户信息响应
type AccountResponse struct {
	Equity         string `json:"accountEquity"`
	Available      string `json:"totalAvailableBalance"`
	MarginOpenLoss string `json:"totalMarginOpenLoss"`
}

// 账户UM信息响应 V2版本
type UMAccountResponse struct {
	Positions []struct {
		Symbol           string `json:"symbol"`
		PosSide          string `json:"positionSide"`
		UnrealizedProfit string `json:"unrealizedProfit"`
		Notional         string `json:"notional"`
		Size             string `json:"positionAmt"`
	} `json:"positions"`
}

func (r BalanceResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range r {
		if _, exists := b.AssetWallets[item.Asset]; !exists {
			b.AssetWallets[item.Asset] = &broker.AssetWallet{}
		}
		b.AssetWallets[item.Asset].Asset = item.Asset
		b.AssetWallets[item.Asset].Free, _ = strconv.ParseFloat(item.Free, 64)
		b.AssetWallets[item.Asset].Locked, _ = strconv.ParseFloat(item.Locked, 64)
		b.AssetWallets[item.Asset].UM, _ = strconv.ParseFloat(item.UM, 64)
		b.AssetWallets[item.Asset].CM, _ = strconv.ParseFloat(item.CM, 64)
	}
}

func (r AccountResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	b.Account.Equity, _ = strconv.ParseFloat(r.Equity, 64)
	b.Account.Available, _ = strconv.ParseFloat(r.Available, 64)
	b.Account.MarginOpenLoss, _ = strconv.ParseFloat(r.MarginOpenLoss, 64)
}

func (r UMAccountResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range r.Positions {
		if _, exists := b.Positions[item.Symbol]; !exists {
			b.Positions[item.Symbol] = &broker.UMPosition{Symbol: item.Symbol}
		}
		posObj := b.Positions[item.Symbol]
		if order.PosSide(item.PosSide) == order.LONG {
			posObj.SizeLong, _ = strconv.ParseFloat(item.Size, 64)
		}
		if order.PosSide(item.PosSide) == order.SHORT {
			posObj.SizeShort, _ = strconv.ParseFloat(item.Size, 64)
		}

		if order.PosSide(item.PosSide) == order.BOTH {
			posObj.SizeLong, _ = strconv.ParseFloat(item.Size, 64)
			posObj.SizeShort, _ = strconv.ParseFloat(item.Size, 64)
			posObj.SizeLong = max(posObj.SizeLong, 0)
			posObj.SizeShort = min(posObj.SizeShort, 0)
		}
		// 开仓价格调整
		notional, _ := strconv.ParseFloat(item.Notional, 64)
		pnl, _ := strconv.ParseFloat(item.UnrealizedProfit, 64)
		if posObj.SizeLong == 0 {
			posObj.OpenPriceLong = 0
		} else {
			posObj.OpenPriceLong = (notional - pnl) / posObj.SizeLong
		}
		if posObj.SizeShort == 0 {
			posObj.OpenPriceShort = 0
		} else {
			posObj.OpenPriceShort = (notional - pnl) / posObj.SizeShort
		}
	}
}
