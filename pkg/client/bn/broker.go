package bn

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/handler"
	"GoTrader/pkg/utils"
	"GoTrader/pkg/wsclient"
	"context"
	"encoding/json"
	"errors"
	"log/slog"
	"time"
)

var _ client.BrokerClient = (*BnBrokerClient)(nil) // 确保实现了DataClient接口

// 用于监听broker的client
type BnBrokerClient struct {
	url              string                                                                     // wsConnector监听 url
	ws               wsclient.WSManager                                                         // WebSocket 监听连接实例
	getListenKeyFunc func(b *broker.Broker) (string, error)                                     // 获取listenkey方法
	updateBrokerFunc func(b *broker.Broker) error                                               // 更新broker方法
	broker           *broker.Broker                                                             // 统一账户对象
	eventHandlers    map[handler.WSEventType][]func([]byte, *broker.Broker, *slog.Logger) error // 事件处理函数, map[事件类型]处理函数
	logger           *slog.Logger                                                               // 日志对象
	err              error                                                                      // 错误对象
}

// 账户信息推送
type BrokerWSEvent struct {
	Event     string `json:"e"`
	EventTime int64  `json:"E"`
}

func (e *BrokerWSEvent) GetEvent() handler.WSEventType {
	return handler.WSEventType(e.Event)
}

// 创建客户端
func NewBrokerClient(
	url string, wsConnector wsclient.WSManager,
	listenKeyFunc func(b *broker.Broker) (string, error),
	updateBrokerFunc func(b *broker.Broker) error,
	logger *slog.Logger) *BnBrokerClient {
	logger = logger.With("client", "bnBroker").With("url", url)
	c := &BnBrokerClient{
		url:              url,
		ws:               wsConnector,
		getListenKeyFunc: listenKeyFunc,
		updateBrokerFunc: updateBrokerFunc,
		broker:           nil,
		eventHandlers:    map[handler.WSEventType][]func([]byte, *broker.Broker, *slog.Logger) error{},
		logger:           logger,
		err:              nil,
	}
	wsConnector.SetReInitFunc(c.ReInitBroker)
	return c
}

// 注册Broker事件处理函数
func (c *BnBrokerClient) AddEventHandler(event handler.WSEventType, handler func([]byte, *broker.Broker, *slog.Logger) error) client.BrokerClient {
	c.eventHandlers[event] = append(c.eventHandlers[event], handler)
	return c
}

// 添加broker
func (c *BnBrokerClient) AddBroker(b *broker.Broker) client.BrokerClient {
	if c.broker != nil {
		c.logger.Warn("broker is not empty, skip add broker")
		return c
	}
	c.broker = b
	return c
}

// 启动broker数据源
func (c *BnBrokerClient) Run(ctx context.Context) (err error) {
	err = c.connect(ctx)
	if err != nil {
		c.logger.Error("error connect ws", "error", err)
		return err
	}
	c.listen(ctx)
	return nil
}

// 连接broker数据源
func (c *BnBrokerClient) connect(ctx context.Context) error {
	if c.broker == nil {
		c.logger.Warn("broker is empty, skip connect")
		return errors.New("broker is empty")
	}
	// 获取listenkey
	listenKey, err := c.getListenKeyFunc(c.broker)
	if err != nil {
		c.logger.Error("get listenKey failed", "error", err)
		return err
	}
	wsListenURL := utils.URLJoin(c.url, listenKey)
	// 建立WebSocket连接
	logger := c.logger.With("listenKey", listenKey)
	err = c.ws.Connect(ctx, wsListenURL, logger)
	if err != nil {
		c.logger.Error("error connect broker ws", "error", err)
		return err
	}
	// 每15min延长一次listenkey有效期
	go func() {
		ticker := time.NewTicker(15 * time.Minute)
		defer ticker.Stop()
		for {
			select {
			case <-ctx.Done():
				c.logger.Info("Broker ws context canceled")
				return
			case <-ticker.C:
				resp, err := c.getListenKeyFunc(c.broker)
				if err != nil {
					logger.Warn("Error renewing listenkey", "error", err)
				}
				logger.Info("listen key Keepalive response", "response", resp)
			}
		}
	}()
	return nil
}

// 开始监听broker
func (c *BnBrokerClient) listen(ctx context.Context) {
	go func() {
		for {
			// 读取 WebSocket 消息
			_, msg, err := c.ws.ReadMessage()

			if err != nil {
				if ctx.Err() != nil {
					c.logger.Info("Broker ws context canceled", "error", err)
					return
				}

				if errors.Is(err, wsclient.ErrReconnected) {
					// 重连成功
					c.logger.Info("Broker ws reconnect success")
					continue
				}
				c.logger.Error("Error reading Broker WebSocket message", "error", err)
				continue
			}

			// 处理 WebSocket 事件
			WebSocketEvent := &BrokerWebSocketEvent{}
			err = json.Unmarshal(msg, WebSocketEvent)
			if err != nil {
				c.logger.Warn("Error unmarshalling Broker WebSocket message", "error", err, "message", string(msg))
				continue
			}
			// 执行事件处理函数
			event := WebSocketEvent.GetEvent()
			if handlers, ok := c.eventHandlers[event]; ok {
				for _, handler := range handlers {
					handler(msg, c.broker, c.logger)
				}
				continue
			}
			c.logger.Warn("Broker Unknown event", "event", event, "message", string(msg))
		}
	}()
}

func (c *BnBrokerClient) ReInitBroker(_ context.Context, ver int64) error {
	// api请求时间较短,无需上下文管理
	return c.updateBrokerFunc(c.broker)
}
