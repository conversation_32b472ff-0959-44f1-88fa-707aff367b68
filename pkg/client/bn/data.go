package bn

import (
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/handler"
	"GoTrader/pkg/utils"
	"GoTrader/pkg/wsclient"
	"context"
	"fmt"
	"log/slog"
	"sync"
)

var _ client.DataClient = (*BnDataClient)(nil) // 确保实现了DataClient接口

// 币安数据源客户端
type BnDataClient struct {
	url      string                                                                        // WebsocketURL
	ws       wsclient.WSManager                                                            // WebSocket 行情推送连接实例
	channels []string                                                                      // 订阅的数据频道
	datas    map[string]*data.SymbolData                                                   // 数据对象, map[交易对]数据对象
	handlers map[handler.WSStreamType][]func([]byte, *data.SymbolData, *slog.Logger) error // 数据处理函数, map[事件类型][交易对]处理函数
	mu       *sync.RWMutex                                                                 // 互斥锁
	token    *utils.TokenBucket                                                            // 令牌桶
	logger   *slog.Logger                                                                  // 日志对象
}

func NewDataClient(url string, channels []string, ws wsclient.WSManager, logger *slog.Logger) *BnDataClient {
	logger = logger.With("client", "bnData").With("url", url).With("channels", fmt.Sprintf("%v", channels))
	c := &BnDataClient{
		url:      url,
		ws:       ws,
		channels: channels,
		datas:    make(map[string]*data.SymbolData),
		handlers: map[handler.WSStreamType][]func([]byte, *data.SymbolData, *slog.Logger) error{},
		mu:       &sync.RWMutex{},
		token:    nil,
		logger:   logger,
	}
	ws.SetReInitFunc(c.reSubscribeDatas)
	return c
}

// 启动数据源
func (c *BnDataClient) Run(ctx context.Context) (err error) {
	err = c.connect(ctx)
	if err != nil {
		return err
	}
	c.listen(ctx)
	return nil
}

func (c *BnDataClient) AddStreamHandler(event handler.WSStreamType, handler func([]byte, *data.SymbolData, *slog.Logger) error) client.DataClient {
	c.handlers[event] = append(c.handlers[event], handler)
	return c
}

// 订阅数据源
func (c *BnDataClient) SubscribeDatas(ds []*data.SymbolData) error {
	// 先将数据源添加到map中
	c.mu.Lock()
	for _, d := range ds {
		c.datas[d.Symbol] = d
	}
	c.mu.Unlock()
	for _, channel := range c.channels {
		ctx, cancel := context.WithCancel(context.Background())
		ver := c.ws.GetVersion()
		err := writeWSChannelMessage(ctx, ver, c.ws, ds, c.token, "SUBSCRIBE", channel, c.logger)
		cancel()
		if err != nil {
			c.logger.Error("SubScribe aggTrade error")
			return err
		}
	}
	return nil
}

// 取消订阅数据
func (c *BnDataClient) UnsubscribeDatas(ds []*data.SymbolData) error {
	for _, channel := range c.channels {
		ctx, cancel := context.WithCancel(context.Background())
		ver := c.ws.GetVersion()
		err := writeWSChannelMessage(ctx, ver, c.ws, ds, c.token, "UNSUBSCRIBE", channel, c.logger)
		cancel()
		if err != nil {
			c.logger.Error("UnsubScribe aggTrade error")
			return err
		}
	}
	return nil
}

// 连接数据源
func (c *BnDataClient) connect(ctx context.Context) error {

	c.token = utils.NewTokenBucket(ctx, 10, 10)
	err := c.ws.Connect(ctx, c.url, c.logger)
	if err != nil {
		c.logger.Error("error connect ws", "error", err)
		return err
	} else {
		c.logger.Info("connect ws success")
	}
	return nil
}

// 监听行情
func (c *BnDataClient) listen(ctx context.Context) {
	go listenStreamWS(ctx, c.ws, c.handlers, c.datas, c.mu, c.logger)
}

// 重新订阅已有数据
func (c *BnDataClient) reSubscribeDatas(ctx context.Context, ver int64) error {
	// 将datamap中的data生成列表
	ds := make([]*data.SymbolData, 0, len(c.datas))
	for _, v := range c.datas {
		ds = append(ds, v)
	}
	for _, channel := range c.channels {
		err := writeWSChannelMessage(ctx, ver, c.ws, ds, c.token, "SUBSCRIBE", channel, c.logger)
		if err != nil {
			c.logger.Error("ResubScribe aggTrade error")
			return err
		}
	}
	return nil
}
