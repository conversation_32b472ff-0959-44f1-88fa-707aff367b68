package bn

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/order"
	"GoTrader/pkg/trade"
	"GoTrader/pkg/utils"
	"errors"
	"math"
	"strconv"
)

// 通用api响应对象

// 定义错误类型
type HTTPStatusError struct {
	StatusCode int // HTTP 状态码（如果是状态码错误）
	Msg        ErrMsg
}

type ErrMsg struct {
	Code int64  `json:"code"`
	Msg  string `json:"msg"`
}

func (e *ErrMsg) String() string {
	// 返回code: msg字符串
	return "Code: " + strconv.FormatInt(e.Code, 10) + "Message: " + e.Msg
}

func (e HTTPStatusError) Error() string {
	// 返回statusCode + msg字符串
	return "HTTPStatusError: " + strconv.Itoa(e.StatusCode) + ": " + e.Msg.String()
}

// ListenKey响应
type ListenKeyResponse struct {
	ListenKey string `json:"listenKey"`
}

// 订单响应
type OrderResponse struct {
	ClOrdId      string `json:"clientOrderId"`
	OrdId        int64  `json:"orderId"`
	ExecutedSize string `json:"executedQty"`
	AvgPrice     string `json:"avgPrice"`
	Size         string `json:"origQty"`
	Price        string `json:"price"`
	Side         string `json:"side"`
	PosSide      string `json:"positionSide"`
	CumQuote     string `json:"cummulativeQuoteQty"`
	Status       string `json:"status"`
	Symbol       string `json:"symbol"`
	OrdType      string `json:"type"`
	TimeInForce  string `json:"timeInForce"`
	PriceMatch   string `json:"priceMatch"`
	UpdateTime   int64  `json:"updateTime"`
}

// 期货交易所信息响应
type FuturesExchangeInfoResponse struct {
	Symbols []struct {
		Symbol            string           `json:"symbol"`
		Status            string           `json:"status"`
		PricePrecision    int              `json:"pricePrecision"`
		QuantityPrecision int              `json:"quantityPrecision"`
		Filters           []ExchangeFilter `json:"filters"`
	} `json:"symbols"`
}

// 现货交易所信息响应
type SpotExchangeInfoResponse struct {
	Symbols []struct {
		Symbol  string           `json:"symbol"`
		Status  string           `json:"status"`
		Filters []ExchangeFilter `json:"filters"`
	} `json:"symbols"`
}

type FilterType string

const (
	PRICE_FILTER          FilterType = "PRICE_FILTER"
	LOT_SIZE              FilterType = "LOT_SIZE"
	MARKET_LOT_SIZE       FilterType = "MARKET_LOT_SIZE"
	MAX_NUM_ORDERS        FilterType = "MAX_NUM_ORDERS"
	MAX_NUM_ALGO_ORDERS   FilterType = "MAX_NUM_ALGO_ORDERS"
	MIN_NOTIONAL          FilterType = "MIN_NOTIONAL"
	NOTIONAL              FilterType = "NOTIONAL"
	PERCENT_PRICE         FilterType = "PERCENT_PRICE"
	PERCENT_PRICE_BY_SIDE FilterType = "PERCENT_PRICE_BY_SIDE"
)

// 交易规则
type ExchangeFilter struct {
	FilterType        string `json:"filterType"`
	MinPrice          string `json:"minPrice,omitempty"`
	MaxPrice          string `json:"maxPrice,omitempty"`
	MultiPlierUp      string `json:"multiplierUp,omitempty"`
	MultiPlierDown    string `json:"multiplierDown,omitempty"`
	MultiPlierDecimal string `json:"multiplierDecimal,omitempty"`
	BidMultiplierUp   string `json:"bidMultiplierUp,omitempty"`
	BidMultiplierDown string `json:"bidMultiplierDown,omitempty"`
	AskMultiplierUp   string `json:"askMultiplierUp,omitempty"`
	AskMultiplierDown string `json:"askMultiplierDown,omitempty"`
	TickSize          string `json:"tickSize,omitempty"`
	MinQty            string `json:"minQty,omitempty"`
	MaxQty            string `json:"maxQty,omitempty"`
	StepSize          string `json:"stepSize,omitempty"`
	Notional          string `json:"notional,omitempty"`
	MinNotional       string `json:"minNotional,omitempty"`
}

// 资金费率响应
type FuturesFundingResponse []struct {
	Symbol      string `json:"symbol"`
	Funding     string `json:"lastFundingRate"`
	FundingTime int64  `json:"nextFundingTime"`
}

// 历史资金费率响应
type FuturesFundingHistResponse []struct {
	Symbol      string `json:"symbol"`
	FundingTime int64  `json:"fundingTime"`
}

// 资金费率信息响应
type FuturesFundingInfoResponse []oneFuturesFundingInfoResponse
type oneFuturesFundingInfoResponse struct {
	Symbol               string `json:"symbol"`
	FundingIntervalHours int64  `json:"fundingIntervalHours"`
	RateCap              string `json:"adjustedFundingRateCap"`
	RateFloor            string `json:"adjustedFundingRateFloor"`
}

// 期货交易记录响应
type UmTradeResponse struct {
	Symbol          string `json:"symbol"`
	Price           string `json:"price"`
	Size            string `json:"qty"`
	Time            int64  `json:"time"`
	Commission      string `json:"commission"`
	CommissionAsset string `json:"commissionAsset"`
	IsMaker         bool   `json:"maker"`
	IsBuy           bool   `json:"buyer"`
	PosSide         string `json:"positionSide"`
	Pnl             string `json:"realizedPnl"`
}

// 期货交易记录响应
type UmTradesResponse []UmTradeResponse

// 期货挂单查询响应
type BrokerUMPendingResponse []OrderResponse

// 现货挂单查询响应
type BrokerMarginPendingResponse []OrderResponse

// 期货订单查询响应
type FuturesOrderResponse OrderResponse

// 杠杆订单查询响应
type MarginOrderResponse OrderResponse

// 期货交易对配置响应
type BrokerUMConfigResponse []struct {
	Symbol   string `json:"symbol"`
	Leverage int64  `json:"leverage"`
}

// 杠杆交易品种查询
type MarginPairsResponse []struct {
	Symbol     string `json:"symbol"`
	Base       string `json:"base"`
	Quote      string `json:"quote"`
	AllowTrade bool   `json:"isMarginTrade"`
	AllowBuy   bool   `json:"isBuyAllowed"`
	AllowSell  bool   `json:"isSellAllowed"`
}

// 期货价格查询响应
type FuturesPriceResponce []struct {
	Symbol string `json:"symbol"`
	Price  string `json:"price"`
}

// 现货价格查询响应
type SpotPriceResponce []struct {
	Symbol string `json:"symbol"`
	Price  string `json:"price"`
}

// K线响应
type KLineResponse [][]any

type OpenInterestResponse struct {
	Symbol string `json:"symbol"`
	OI     string `json:"openInterest"`
	Time   int64  `json:"time"`
}

// 期货杠杆档位响应
type PositionTiersResponse []struct {
	Symbol   string `json:"symbol"`
	Brackets []struct {
		Bracket          int64   `json:"bracket"`
		InitialLeverage  int64   `json:"initialLeverage"`
		MaintMarginRatio float64 `json:"maintMarginRatio"`
		NotionalCap      float64 `json:"notionalCap"`
		NotionalFloor    float64 `json:"notionalFloor"`
		Cum              float64 `json:"cum"`
	} `json:"brackets"`
}

type FuturesCommissionRateResponse struct {
	Symbol string `json:"symbol"`
	Maker  string `json:"makerCommissionRate"`
	Taker  string `json:"takerCommissionRate"`
}

type SpotCommissionRateResponse struct {
	Symbol   string `json:"symbol"`
	Standard struct {
		Maker string `json:"maker"`
		Taker string `json:"taker"`
	} `json:"standardCommission"`
}

type DepthResponse struct {
	LastUpdateId int64      `json:"lastUpdateId"`
	Bids         [][]string `json:"bids"`
	Asks         [][]string `json:"asks"`
}

func (r BrokerUMPendingResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range r {
		// 更新订单状态
		o := b.FuturesPending.Pending[item.ClOrdId]
		if o == nil {
			o = &order.FuturesOrder{
				ClOrdId:     item.ClOrdId,
				Symbol:      item.Symbol,
				Side:        order.Side(item.Side),
				OrdType:     order.OrdType(item.OrdType),
				TimeInForce: order.TimeInForce(item.TimeInForce),
				PosSide:     order.PosSide(item.PosSide),
				PriceMatch:  order.PriceMatch(item.PriceMatch),
			}
			b.FuturesPending.Pending[item.ClOrdId] = o
		}
		o.Status = order.OrdStatus(item.Status)
		o.ExecutedSize, _ = strconv.ParseFloat(item.ExecutedSize, 64)
		o.AvgPrice, _ = strconv.ParseFloat(item.AvgPrice, 64)
		o.Price, _ = strconv.ParseFloat(item.Price, 64)
		o.Size, _ = strconv.ParseFloat(item.Size, 64)
	}
}

func (r BrokerMarginPendingResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range r {
		// 更新订单状态
		o := b.MarginPending.Pending[item.ClOrdId]
		if o == nil {
			o = &order.MarginOrder{
				ClOrdId:     item.ClOrdId,
				Symbol:      item.Symbol,
				Side:        order.Side(item.Side),
				OrdType:     order.OrdType(item.OrdType),
				TimeInForce: order.TimeInForce(item.TimeInForce),
			}
			b.MarginPending.Pending[item.ClOrdId] = o
		}
		o.Status = order.OrdStatus(item.Status)
		o.ExecutedSize, _ = strconv.ParseFloat(item.ExecutedSize, 64)
		o.Price, _ = strconv.ParseFloat(item.Price, 64)
		o.Size, _ = strconv.ParseFloat(item.Size, 64)
		cumQuote, _ := strconv.ParseFloat(item.CumQuote, 64)
		o.AvgPrice = cumQuote / o.ExecutedSize
	}
}

// 更新交易信息
func (r *FuturesExchangeInfoResponse) UpdateData(ds []*data.SymbolData) {
	// data list转换为map
	dataMap := make(map[string]*data.ExchangeInfo)
	for _, d := range ds {
		dataMap[d.Symbol] = d.Info
	}
	// 遍历filters更新data
	for _, info := range r.Symbols {
		d := dataMap[info.Symbol]
		if d == nil {
			continue
		}
		// filters list转换为map
		filterMap := make(map[FilterType]*ExchangeFilter)
		for _, filter := range info.Filters {
			filterMap[FilterType(filter.FilterType)] = &filter
		}
		d.CtVal = 1
		d.Status = data.ContractStatus(info.Status)
		d.PricePrecision = info.PricePrecision
		d.QuantityPrecision = info.QuantityPrecision
		d.TickSize = utils.StringToFloat64(filterMap[PRICE_FILTER].TickSize)
		d.StepSize = utils.StringToFloat64(filterMap[LOT_SIZE].StepSize)
		d.MinNotional = utils.StringToFloat64(filterMap[MIN_NOTIONAL].Notional)
		d.MaxPricePercent = utils.StringToFloat64(filterMap[PERCENT_PRICE].MultiPlierUp)
		d.MinPricePercent = utils.StringToFloat64(filterMap[PERCENT_PRICE].MultiPlierDown)
		d.MaxPrice = utils.StringToFloat64(filterMap[PRICE_FILTER].MaxPrice)
		d.MinPrice = utils.StringToFloat64(filterMap[PRICE_FILTER].MinPrice)
	}
}

func (rs *SpotExchangeInfoResponse) UpdateData(ds []*data.ExchangeInfo) {
	// data list转换为map
	dataMap := make(map[string]*data.ExchangeInfo)
	for _, d := range ds {
		dataMap[d.Symbol] = d
	}
	// 遍历filters更新data
	for _, info := range rs.Symbols {
		d := dataMap[info.Symbol]
		if d == nil {
			continue
		}
		// filters list转换为map
		filterMap := make(map[FilterType]*ExchangeFilter)
		for _, filter := range info.Filters {
			filterMap[FilterType(filter.FilterType)] = &filter
		}
		d.CtVal = 1
		d.Status = data.ContractStatus(info.Status)
		d.TickSize = utils.StringToFloat64(filterMap[PRICE_FILTER].TickSize)
		d.StepSize = utils.StringToFloat64(filterMap[LOT_SIZE].StepSize)
		d.MinNotional = utils.StringToFloat64(filterMap[NOTIONAL].MinNotional)
		// 根据stepsize确定数量精度
		d.QuantityPrecision = int(math.Log10(1 / d.StepSize))
		// 根据ticksize确定价格精度
		d.PricePrecision = int(math.Log10(1 / d.TickSize))
		d.MinPrice = utils.StringToFloat64(filterMap[PRICE_FILTER].MinPrice)
		d.MaxPrice = utils.StringToFloat64(filterMap[PRICE_FILTER].MaxPrice)
		d.MinPricePercent = utils.StringToFloat64(filterMap[PERCENT_PRICE_BY_SIDE].BidMultiplierDown)
		d.MaxPricePercent = utils.StringToFloat64(filterMap[PERCENT_PRICE_BY_SIDE].AskMultiplierUp)

	}
}

// 更新资金费率
func (rs FuturesFundingResponse) UpdateData(ds []*data.SymbolData) {
	dsMap := make(map[string]*data.SymbolData)
	for _, d := range ds {
		dsMap[d.Symbol] = d
	}
	for _, r := range rs {
		d := dsMap[r.Symbol]
		if d == nil {
			continue
		}
		d.Funding.Rate = utils.StringToFloat64(r.Funding)
		d.Funding.Time = r.FundingTime
	}
}

// 更新历史资金费率
func (rs FuturesFundingHistResponse) UpdateData(d *data.SymbolData) {
	// 新品种可能没有收取过资金费
	if len(rs) == 0 {
		return
	}
	d.Funding.PrevTime = rs[0].FundingTime
}

// 更新资金费率信息
func (rs FuturesFundingInfoResponse) UpdateData(ds []*data.SymbolData) {
	infoMap := make(map[string]oneFuturesFundingInfoResponse)
	for _, r := range rs {
		infoMap[r.Symbol] = r
	}
	for _, d := range ds {
		info, ok := infoMap[d.Symbol]
		if !ok {
			d.Funding.PrevTime = d.Funding.Time - 8*60*60*1000 // 默认8h
			// 默认的上下限计算延后至获取杠杆分层标准时计算
		} else {
			d.Funding.Floor = utils.StringToFloat64(info.RateFloor)
			d.Funding.Cap = utils.StringToFloat64(info.RateCap)
			d.Funding.PrevTime = d.Funding.Time - info.FundingIntervalHours*60*60*1000
		}
	}
}

func (rs UmTradesResponse) ToTrade() []*trade.FuturesTrade {
	var data []*trade.FuturesTrade
	for _, item := range rs {
		data = append(data, &trade.FuturesTrade{
			Symbol:          item.Symbol,
			Price:           utils.StringToFloat64(item.Price),
			Size:            utils.StringToFloat64(item.Size),
			Time:            item.Time,
			Commission:      utils.StringToFloat64(item.Commission),
			CommissionAsset: item.CommissionAsset,
			IsMaker:         item.IsMaker,
			IsBuy:           item.IsBuy,
			IsLong:          item.PosSide == "LONG",
			Pnl:             utils.StringToFloat64(item.Pnl),
		})
	}
	return data
}

func (rs BrokerUMConfigResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range rs {
		b.Leverages[item.Symbol] = item.Leverage
	}
}

func (rs *FuturesPriceResponce) UpdateData(ds []*data.SymbolData) {
	dsMap := make(map[string]*data.SymbolData)
	for _, d := range ds {
		dsMap[d.Symbol] = d
	}
	for _, r := range *rs {
		d := dsMap[r.Symbol]
		if d == nil {
			continue
		}
		d.Price.EnQueue(utils.StringToFloat64(r.Price))
		d.Index.EnQueue(utils.StringToFloat64(r.Price)) // 指数价格暂时用当前价代替
	}
}

func (rs *SpotPriceResponce) UpdateData(ds []*data.SymbolData) {
	dsMap := make(map[string]*data.SymbolData)
	for _, d := range ds {
		dsMap[d.Symbol] = d
	}
	for _, r := range *rs {
		d := dsMap[r.Symbol]
		if d == nil {
			continue
		}
		d.Price.EnQueue(utils.StringToFloat64(r.Price))
	}
}

func (r PositionTiersResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range r {
		b.PositionTiers[item.Symbol] = []broker.PositionTier{}
		// // 只保存前10档位
		// length := min(len(item.Brackets), 10)
		for _, bracket := range item.Brackets {
			posTier := broker.PositionTier{
				Tier:             bracket.Bracket,
				MaxSize:          math.Inf(1),
				MinSize:          math.Inf(-1),
				MaxNotional:      bracket.NotionalCap,
				MinNotional:      bracket.NotionalFloor,
				Cum:              bracket.Cum,
				MaintMarginRatio: bracket.MaintMarginRatio,
				InitialLeverage:  float64(bracket.InitialLeverage),
			}
			b.PositionTiers[item.Symbol] = append(b.PositionTiers[item.Symbol], posTier)
		}
	}
}

func (r FuturesCommissionRateResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	b.FuturesCommission = broker.Commission{
		Maker: utils.StringToFloat64(r.Maker),
		Taker: utils.StringToFloat64(r.Taker),
	}
}

func (r SpotCommissionRateResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	b.SpotCommission = broker.Commission{
		Maker: utils.StringToFloat64(r.Standard.Maker),
		Taker: utils.StringToFloat64(r.Standard.Taker),
	}
}

func (r DepthResponse) UpdateData(d *data.SymbolData) error {
	d.OrderBooks.Lock()
	defer d.OrderBooks.Unlock()
	if r.LastUpdateId <= d.OrderBooks.UpdateId {
		return errors.New("深度数据回执已经过期")
	}
	d.OrderBooks.Bids = []*data.OrderBook{}
	for _, bid := range r.Bids {
		price := utils.StringToFloat64(bid[0])
		amount := utils.StringToFloat64(bid[1])
		d.OrderBooks.Bids = append(d.OrderBooks.Bids, &data.OrderBook{
			Price: price,
			Size:  amount,
		})
	}
	d.OrderBooks.Asks = []*data.OrderBook{}
	for _, ask := range r.Asks {
		price := utils.StringToFloat64(ask[0])
		amount := utils.StringToFloat64(ask[1])
		d.OrderBooks.Asks = append(d.OrderBooks.Asks, &data.OrderBook{
			Price: price,
			Size:  amount,
		})
	}
	d.OrderBooks.UpdateId = r.LastUpdateId
	d.OrderBooks.Snap = true
	return nil
}

func (r FuturesOrderResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	item := r
	// 更新订单状态
	o := b.FuturesPending.Pending[item.ClOrdId]
	if o == nil {
		return
	}
	o.Status = order.OrdStatus(item.Status)
	o.ExecutedSize, _ = strconv.ParseFloat(item.ExecutedSize, 64)
	o.AvgPrice, _ = strconv.ParseFloat(item.AvgPrice, 64)
	o.Price, _ = strconv.ParseFloat(item.Price, 64)
	o.Size, _ = strconv.ParseFloat(item.Size, 64)
}

func (r MarginOrderResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	item := r
	// 更新订单状态
	o := b.MarginPending.Pending[item.ClOrdId]
	if o == nil {
		return
	}
	o.Status = order.OrdStatus(item.Status)
	o.ExecutedSize, _ = strconv.ParseFloat(item.ExecutedSize, 64)
	o.AvgPrice, _ = strconv.ParseFloat(item.AvgPrice, 64)
	o.Price, _ = strconv.ParseFloat(item.Price, 64)
	o.Size, _ = strconv.ParseFloat(item.Size, 64)
}
