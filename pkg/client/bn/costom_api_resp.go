package bn

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/order"
	"strconv"
)

// 传统期货账户资产响应
type FuturesBalanceResponse []struct {
	Asset string `json:"asset"`
	Total string `json:"balance"`
	Free  string `json:"availableBalance"`
}

// 传统期货账户响应
type FuturesAccountResponse struct {
	Balance   string `json:"totalWalletBalance"`
	Equity    string `json:"totalMarginBalance"`
	Available string `json:"availableBalance"`
	Assets    []struct {
		Asset      string `json:"asset"`
		Balance    string `json:"marginBalance"`
		Available  string `json:"availableBalance"`
		UpdateTime int64  `json:"updateTime"`
	} `json:"assets"`
	Positions []struct {
		Symbol string `json:"symbol"`
		// InitialMargin  string `json:"initialMargin"`
		UnrealizedProfit string `json:"unrealizedProfit"`
		Notional         string `json:"notional"`
		PositionSide     string `json:"positionSide"`
		PositionAmount   string `json:"positionAmt"`
		UpdateTime       int64  `json:"updateTime"`
	} `json:"positions"`
}

// 传统现货账户响应
type SpotAccountResponse struct {
	Balances []struct {
		Asset  string `json:"asset"`
		Free   string `json:"free"`
		Locked string `json:"locked"`
	}
}

func (r FuturesBalanceResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range r {
		if _, exists := b.AssetWallets[item.Asset]; !exists {
			b.AssetWallets[item.Asset] = &broker.AssetWallet{}
		}
		b.AssetWallets[item.Asset].Asset = item.Asset
		b.AssetWallets[item.Asset].UM, _ = strconv.ParseFloat(item.Free, 64)
	}
}

func (r *FuturesAccountResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	b.Account.CashBalance, _ = strconv.ParseFloat(r.Balance, 64)
	b.Account.Equity, _ = strconv.ParseFloat(r.Balance, 64)
	b.Account.Available, _ = strconv.ParseFloat(r.Available, 64)
	for _, item := range r.Assets {
		if _, exists := b.AssetWallets[item.Asset]; !exists {
			b.AssetWallets[item.Asset] = &broker.AssetWallet{}
		}
		b.AssetWallets[item.Asset].Asset = item.Asset
		b.AssetWallets[item.Asset].UM, _ = strconv.ParseFloat(item.Available, 64)
		b.AssetWallets[item.Asset].UpdateTime = item.UpdateTime
	}
	for _, item := range r.Positions {
		if _, exists := b.Positions[item.Symbol]; !exists {
			b.Positions[item.Symbol] = &broker.UMPosition{}
		}
		posObj := b.Positions[item.Symbol]
		posObj.Symbol = item.Symbol
		position, _ := strconv.ParseFloat(item.PositionAmount, 64)
		switch order.PosSide(item.PositionSide) {
		case order.LONG:
			posObj.SizeLong = position
		case order.SHORT:
			posObj.SizeShort = position
		case order.BOTH:
			posObj.SizeLong = max(position, 0)
			posObj.SizeShort = min(position, 0)
		}
		// 开仓价格调整
		notional, _ := strconv.ParseFloat(item.Notional, 64)
		pnl, _ := strconv.ParseFloat(item.UnrealizedProfit, 64)
		if posObj.SizeLong == 0 {
			posObj.OpenPriceLong = 0
		} else {
			posObj.OpenPriceLong = (notional - pnl) / posObj.SizeLong
		}
		if posObj.SizeShort == 0 {
			posObj.OpenPriceShort = 0
		} else {
			posObj.OpenPriceShort = (notional - pnl) / posObj.SizeShort
		}
		posObj.UpdateTime = item.UpdateTime
	}
}

func (r *SpotAccountResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range r.Balances {
		if _, exists := b.AssetWallets[item.Asset]; !exists {
			b.AssetWallets[item.Asset] = &broker.AssetWallet{}
		}
		b.AssetWallets[item.Asset].Asset = item.Asset
		b.AssetWallets[item.Asset].Free, _ = strconv.ParseFloat(item.Free, 64)
		b.AssetWallets[item.Asset].Locked, _ = strconv.ParseFloat(item.Locked, 64)
	}
}
