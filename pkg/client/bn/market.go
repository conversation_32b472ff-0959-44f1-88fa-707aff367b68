package bn

import (
	"GoTrader/pkg/data"
	"GoTrader/pkg/handler"
	"GoTrader/pkg/utils"
	ws "GoTrader/pkg/wsclient"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"sync"

	"github.com/gorilla/websocket"
)

// 币安数据源客户端
type BnMarketClient struct {
	url      string                                                                        // WebsocketURL
	ws       ws.WSManager                                                                  // WebSocket 行情推送连接实例
	channels []string                                                                      // 订阅的数据频道
	data     *data.MarketData                                                              // 数据对象
	handlers map[handler.WSStreamType][]func([]byte, *data.MarketData, *slog.Logger) error // 数据处理函数, map[事件类型][处理函数]
	mu       *sync.RWMutex                                                                 // 互斥锁
	token    *utils.TokenBucket                                                            // 令牌桶
	logger   *slog.Logger                                                                  // 日志对象
}

func NewMarketClient(url string, channels []string, ws ws.WSManager, mdata *data.MarketData, logger *slog.Logger) *BnMarketClient {
	logger = logger.With("client", "bnMarket").With("url", url)
	c := &BnMarketClient{
		url:      url,
		ws:       ws,
		channels: channels,
		data:     mdata,
		handlers: map[handler.WSStreamType][]func([]byte, *data.MarketData, *slog.Logger) error{},
		mu:       &sync.RWMutex{},
		token:    nil,
		logger:   logger,
	}
	// 设置重连函数
	ws.SetReInitFunc(c.reSubscribeChannels)
	return c
}

// 启动数据源
func (c *BnMarketClient) Run(ctx context.Context) (err error) {
	err = c.connect(ctx)
	if err != nil {
		return err
	}
	c.listen(ctx)
	return nil
}

func (c *BnMarketClient) AddStreamHandler(event handler.WSStreamType, handler func([]byte, *data.MarketData, *slog.Logger) error) *BnMarketClient {
	c.handlers[event] = append(c.handlers[event], handler)
	return c
}

// 连接数据源
func (c *BnMarketClient) connect(ctx context.Context) error {

	c.token = utils.NewTokenBucket(ctx, 10, 10)
	err := c.ws.Connect(ctx, c.url, c.logger)
	if err != nil {
		c.logger.Error("error connect ws", "error", err)
		return err
	} else {
		c.logger.Info("connect ws success")
	}

	// 订阅数据, TODO 暂时忽略token不足的问题

	params := []string{}
	params = append(params, c.channels...)

	req := data.SubscribeRequest{
		Method: "SUBSCRIBE",
		Params: params,
		ID:     1,
	}

	var subscribeMessage []byte
	subscribeMessage, _ = json.Marshal(req)
	c.logger.Info("Write message", "Message", string(subscribeMessage))
	err = c.ws.WriteMessage(websocket.TextMessage, subscribeMessage)
	if err != nil {
		c.logger.Error("Write message error", "Message", string(subscribeMessage))
		return err
	} else {
		c.logger.Info("Write message success", "Message", string(subscribeMessage))
	}
	return nil
}

// 监听行情
func (c *BnMarketClient) listen(ctx context.Context) {
	go func() {
		for {
			eventMsg := &MarketStreamEvent{}
			// 等待接收数据
			_, msg, err := c.ws.ReadMessage()
			if err != nil {
				if ctx.Err() != nil {
					c.logger.Info("Market ws context canceled", "error", err)
					return
				}
				if errors.Is(err, ws.ErrReconnected) {
					// 重连成功
					c.logger.Warn("Market ws reconnect success")
					continue
				}
				c.logger.Error("Error reading Data WebSocket message, skip", "error", err)
				continue
			}
			// 解析消息
			err = json.Unmarshal(msg, &eventMsg)
			if err != nil {
				c.logger.Warn("Error unmarshalling Data WebSocket message", "error", err)
				continue
			}
			event := eventMsg.GetEvent()
			if event == "" {
				c.logger.Info("Empty event skip", "event", event)
				continue
			}

			// 执行事件处理函数
			handlers, ok := c.handlers[event]
			if !ok {
				c.logger.Warn("Unsigned event", "event", event)
				continue
			}
			for _, handler := range handlers {
				handler(msg, c.data, c.logger)
			}
		}
	}()
}

// 重新订阅已有数据
func (c *BnMarketClient) reSubscribeChannels(_ context.Context, ver int64) error {
	// 将datamap中的data生成列表
	params := []string{}
	params = append(params, c.channels...)

	req := data.SubscribeRequest{
		Method: "SUBSCRIBE",
		Params: params,
		ID:     1,
	}

	var subscribeMessage []byte
	subscribeMessage, _ = json.Marshal(req)
	// 验证版本号
	if !c.ws.IsVersionValid(ver) {
		c.logger.Info("ws version changed, stop write ws channel message")
		return fmt.Errorf("ws version changed")
	}
	c.logger.Info("Write message", "Message", string(subscribeMessage))
	err := c.ws.WriteMessage(websocket.TextMessage, subscribeMessage)
	if err != nil {
		c.logger.Error("Write message error", "Message", string(subscribeMessage))
		return err
	} else {
		c.logger.Info("Write message success", "Message", string(subscribeMessage))
	}
	return nil
}
