package bn

import (
	"GoTrader/pkg/api"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	"encoding/json"
	"errors"
	"log/slog"
	"math"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"
)

type BnPortfolioAPIClient struct {
	FuturesAPIURL string               // 期货RestfulAPIURL
	SpotAPIURL    string               // 现货RestfulAPIURL
	BrokerAPIURL  string               // 账户API请求 url
	APIs          api.BnBrokerAPIgroup // api集合
	httpClient    *http.Client         // http连接实例
	blocked       *utils.BoolFlag      // 已阻断标记
	logger        *slog.Logger         // 日志对象
}

func NewPortfolioAPIClient(futuresAPIURL string, spotAPIURL string, brokerAPIURL string, apis api.BnBrokerAPIgroup, httpClient *http.Client, logger *slog.Logger) *BnPortfolioAPIClient {
	return &BnPortfolioAPIClient{
		FuturesAPIURL: futuresAPIURL,
		SpotAPIURL:    spotAPIURL,
		BrokerAPIURL:  brokerAPIURL,
		APIs:          apis,
		httpClient:    httpClient,
		blocked:       utils.NewBoolFlag(false),
		logger:        logger,
	}
}

// 发送期货api请求
func (c *BnPortfolioAPIClient) sendFutures(data map[string]any, rURL api.APIMethod, b *broker.Broker, signature bool) (response []byte, err error) {
	if c.blocked.IsTrue() {
		return nil, errors.New("BnPortfolioAPIClient is blocked")
	}
	resp, usedWeight, statusCode, err := httpSend(data, c.FuturesAPIURL, rURL, b, c.httpClient, c.logger)
	if usedWeight >= 1500 || statusCode == 429 {
		go func() {
			c.blocked.SetTrue()
			sec := utils.GetSecondsToNextMinute()
			c.logger.Warn("Wait until the next minute to reset the weight.", "seconds", sec, "usedWeight", usedWeight, "statusCode", statusCode)
			time.Sleep(time.Duration(sec) * time.Second)
			c.blocked.SetFalse()
		}()
	}
	return resp, err
}

// 发送现货api请求
func (c *BnPortfolioAPIClient) sendSpot(data map[string]any, rURL api.APIMethod, b *broker.Broker, signature bool) (response []byte, err error) {
	if c.blocked.IsTrue() {
		return nil, errors.New("BnPortfolioAPIClient is blocked")
	}

	if signature {
		// 添加时间戳并签名
		data = signatureHMACRequest(data, b.Secret)
	}
	resp, usedWeight, statusCode, err := httpSend(data, c.SpotAPIURL, rURL, b, c.httpClient, c.logger)
	if usedWeight >= 1500 || statusCode == 429 {
		go func() {
			c.blocked.SetTrue()
			sec := utils.GetSecondsToNextMinute()
			c.logger.Warn("Wait until the next minute to reset the weight.", "seconds", sec, "usedWeight", usedWeight, "statusCode", statusCode)
			time.Sleep(time.Duration(sec) * time.Second)
			c.blocked.SetFalse()
		}()
	}
	return resp, err
}

// 发送请求并返回响应
func (c *BnPortfolioAPIClient) sendBroker(data map[string]any, rURL api.APIMethod, b *broker.Broker) (response []byte, err error) {
	if c.blocked.IsTrue() {
		return nil, errors.New("BnPortfolioAPIClient is blocked")
	}
	data["apiKey"] = b.ApiKey
	// 添加时间戳并签名
	data = signatureHMACRequest(data, b.Secret)
	resp, usedWeight, statusCode, err := httpSend(data, c.BrokerAPIURL, rURL, b, c.httpClient, c.logger)
	if usedWeight >= 1500 || statusCode == 429 {
		go func() {
			c.blocked.SetTrue()
			sec := utils.GetSecondsToNextMinute()
			c.logger.Warn("Wait until the next minute to reset the weight.", "seconds", sec, "usedWeight", usedWeight, "statusCode", statusCode)
			time.Sleep(time.Duration(sec) * time.Second)
			c.blocked.SetFalse()
		}()
	}
	return resp, err
}

// 获取账户期货listenKey
func (c *BnPortfolioAPIClient) GetListenKey(b *broker.Broker) (string, error) {
	response, err := c.sendBroker(map[string]any{}, c.APIs.GetListenKey, b)
	if err != nil {
		return "", errors.Join(err, errors.New("get listenKey failed"))
	}
	// 解析响应
	var listenKeyResponse ListenKeyResponse
	err = json.Unmarshal(response, &listenKeyResponse)
	if err != nil {
		return "", errors.New("error unmarshalling listenKeyResponse JSON")
	}
	return listenKeyResponse.ListenKey, nil
}

// 提交期货订单
func (c *BnPortfolioAPIClient) SubmitFuturesOrder(b *broker.Broker, o *order.FuturesOrder, info *data.ExchangeInfo) (client.OrderViewer, error) {
	logger := c.logger.With("order", o)
	o = fixFuturesOrderClOrdIdPrefix(o)
	// 控制OrdId长度
	if len(o.ClOrdId) > 36 {
		o.ClOrdId = o.ClOrdId[:36]
	}
	// 计算订单价值
	orderValue := math.Abs(o.Size * o.Price)
	if o.IsOpen() && orderValue < info.MinNotional {
		// 订单价值小于最小价值
		logger.Warn("Order value is less than min notional", "orderValue", orderValue, "minNotional", info.MinNotional)
		return o, errors.New("order value is less than min notional")
	}
	if o.IsOpen() && orderValue/float64(b.Leverages[o.Symbol]) > b.GetAvailableMargin() {
		// 订单价值大于可用余额
		logger.Warn("Order value is more than available balance", "orderValue", orderValue, "available", b.GetAvailableMargin())
		return o, errors.New("order value is more than available balance")
	}
	// 将order加入borkerpending中
	b.AddFuturesOrder(o)

	orderRequest := &OrderRequest{
		ApiKey:      b.ApiKey,
		Symbol:      o.Symbol,
		OrdId:       o.ClOrdId,
		OrdType:     string(o.OrdType),
		PosSide:     string(o.PosSide),
		Price:       strconv.FormatFloat(o.Price, 'f', info.PricePrecision, 64),
		PriceMatch:  string(o.PriceMatch),
		Side:        string(o.Side),
		Size:        strconv.FormatFloat(math.Abs(o.Size), 'f', info.QuantityPrecision, 64),
		TimeInForce: string(o.TimeInForce),
	}

	if o.OrdType == order.MARKET {
		orderRequest.Price = ""
		orderRequest.TimeInForce = ""
	}

	// 处理PriceMatch和Price的冲突问题
	if o.PriceMatch != "" {
		orderRequest.Price = ""
	}

	params, err := utils.StructToMap(orderRequest)
	if err != nil {
		logger.Error("Error converting struct to map", "error", err)
		o.ToReject()
		b.RemoveFuturesOrder(o)
		return o, err
	}

	// 签名并返回map
	response, err := c.sendBroker(params, c.APIs.FutureOrder, b)
	if err != nil {
		// 检查响应
		// 将order从borkerpending中移除
		o.ToReject()
		b.RemoveFuturesOrder(o)
		logger.Warn("Error sending Futures order", "error", err, "params", params, "info", info)
		return o, err
	}

	orderResponse := &OrderResponse{}
	err = json.Unmarshal(response, orderResponse)
	if err != nil {
		// 解析错误, 终止程序
		o.ToReject()
		b.RemoveFuturesOrder(o)
		logger.Error("Error unmarshalling JSON Futures order", "error", err, "response", response)
		os.Exit(1)
	}
	o.ToNew()
	return o, nil
}

// 提交杠杆订单
func (c *BnPortfolioAPIClient) SubmitMarginOrder(b *broker.Broker, o *order.MarginOrder, info *data.ExchangeInfo) (client.OrderViewer, error) {
	logger := c.logger.With("order", o)
	o = fixMarginOrderClOrdIdPrefix(o)
	// 控制OrdId长度
	if len(o.ClOrdId) > 36 {
		o.ClOrdId = o.ClOrdId[:36]
	}
	orderValue := math.Abs(o.Size * o.Price)
	if orderValue < info.MinNotional {
		logger.Warn("Order value is less than min notional", "orderValue", orderValue, "minNotional", info.MinNotional)
		return o, errors.New("order value is less than min notional")
	}
	if o.IsOpen() && orderValue > b.GetAvailableUSDT() {
		logger.Warn("Order value is more than available balance", "orderValue", orderValue, "available", b.GetAvailableUSDT())
		return o, errors.New("order value is more than available balance")
	}
	// 将order加入borkerpending中
	b.AddMarginOrder(o)
	orderRequest := &OrderRequest{
		Symbol:      o.Symbol,
		OrdId:       o.ClOrdId,
		OrdType:     string(o.OrdType),
		Price:       strconv.FormatFloat(o.Price, 'f', info.PricePrecision, 64),
		Side:        string(o.Side),
		Size:        strconv.FormatFloat(o.Size, 'f', info.QuantityPrecision, 64),
		TimeInForce: string(o.TimeInForce),
	}

	if o.OrdType == order.MARKET {
		orderRequest.Price = ""
		orderRequest.TimeInForce = ""
	}

	params, err := utils.StructToMap(orderRequest)
	if err != nil {
		logger.Error("Error converting struct to map", "error", err)
		o.ToReject()
		b.RemoveMarginOrder(o)
		return o, err
	}

	response, err := c.sendBroker(params, c.APIs.MarginOrder, b)
	if err != nil {
		// 检查响应
		// 将order从borkerpending中移除
		o.ToReject()
		b.RemoveMarginOrder(o)
		logger.Warn("Error sending Margin order", "error", err, "params", params, "info", info)
		return o, err
	}

	orderResponse := &OrderResponse{}
	err = json.Unmarshal(response, orderResponse)
	if err != nil {
		// 解析错误, 终止程序
		o.ToReject()
		b.RemoveMarginOrder(o)
		logger.Error("Error unmarshalling JSON Margin order", "error", err, "response", response)
		os.Exit(1)
	}
	o.ToNew()
	return o, nil
}

// 修改期货订单
func (c *BnPortfolioAPIClient) ModifyFuturesOrder(b *broker.Broker, o order.FuturesOrder, info *data.ExchangeInfo) error {
	logger := c.logger.With("order", o)
	// 检查order是否在挂单中
	if !b.HasFuturesOrder(o.ClOrdId) {
		logger.Warn("Order not found in pending orders", "order", o)
		return nil
	}
	modifyOrdersRequest := ModifyOrderRequest{
		ClientOrderId: o.ClOrdId,
		Symbol:        o.Symbol,
		Side:          string(o.Side),
		Size:          strconv.FormatFloat(o.Size, 'f', info.QuantityPrecision, 64),
		Price:         strconv.FormatFloat(o.Price, 'f', info.PricePrecision, 64),
	}

	params, err := utils.StructToMap(modifyOrdersRequest)
	if err != nil {
		return err
	}

	_, err = c.sendBroker(params, c.APIs.ModifyFuturesOrder, b)
	if err != nil {
		logger.Warn("Error sending Modify Futures orders", "error", err, "params", params, "info", info)
		return err
	}

	return nil
}

// 请求账户资产
func (c *BnPortfolioAPIClient) updateBrokerBalance(b *broker.Broker) error {
	response, err := c.sendBroker(map[string]any{}, c.APIs.GetBalance, b)
	if err != nil {
		c.logger.Error("Error sending QueryBalance request", "error", err)
		return err
	}

	// 解析响应
	var balanceResponse BalanceResponse
	err = json.Unmarshal(response, &balanceResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	balanceResponse.UpdateBroker(b)

	return nil
}

// 请求账户信息
func (c *BnPortfolioAPIClient) updateBrokerAccount(b *broker.Broker) error {
	response, err := c.sendBroker(map[string]any{}, c.APIs.GetAccount, b)
	if err != nil {
		return err
	}
	// 解析响应
	var brokerInfoResponse AccountResponse
	err = json.Unmarshal(response, &brokerInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	brokerInfoResponse.UpdateBroker(b)

	return nil
}

// 请求UM仓位信息
func (c *BnPortfolioAPIClient) updateBrokerUMAccount(b *broker.Broker) error {
	response, err := c.sendBroker(map[string]any{}, c.APIs.GetUMAccount, b)
	if err != nil {
		return err
	}

	// 解析响应
	var brokerUMInfoResponse UMAccountResponse
	err = json.Unmarshal(response, &brokerUMInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	brokerUMInfoResponse.UpdateBroker(b)

	return nil
}

// 请求期货账户信息并直接返回
func (c *BnPortfolioAPIClient) QueryAccount(b *broker.Broker) (string, error) {
	response, err := c.sendBroker(map[string]any{}, c.APIs.GetUMAccount, b)
	if err != nil {
		return "", err
	}

	return string(response), nil
}

// 查询UM账户未完成订单
func (c *BnPortfolioAPIClient) QueryUMPending(b *broker.Broker, s string) error {
	s = strings.ToUpper(s)
	response, err := c.sendBroker(map[string]any{"symbol": s}, c.APIs.GetUMPending, b)
	if err != nil {
		return err
	}

	// 解析响应
	var brokerUMPendingResponse BrokerUMPendingResponse
	err = json.Unmarshal(response, &brokerUMPendingResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	brokerUMPendingResponse.UpdateBroker(b)

	return nil
}

// 查询杠杆账户未完成订单
func (c *BnPortfolioAPIClient) QueryMarginPending(b *broker.Broker, s string) error {
	s = strings.ToUpper(s)
	response, err := c.sendBroker(map[string]any{"symbol": s}, c.APIs.GetMarginPending, b)
	if err != nil {
		return err
	}

	// 解析响应
	var brokerMarginPendingResponse BrokerMarginPendingResponse
	err = json.Unmarshal(response, &brokerMarginPendingResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}

	brokerMarginPendingResponse.UpdateBroker(b)

	return nil
}

// 查看合约账户杠杆
func (c *BnPortfolioAPIClient) updateBrokerUMLeverage(b *broker.Broker) error {
	response, err := c.sendBroker(map[string]any{}, c.APIs.GetUMConfig, b)
	if err != nil {
		return err
	}

	// 解析响应
	var brokerUMConfigResponse BrokerUMConfigResponse
	err = json.Unmarshal(response, &brokerUMConfigResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	brokerUMConfigResponse.UpdateBroker(b)

	return nil
}

// 更新broker信息
func (c *BnPortfolioAPIClient) UpdateBroker(b *broker.Broker) (err error) {
	// 需要提前获取杠杆信息
	if err = c.updateBrokerUMLeverage(b); err != nil {
		return err
	}
	if err = c.updateBrokerBalance(b); err != nil {
		return err
	}
	if err = c.updateBrokerAccount(b); err != nil {
		return err
	}
	if err = c.updateBrokerUMAccount(b); err != nil {
		return err
	}
	if err = c.updateFuturesDataComm(b); err != nil {
		return err
	}
	if err = c.updateSpotDataComm(b); err != nil {
		return err
	}
	return nil
}

// 更新broker期货挂单
func (c *BnPortfolioAPIClient) UpdateBrokerUMPending(b *broker.Broker, ds []*data.SymbolData) (err error) {
	for _, d := range ds {
		if d.DataType == data.Futures {
			if err = c.QueryUMPending(b, d.Symbol); err != nil {
				return err
			}
		}
	}
	return nil
}

// 更新broker杠杆挂单
func (c *BnPortfolioAPIClient) UpdateBrokerMarginPending(b *broker.Broker, ds []*data.SymbolData) (err error) {
	for _, d := range ds {
		if d.DataType == data.Spot {
			if err = c.QueryMarginPending(b, d.Symbol); err != nil {
				return err
			}
		}
	}
	return nil
}

// 更新期货数据
func (c *BnPortfolioAPIClient) UpdateFuturesDatas(ds []*data.SymbolData) (err error) {
	if err = c.updateFuturesDataInfo(ds); err != nil {
		return err
	}
	if err = c.updateFuturesFunding(ds); err != nil {
		return err
	}
	if err = c.updateFuturesFundingInfo(ds); err != nil {
		return err
	}
	if err = c.updateFuturesDataPrice(ds); err != nil {
		return err
	}
	c.updateDepthMaxSize(ds)
	return nil
}

// 更新现货数据
func (c *BnPortfolioAPIClient) UpdateSpotDatas(ds []*data.SymbolData) (err error) {
	if err = c.updateSpotDataInfo(ds); err != nil {
		return err
	}
	if err = c.updateSpotDataPrice(ds); err != nil {
		return err
	}
	return nil
}

// 查询期货交易规范
func (c *BnPortfolioAPIClient) updateFuturesDataInfo(ds []*data.SymbolData) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesExchangeInfo, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var exchangeInfoResponse FuturesExchangeInfoResponse
	err = json.Unmarshal(response, &exchangeInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}

	exchangeInfoResponse.UpdateData(ds)

	return nil
}

// 查询期货资金费率
func (c *BnPortfolioAPIClient) updateFuturesFunding(ds []*data.SymbolData) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesFunding, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var fundingResponse FuturesFundingResponse
	err = json.Unmarshal(response, &fundingResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	fundingResponse.UpdateData(ds)
	return nil
}

// // 查询并更新期货历史资金费率
// func (c *BnPortfolioAPIClient) updateFuturesFundingHist(d *data.SymbolData) error {
// 	response, err := c.sendFutures(map[string]any{"symbol": d.Symbol, "limit": 1}, c.APIs.GetFuturesFundingHist, nil, false)
// 	if err != nil {
// 		return err
// 	}
// 	// 解析响应
// 	var fundingHistResponse FuturesFundingHistResponse
// 	err = json.Unmarshal(response, &fundingHistResponse)
// 	if err != nil {
// 		c.logger.Error("Error unmarshalling JSON", "error", err)
// 		return err
// 	}
// 	fundingHistResponse.UpdateData(d)
// 	return nil
// }

// 查询期货资金费率信息
func (c *BnPortfolioAPIClient) updateFuturesFundingInfo(ds []*data.SymbolData) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesFundingInfo, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var fundingInfoResponse FuturesFundingInfoResponse
	err = json.Unmarshal(response, &fundingInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	fundingInfoResponse.UpdateData(ds)
	return nil
}

// 查询现货交易规范
func (c *BnPortfolioAPIClient) updateSpotDataInfo(ds []*data.SymbolData) error {
	response, err := c.sendSpot(map[string]any{"showPermissionSets": false}, c.APIs.GetSpotExchangeInfo, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var exchangeInfoResponse SpotExchangeInfoResponse
	err = json.Unmarshal(response, &exchangeInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}

	infos := []*data.ExchangeInfo{}
	for _, d := range ds {
		infos = append(infos, d.Info)
	}
	exchangeInfoResponse.UpdateData(infos)

	return nil
}

// 查询期货当前价格
func (c *BnPortfolioAPIClient) updateFuturesDataPrice(ds []*data.SymbolData) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesPrice, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var priceResponse FuturesPriceResponce
	err = json.Unmarshal(response, &priceResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	priceResponse.UpdateData(ds)
	return nil
}

// 查询现货当前价格
func (c *BnPortfolioAPIClient) updateSpotDataPrice(ds []*data.SymbolData) error {
	response, err := c.sendSpot(map[string]any{}, c.APIs.GetSpotPrice, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var priceResponse SpotPriceResponce
	err = json.Unmarshal(response, &priceResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	priceResponse.UpdateData(ds)
	return nil
}

func (c *BnPortfolioAPIClient) updateDepthMaxSize(ds []*data.SymbolData) {
	for _, d := range ds {
		d.OrderBooks.MaxSize = 1000
	}
}

// 取消所有期货订单
func (c *BnPortfolioAPIClient) CancelAllFutureOrders(b *broker.Broker, d *data.SymbolData) error {
	if b.GetFuturesOrders(d.Symbol) == nil {
		return nil
	}
	_, err := c.sendBroker(map[string]any{"symbol": d.Symbol}, c.APIs.CancelAllFuturesOrder, b)
	if err != nil {
		return err
	}
	c.logger.Info("Futures Orders Cancelled", "symbol", d.Symbol)
	return nil
}

// 取消所有杠杆订单
func (c *BnPortfolioAPIClient) CancelAllMarginOrders(b *broker.Broker, d *data.SymbolData) error {
	if b.GetMarginOrders(d.Symbol) == nil {
		return nil
	}
	_, err := c.sendBroker(map[string]any{"symbol": d.Symbol}, c.APIs.CancelAllMarginOrder, b)
	if err != nil {
		// 判断异常类型
		if httpErr, ok := err.(*HTTPStatusError); ok {
			if httpErr.StatusCode != 400 {
				// 400错误表示没有找到任何杠杆订单, 合法操作
				return err
			}
		}
	}
	c.logger.Info("Margin Orders Cancelled", "symbol", d.Symbol)
	return nil
}

// 取消期货订单
func (c *BnPortfolioAPIClient) CancelFuturesOrder(b *broker.Broker, o *order.FuturesOrder) error {
	_, err := c.sendBroker(map[string]any{"symbol": o.Symbol, "origClientOrderId": o.ClOrdId}, c.APIs.CancelFuturesOrder, b)
	if err != nil {
		return err
	}
	c.logger.Info("Futures Order Cancelled", "order", o)
	return nil
}

// 取消杠杆订单
func (c *BnPortfolioAPIClient) CancelMarginOrder(b *broker.Broker, o *order.MarginOrder) error {
	_, err := c.sendBroker(map[string]any{"symbol": o.Symbol, "origClientOrderId": o.ClOrdId}, c.APIs.CancelMarginOrder, b)
	if err != nil {
		return err
	}
	c.logger.Info("Margin Order Cancelled", "order", o)
	return nil
}

// 查询期货所有可交易品种
func (c *BnPortfolioAPIClient) QueryAllTradingFutures() []string {
	resp, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesExchangeInfo, nil, false)
	if err != nil {
		return nil
	}
	var exchangeInfoResponse FuturesExchangeInfoResponse
	err = json.Unmarshal(resp, &exchangeInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return nil
	}
	symbols := []string{}
	for _, s := range exchangeInfoResponse.Symbols {
		if s.Status == "TRADING" {
			symbols = append(symbols, s.Symbol)
		}
	}
	return symbols
}

// 查询期货所有资金费率
func (c *BnPortfolioAPIClient) QueryAllFunding() map[string]float64 {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesFunding, nil, false)
	if err != nil {
		return nil
	}
	// 解析响应
	var fundingResponse FuturesFundingResponse
	err = json.Unmarshal(response, &fundingResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return nil
	}
	r := make(map[string]float64)
	for _, f := range fundingResponse {
		r[f.Symbol] = utils.StringToFloat64(f.Funding)
	}
	return r
}

// 查询所有可交易杠杆品种
func (c *BnPortfolioAPIClient) QueryAllTradingMargin(b *broker.Broker) []string {
	resp, err := c.sendSpot(map[string]any{}, c.APIs.GetMarginPairs, b, false)
	if err != nil {
		return nil
	}
	var marginPairsResponse MarginPairsResponse
	err = json.Unmarshal(resp, &marginPairsResponse)
	if err != nil {
		c.logger.Info("Error unmarshalling JSON", "error", err)
		return nil
	}
	symbols := []string{}
	for _, s := range marginPairsResponse {
		if s.Quote == "USDT" && s.AllowTrade {
			symbols = append(symbols, s.Symbol)
		}
	}
	return symbols
}

// 期货平仓
func (c *BnPortfolioAPIClient) CloseFutures(b *broker.Broker, d *data.SymbolData) error {
	// 期货多仓平仓
	if math.Abs(b.GetFuturesSizeLong(d.Symbol)) > 0 {
		futuresOrderLong := order.CreateFuturesOrder().SetSymbol(d.Symbol).
			SetSize(math.Abs(b.GetFuturesSizeLong(d.Symbol))).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.MARKET).SetTimeInForce(order.GTC)
		_, err := c.SubmitFuturesOrder(
			b, futuresOrderLong, d.Info,
		)
		if err != nil {
			c.logger.Error(err.Error())
			return err
		}
	}
	if math.Abs(b.GetFuturesSizeShort(d.Symbol)) > 0 {
		// 期货空仓平仓
		futuresOrderShort := order.CreateFuturesOrder().SetSymbol(d.Symbol).
			SetSize(math.Abs(b.GetFuturesSizeShort(d.Symbol))).SetSide(order.BUY).SetPosSide(order.SHORT).SetOrdType(order.MARKET)
		_, err := c.SubmitFuturesOrder(
			b, futuresOrderShort, d.Info,
		)
		if err != nil {
			c.logger.Error(err.Error())
			return err
		}
	}
	return nil
}

// 现货平仓
func (c *BnPortfolioAPIClient) CloseMargin(b *broker.Broker, d *data.SymbolData) (err error) {
	size := b.GetAssetTotalFloor(d.Asset, d.Info)
	if size > 0 {
		marginOrder := order.CreateMarginOrder().SetSymbol(d.Symbol).SetPrice(d.Price.Last()).
			SetSize(size).SetSide(order.SELL).SetOrdType(order.MARKET)
		_, err := c.SubmitMarginOrder(
			b, marginOrder, d.Info,
		)
		if err != nil {
			c.logger.Error(err.Error())
		}
	}
	return nil
}

// 修改期货杠杆
func (c *BnPortfolioAPIClient) ModifyFuturesLeverage(b *broker.Broker, d *data.SymbolData, lever int64) (err error) {
	_, err = c.sendBroker(map[string]any{"symbol": d.Symbol, "leverage": lever}, c.APIs.ModifyFuturesLeverage, b)
	if err != nil {
		return err
	}
	c.logger.Info("Modify Futures Leverage Success", "symbol", d.Symbol, "lever", lever)
	return nil
}

// 查询单币种杠杆
func (c *BnPortfolioAPIClient) UpdateFuturesLeverageSingle(b *broker.Broker, d *data.SymbolData) error {
	return nil
}

func (c *BnPortfolioAPIClient) SubmitOrder(b *broker.Broker, o *order.Order, d *data.SymbolData) (client.OrderViewer, error) {
	switch d.DataType {
	case data.Futures:
		var posSide order.PosSide
		// 根据当前仓位和买卖方向判断posSide
		sizeLong, sizeShort := b.GetFuturesSize(d.Symbol)
		size := sizeLong + sizeShort
		switch {
		case size > 0:
			posSide = order.LONG
		case size < 0:
			posSide = order.SHORT
		case o.Side == order.SELL:
			posSide = order.SHORT
		case o.Side == order.BUY:
			posSide = order.LONG
		}
		fo := order.CreateFuturesOrderFromCommon(o, posSide)
		return c.SubmitFuturesOrder(b, fo, d.Info)
	case data.Spot:
		mo := order.CreateMarginOrderFromCommon(o)
		return c.SubmitMarginOrder(b, mo, d.Info)
	}
	return nil, nil
}

func (c *BnPortfolioAPIClient) CancelAllOrders(b *broker.Broker, d *data.SymbolData) error {
	switch d.DataType {
	case data.Futures:
		return c.CancelAllFutureOrders(b, d)
	case data.Spot:
		return c.CancelAllMarginOrders(b, d)
	}
	return nil
}

func (c *BnPortfolioAPIClient) Close(b *broker.Broker, d *data.SymbolData) error {
	switch d.DataType {
	case data.Futures:
		return c.CloseFutures(b, d)
	case data.Spot:
		return c.CloseMargin(b, d)
	}
	return nil
}

func (c *BnPortfolioAPIClient) UpdateDatas(ds []*data.SymbolData) error {
	fd := make([]*data.SymbolData, 0)
	sd := make([]*data.SymbolData, 0)

	for _, d := range ds {
		switch d.DataType {
		case data.Futures:
			fd = append(fd, d)
		case data.Spot:
			sd = append(sd, d)
		}
	}

	var err error
	err = c.UpdateFuturesDatas(fd)
	if err != nil {
		return err
	}
	err = c.UpdateSpotDatas(sd)
	if err != nil {
		return err
	}
	return nil
}

// 查询仓位档位
func (c *BnPortfolioAPIClient) UpdatePositionTiers(b *broker.Broker, d *data.SymbolData) error {
	response, err := c.sendBroker(map[string]any{"symbol": d.Symbol}, c.APIs.GetPositionTiers, b)
	if err != nil {
		return err
	}
	// 解析响应
	var positionTiersResponse PositionTiersResponse
	err = json.Unmarshal(response, &positionTiersResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	positionTiersResponse.UpdateBroker(b)
	return nil
}

// 查询张币系数
func (c *BnPortfolioAPIClient) UpdateCtVal(b *broker.Broker, d *data.SymbolData) error {
	b.CtVals[d.Symbol] = 1
	return nil
}

// 查询期货手续费
func (c *BnPortfolioAPIClient) updateFuturesDataComm(b *broker.Broker) error {
	response, err := c.sendBroker(map[string]any{"symbol": "BTCUSDT"}, c.APIs.GetFuturesCommissionRate, b)
	if err != nil {
		return err
	}
	// 解析响应
	var commissionResponse FuturesCommissionRateResponse
	err = json.Unmarshal(response, &commissionResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	commissionResponse.UpdateBroker(b)
	return nil
}

// 查询现货手续费
func (c *BnPortfolioAPIClient) updateSpotDataComm(b *broker.Broker) error {
	response, err := c.sendSpot(map[string]any{"symbol": "BTCUSDT"}, c.APIs.GetSpotCommissionRate, b, true)
	if err != nil {
		return err
	}
	// 解析响应
	var commissionResponse SpotCommissionRateResponse
	err = json.Unmarshal(response, &commissionResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	commissionResponse.UpdateBroker(b)
	return nil
}

// 更新深度(全量)
func (c *BnPortfolioAPIClient) UpdateDepth(d *data.SymbolData) error {
	switch d.DataType {
	case data.Futures:
		return c.updateFuturesDepth(d)
	case data.Spot:
		return c.updateSpotDepth(d)
	}
	return nil
}

func (c *BnPortfolioAPIClient) updateFuturesDepth(d *data.SymbolData) error {
	response, err := c.sendFutures(map[string]any{"symbol": d.Symbol, "limit": 1000}, c.APIs.GetFuturesDepth, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var depthResponse DepthResponse
	err = json.Unmarshal(response, &depthResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	depthResponse.UpdateData(d)
	return nil
}

func (c *BnPortfolioAPIClient) updateSpotDepth(d *data.SymbolData) error {
	response, err := c.sendSpot(map[string]any{"symbol": d.Symbol, "limit": 1000}, c.APIs.GetSpotDepth, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var depthResponse DepthResponse
	err = json.Unmarshal(response, &depthResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	depthResponse.UpdateData(d)
	return nil
}

func (c *BnPortfolioAPIClient) Query24hrQuote(d *data.SymbolData) (float64, error) {
	var response []byte
	var err error
	if d.DataType == data.Spot {
		response, err = c.sendSpot(map[string]any{"symbol": d.Symbol, "interval": "1h", "limit": 24}, c.APIs.GetSpotKlines, nil, false)
	}
	if d.DataType == data.Futures {
		response, err = c.sendFutures(map[string]any{"symbol": d.Symbol, "interval": "1h", "limit": 24}, c.APIs.GetFuturesKlines, nil, false)
	}
	if err != nil {
		return 0, err
	}
	// 解析响应
	var klines KLineResponse
	err = json.Unmarshal(response, &klines)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return 0, err
	}
	quote := 0.0
	errs := make([]error, 0)
	for _, kline := range klines {
		quote += utils.MustParseFloat64(kline[7].(string), &errs)
	}
	return quote, utils.CollectErrors(errs)
}

// 查询持仓量
func (c *BnPortfolioAPIClient) QueryOpenInterest(d *data.SymbolData) (float64, error) {
	response, err := c.sendFutures(map[string]any{"symbol": d.Symbol}, c.APIs.GetFuturesOpenInterest, nil, false)
	if err != nil {
		return 0, err
	}
	// 解析响应
	var openInterestResponse OpenInterestResponse
	err = json.Unmarshal(response, &openInterestResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return 0, err
	}
	count := utils.StringToFloat64(openInterestResponse.OI)
	return count, nil
}
