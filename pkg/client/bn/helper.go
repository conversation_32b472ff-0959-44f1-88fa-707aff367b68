package bn

import (
	"GoTrader/pkg/api"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/handler"
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	ws "GoTrader/pkg/wsclient"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"math/rand"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

const (
	WeightLimitHeader      = "X-Mbx-Used-Weight-"
	OrderWeightLimitHeader = "X-Mbx-Used-Count-"
)

func listenStreamWS(
	ctx context.Context,
	wsManager ws.WSManager,
	handlerMap map[handler.WSStreamType][]func([]byte, *data.SymbolData, *slog.Logger) error,
	dataMap map[string]*data.SymbolData,
	mu *sync.RWMutex,
	logger *slog.Logger,
) {
	for {
		eventMsg := &StreamEvent{}
		// 等待接收数据
		_, msg, err := wsManager.ReadMessage()
		if err != nil {
			if ctx.Err() != nil {
				logger.Info("Data ws context canceled", "error", err)
				return
			}
			if errors.Is(err, ws.ErrReconnected) {
				// 重连成功
				logger.Warn("Data ws reconnected", "error", err)
				continue
			}
			logger.Error("Error reading Data WebSocket message, skip", "error", err)
			continue
		}
		// 解析消息
		err = json.Unmarshal(msg, &eventMsg)
		if err != nil {
			logger.Warn("Error unmarshalling Data WebSocket message", "error", err)
			continue
		}
		event := eventMsg.GetEvent()
		if event == "" {
			logger.Info("Empty event skip", "event", event)
			continue
		}
		// 执行事件处理函数
		handlers, ok := handlerMap[event]
		if !ok {
			logger.Warn("Unsigned event", "event", event)
			continue
		}
		symbol := eventMsg.GetSymbol()
		mu.RLock()
		d, ok := dataMap[symbol]
		mu.RUnlock()
		if !ok {
			logger.Info("Unsigned symbol", "symbol", symbol)
			continue
		}
		for _, handler := range handlers {
			handler(msg, d, logger)
		}
	}
}

func writeWSChannelMessage(ctx context.Context, ver int64, ws ws.WSManager, ds []*data.SymbolData, tb *utils.TokenBucket, method string, suffix string, logger *slog.Logger) error {
	symbols := make([]string, 0, len(ds))
	for _, d := range ds {
		symbols = append(symbols, d.Symbol)
	}
	for len(ds) > 0 {
		if ctx.Err() != nil {
			logger.Warn("ctx canceled, stop write ws channel message")
			return ctx.Err()
		}

		tokenCtx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		defer cancel()
		// 一次占用2个令牌, 防止速度过快
		token := tb.Take(tokenCtx, len(ds), 2)
		subds := ds[:token]
		ds = ds[token:]
		if len(subds) == 0 {
			continue
		}
		params := []string{}
		for _, d := range subds {
			symbol := strings.ToLower(d.Symbol)
			params = append(params, symbol+suffix)
		}

		req := data.SubscribeRequest{
			Method: method,
			Params: params,
			ID:     1,
		}

		var subscribeMessage []byte
		subscribeMessage, err := json.Marshal(req)
		if err != nil {
			return err
		}
		// 验证版本号
		if !ws.IsVersionValid(ver) {
			logger.Info("ws version changed, stop write ws channel message")
			return fmt.Errorf("ws version changed")
		}
		logger.Info("write WSChannel Message", "message", string(subscribeMessage))
		err = ws.WriteMessage(websocket.TextMessage, subscribeMessage)
		if err != nil {
			logger.Error("write WSChannel Message message error", "message", string(subscribeMessage), "error", err)
			return err
		}
	}
	//再次验证版本号
	if !ws.IsVersionValid(ver) {
		logger.Info("ws version changed, stop write ws channel message")
		return fmt.Errorf("ws version changed")
	}
	logger.Info("All messages sent", "method", method, "suffix", suffix, "symbols", symbols)
	return nil
}

func httpDo(req *http.Request, httpClient *http.Client, logger *slog.Logger) (body []byte, usedWeight int64, statuCode int, err error) {
	logger = logger.With("method", req.Method, "requestURL", req.URL, "requestTime", time.Now().UnixMilli())
	pre := time.Now()
	resp, err := httpClient.Do(req)
	respTime := time.Since(pre)
	if err != nil {
		logger.Error("Error sending request", "error", err)
		return nil, 0, 0, err
	}
	defer resp.Body.Close()
	// 正确读取响应体
	body, err = io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("Error reading response body", "error", err)
		return nil, 0, 0, err
	}
	// 打印响应体
	// 获取已用权重
	for key, values := range resp.Header {
		if len(key) > len(WeightLimitHeader) && key[:len(WeightLimitHeader)] == WeightLimitHeader {
			usedWeight := values[0]
			logger = logger.With("Limit header key", key, "usedWeight", usedWeight)
		}
	}
	logger.Info("Response", "body", utils.TruncateString(string(body), 500), "respTime", respTime.String())
	// 检查状态码
	if resp.StatusCode != 200 {
		// 分类
		return body, usedWeight, resp.StatusCode, fmt.Errorf("%s", body)
	}
	return body, usedWeight, statuCode, nil
}

func httpSend(data map[string]any, host string, rURL api.APIMethod, b *broker.Broker, httpClient *http.Client, logger *slog.Logger) ([]byte, int64, int, error) {
	// 解析基础 URL
	fullURL := utils.URLJoin(host, rURL.Path)
	// 转换为 url.Values
	formData := utils.MapToURLValues(data)
	logger = logger.With("url", fullURL).With("data", formData)
	// 发送请求
	req, err := http.NewRequest(rURL.Method, fullURL+"?"+formData, nil)
	if err != nil {
		logger.Error("Error creating request", "error", err)
		return nil, 0, 0, err
	}
	if b != nil {
		// 设置请求头
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		req.Header.Set("X-MBX-APIKEY", b.ApiKey)
	}
	// 发送请求
	return httpDo(req, httpClient, logger)
}

// 期货添加clordid前缀
func fixFuturesOrderClOrdIdPrefix(o *order.FuturesOrder) *order.FuturesOrder {
	o.ClOrdId = generateValidClordId("x-tdk3UjFd-" + o.ClOrdId)
	return o
}

// 现货添加clordid前缀
func fixMarginOrderClOrdIdPrefix(o *order.MarginOrder) *order.MarginOrder {
	o.ClOrdId = generateValidClordId("x-P8DHAU8C-" + o.ClOrdId)
	return o
}

// 填充 ID 长度直到 32 位
func generateValidClordId(id string) string {
	// 2. 检查长度是否符合要求
	if len(id) < 32 {
		// 生成随机字母
		randomSuffix := generateRandomString(32 - len(id))
		id += randomSuffix
	}

	// 3. 确保长度在 32 位以内
	if len(id) > 32 {
		id = id[:32] // 截取多余部分
	}

	return id
}

// 生成指定长度的随机字母字符串
func generateRandomString(length int) string {
	const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	result := make([]byte, length)
	rng := rand.New(rand.NewSource(time.Now().UnixNano())) // 创建新的随机数生成器
	for i := range result {
		// 随机选择字母
		result[i] = letters[rng.Intn(len(letters))]
	}
	return string(result)
}

// Binance HMAC签名方法
func signatureHMACRequest(params map[string]any, secretKey string) map[string]any {
	// 请求参数签名
	if params == nil {
		params = make(map[string]any)
	}

	delete(params, "signature")

	// 添加时间戳（以毫秒为单位的 UNIX 时间戳）
	timestamp := time.Now().UnixMilli()
	params["timestamp"] = timestamp

	// 打印签名前的参数
	// log.Println("签名前的参数:", params)

	// 将参数转换为 URL 编码格式
	var queryParams url.Values = url.Values{}
	for key, value := range params {
		queryParams.Set(key, fmt.Sprintf("%v", value))
	}

	// 生成签名
	payload := queryParams.Encode()
	// log.Printf("payload: %s \n", payload)

	// 将 secretKey 作为 HMAC-SHA-256 的密钥
	// 使用 secretKey 创建一个 HMAC 对象
	hmacHasher := hmac.New(sha256.New, []byte(secretKey))

	// 使用 HMAC 对有效负载进行签名
	hmacHasher.Write([]byte(payload))

	// 获取 HMAC-SHA-256 的输出
	hmacOutput := hmacHasher.Sum(nil)

	// 将 HMAC 输出编码为 hex 字符串
	hmacHex := hex.EncodeToString(hmacOutput)

	// 输出结果
	// log.Println("HMAC-SHA-256 Hex Output:", hmacHex)

	// 添加签名到参数
	params["signature"] = hmacHex

	// 打印签名后的参数
	// log.Println("签名后的参数:", params)

	return params
}
