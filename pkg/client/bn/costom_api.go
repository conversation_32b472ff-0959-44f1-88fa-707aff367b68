package bn

import (
	"GoTrader/pkg/api"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	"encoding/json"
	"errors"
	"log/slog"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"
)

const (
	// API 限制常量
	MaxOrderIdLength = 36   // 订单ID最大长度
	WeightLimit      = 1500 // 权重限制阈值
	DepthLimit       = 1000 // 深度查询限制
	KlineLimit       = 24   // K线查询限制
)

// 错误消息常量
const (
	ErrClientBlocked                   = "BnCostomAPIClient is blocked"
	ErrFuturesOrderMinNotional         = "futures order value is less than min notional"
	ErrFuturesOrderInsufficientBalance = "futures order value is more than available balance"
	ErrMarginOrderMinNotional          = "margin order value is less than min notional"
	ErrMarginOrderInsufficientBalance  = "margin order value is more than available balance"
	ErrUnknownDataType                 = "unknown data type"
	ErrGetListenKeyFailed              = "get listenKey failed"
)

type BnCostomAPIClient struct {
	FuturesAPIURL string               // 期货RestfulAPIURL
	SpotAPIURL    string               // 现货RestfulAPIURL
	APIs          api.BnBrokerAPIgroup // api集合
	httpClient    *http.Client         // http连接实例
	blocked       *utils.BoolFlag      // 已阻断标记
	logger        *slog.Logger         // 日志对象
}

func NewCostomAPIClient(futuresAPIURL string, spotAPIURL string, apis api.BnBrokerAPIgroup, httpClient *http.Client, logger *slog.Logger) *BnCostomAPIClient {
	return &BnCostomAPIClient{
		FuturesAPIURL: futuresAPIURL,
		SpotAPIURL:    spotAPIURL,
		APIs:          apis,
		httpClient:    httpClient,
		blocked:       utils.NewBoolFlag(false),
		logger:        logger,
	}
}

// parseJSONResponse 通用的 JSON 解析函数，减少重复代码
func (c *BnCostomAPIClient) parseJSONResponse(response []byte, target interface{}, operation string) error {
	err := json.Unmarshal(response, target)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "operation", operation, "error", err)
		return err
	}
	return nil
}

// 发送期货api请求
func (c *BnCostomAPIClient) sendFutures(data map[string]any, rURL api.APIMethod, b *broker.Broker, signature bool) (response []byte, err error) {
	if c.blocked.IsTrue() {
		return nil, errors.New(ErrClientBlocked)
	}
	if signature {
		data["apiKey"] = b.ApiKey
		// 添加时间戳并签名
		data = signatureHMACRequest(data, b.Secret)
	}
	resp, usedWeight, statusCode, err := httpSend(data, c.FuturesAPIURL, rURL, b, c.httpClient, c.logger)

	if statusCode == 418 {
		// 418 Too Many Requests
		c.logger.Error("Too Many Requests, request has been blocked", "error", err, "statusCode", statusCode)
		return resp, err
	}

	if statusCode == 400 {
		// 400 Bad Request
		// 将err文本转换为json
		var errMsg ErrMsg
		json.Unmarshal(resp, &errMsg)
		if errMsg.Code == -2019 {
			// 余额不足
			err = errors.New("margin") // 保持原有的 "margin" 错误标识，可能被上层代码依赖
			return resp, err
		}
	}

	if usedWeight >= WeightLimit || statusCode == 429 {
		go func() {
			c.blocked.SetTrue()
			sec := utils.GetSecondsToNextMinute()
			c.logger.Warn("Wait until the next minute to reset the weight.", "seconds", sec, "usedWeight", usedWeight, "statusCode", statusCode)
			time.Sleep(time.Duration(sec) * time.Second)
			c.blocked.SetFalse()
		}()
	}
	return resp, err
}

// 发送现货api请求
func (c *BnCostomAPIClient) sendSpot(data map[string]any, rURL api.APIMethod, b *broker.Broker, signature bool) (response []byte, err error) {
	if c.blocked.IsTrue() {
		return nil, errors.New(ErrClientBlocked)
	}
	if signature {
		data = signatureHMACRequest(data, b.Secret)
	}
	resp, usedWeight, statusCode, err := httpSend(data, c.SpotAPIURL, rURL, b, c.httpClient, c.logger)
	if usedWeight >= WeightLimit || statusCode == 429 {
		go func() {
			c.blocked.SetTrue()
			sec := utils.GetSecondsToNextMinute()
			c.logger.Warn("Wait until the next minute to reset the weight.", "seconds", sec, "usedWeight", usedWeight, "statusCode", statusCode)
			time.Sleep(time.Duration(sec) * time.Second)
			c.blocked.SetFalse()
		}()
	}
	return resp, err
}

// 获取账户listenKey
func (c *BnCostomAPIClient) GetFuturesListenKey(b *broker.Broker) (string, error) {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetListenKey, b, true)
	if err != nil {
		return "", errors.Join(err, errors.New(ErrGetListenKeyFailed))
	}
	// 解析响应
	var listenKeyResponse ListenKeyResponse
	err = c.parseJSONResponse(response, &listenKeyResponse, "GetFuturesListenKey")
	if err != nil {
		return "", err
	}
	return listenKeyResponse.ListenKey, nil
}

// 获取账户listenKey
func (c *BnCostomAPIClient) GetSpotListenKey(b *broker.Broker) (string, error) {
	response, err := c.sendSpot(map[string]any{}, c.APIs.GetSpotListenKey, b, false)
	if err != nil {
		return "", errors.Join(err, errors.New(ErrGetListenKeyFailed))
	}
	// 解析响应
	var listenKeyResponse ListenKeyResponse
	err = c.parseJSONResponse(response, &listenKeyResponse, "GetSpotListenKey")
	if err != nil {
		return "", err
	}
	return listenKeyResponse.ListenKey, nil
}

// 提交期货订单
func (c *BnCostomAPIClient) SubmitFuturesOrder(b *broker.Broker, o *order.FuturesOrder, info *data.ExchangeInfo) (client.OrderViewer, error) {
	logger := c.logger.With("order", o)
	o = fixFuturesOrderClOrdIdPrefix(o)
	// 控制OrdId长度
	if len(o.ClOrdId) > MaxOrderIdLength {
		o.ClOrdId = o.ClOrdId[:MaxOrderIdLength]
	}
	// 计算订单价值
	orderValue := math.Abs(o.Size * o.Price)
	if o.IsOpen() && orderValue < info.MinNotional {
		// 订单价值小于最小价值
		logger.Warn("Futures order value is less than min notional", "orderValue", orderValue, "minNotional", info.MinNotional)
		return o, errors.New(ErrFuturesOrderMinNotional)
	}
	if o.IsOpen() && orderValue/float64(b.Leverages[o.Symbol]) > b.Account.Available {
		// 订单价值大于可用余额
		logger.Warn("Futures order value is more than available balance", "orderValue", orderValue, "available", b.Account.Available)
		return o, errors.New(ErrFuturesOrderInsufficientBalance)
	}
	// 将order加入borkerpending中
	b.AddFuturesOrder(o)

	orderRequest := &OrderRequest{
		ApiKey:      b.ApiKey,
		Symbol:      o.Symbol,
		OrdId:       o.ClOrdId,
		OrdType:     string(o.OrdType),
		PosSide:     string(o.PosSide),
		Price:       strconv.FormatFloat(o.Price, 'f', info.PricePrecision, 64),
		PriceMatch:  string(o.PriceMatch),
		Side:        string(o.Side),
		Size:        strconv.FormatFloat(math.Abs(o.Size), 'f', info.QuantityPrecision, 64),
		TimeInForce: string(o.TimeInForce),
	}

	if o.OrdType == order.MARKET {
		orderRequest.Price = ""
		orderRequest.TimeInForce = ""
	}

	// 处理PriceMatch和Price的冲突问题
	if o.PriceMatch != "" {
		orderRequest.Price = ""
	}

	params, err := utils.StructToMap(orderRequest)
	if err != nil {
		logger.Error("Error converting struct to map", "error", err)
		return o, err
	}

	// 签名并返回map
	response, err := c.sendFutures(params, c.APIs.FutureOrder, b, true)
	if err != nil {
		// 检查响应
		// 将order从borkerpending中移除
		o.ToReject()
		b.RemoveFuturesOrder(o)
		logger.Warn("Error sending Futures order", "error", err, "params", params, "info", info)
		return o, err
	}

	orderResponse := &OrderResponse{}
	err = c.parseJSONResponse(response, orderResponse, "SubmitFuturesOrder")
	if err != nil {
		// 解析错误, 拒绝订单并返回错误
		o.ToReject()
		b.RemoveFuturesOrder(o)
		logger.Error("Error unmarshalling JSON Futures order", "error", err, "response", string(response))
		return o, err
	}
	o.ToNew()
	return o, nil
}

// 提交杠杆订单, 实际提交现货订单
func (c *BnCostomAPIClient) SubmitMarginOrder(b *broker.Broker, o *order.MarginOrder, info *data.ExchangeInfo) (client.OrderViewer, error) {
	logger := c.logger.With("order", o)
	o = fixMarginOrderClOrdIdPrefix(o)
	// 控制OrdId长度
	if len(o.ClOrdId) > MaxOrderIdLength {
		o.ClOrdId = o.ClOrdId[:MaxOrderIdLength]
	}
	orderValue := math.Abs(o.Size * o.Price)
	if orderValue < info.MinNotional {
		logger.Warn("Margin order value is less than min notional", "orderValue", orderValue, "minNotional", info.MinNotional)
		return o, errors.New(ErrMarginOrderMinNotional)
	}
	if o.IsOpen() && orderValue > b.AssetWallets["USDT"].Free {
		logger.Warn("Margin order value is more than available balance", "orderValue", orderValue, "available", b.AssetWallets["USDT"].Free)
		return o, errors.New(ErrMarginOrderInsufficientBalance)
	}
	// 将order加入borkerpending中
	b.AddMarginOrder(o)
	orderRequest := &OrderRequest{
		Symbol:      o.Symbol,
		OrdId:       o.ClOrdId,
		OrdType:     string(o.OrdType),
		Price:       strconv.FormatFloat(o.Price, 'f', info.PricePrecision, 64),
		Side:        string(o.Side),
		Size:        strconv.FormatFloat(o.Size, 'f', info.QuantityPrecision, 64),
		TimeInForce: string(o.TimeInForce),
	}

	if o.OrdType == order.MARKET {
		orderRequest.Price = ""
		orderRequest.TimeInForce = ""
	}

	params, err := utils.StructToMap(orderRequest)
	if err != nil {
		return o, err
	}

	response, err := c.sendSpot(params, c.APIs.MarginOrder, b, true)
	if err != nil {
		// 检查响应
		// 将order从borkerpending中移除
		o.ToReject()
		b.RemoveMarginOrder(o)
		logger.Warn("Error sending Margin order", "error", err, "params", params, "info", info)
		return o, err
	}

	orderResponse := &OrderResponse{}
	err = c.parseJSONResponse(response, orderResponse, "SubmitMarginOrder")
	if err != nil {
		// 解析错误, 拒绝订单并返回错误
		o.ToReject()
		b.RemoveMarginOrder(o)
		logger.Error("Error unmarshalling JSON Margin order", "error", err, "response", string(response))
		return o, err
	}
	o.ToNew()
	return o, nil
}

// 修改期货订单
func (c *BnCostomAPIClient) ModifyFuturesOrder(b *broker.Broker, o order.FuturesOrder, info *data.ExchangeInfo) error {
	// 检查order是否在挂单中
	if !b.HasFuturesOrder(o.ClOrdId) {
		c.logger.Warn("Order not found in pending orders", "order", o)
		return nil
	}
	modifyOrdersRequest := ModifyOrderRequest{
		ClientOrderId: o.ClOrdId,
		Symbol:        o.Symbol,
		Side:          string(o.Side),
		Size:          strconv.FormatFloat(o.Size, 'f', info.QuantityPrecision, 64),
		Price:         strconv.FormatFloat(o.Price, 'f', info.PricePrecision, 64),
	}

	params, err := utils.StructToMap(modifyOrdersRequest)
	if err != nil {
		return err
	}

	_, err = c.sendFutures(params, c.APIs.ModifyFuturesOrder, b, true)
	if err != nil {
		c.logger.Warn("Error sending Modify Futures orders", "error", err, "params", params, "info", info)
		return err
	}

	return nil
}

// 更新broker信息
func (c *BnCostomAPIClient) UpdateBroker(b *broker.Broker) (err error) {
	if err = c.updateBrokerUMLeverage(b); err != nil {
		return err
	}
	if err = c.updateBrokerAccount(b); err != nil {
		return err
	}
	if err = c.updateBrokerUMAccount(b); err != nil {
		return err
	}
	if err = c.updateFuturesDataComm(b); err != nil {
		return err
	}
	if err = c.updateSpotDataComm(b); err != nil {
		return err
	}
	return nil
}

// 更新broker期货挂单
func (c *BnCostomAPIClient) UpdateBrokerUMPending(b *broker.Broker, ds []*data.SymbolData) (err error) {
	for _, d := range ds {
		if d.DataType == data.Futures {
			if err = c.QueryUMPending(b, d.Symbol); err != nil {
				return err
			}
		}
	}
	return nil
}

// 更新broker杠杆挂单
func (c *BnCostomAPIClient) UpdateBrokerMarginPending(b *broker.Broker, ds []*data.SymbolData) (err error) {
	for _, d := range ds {
		if d.DataType == data.Spot {
			if err = c.QueryMarginPending(b, d.Symbol); err != nil {
				return err
			}
		}
	}
	return nil
}

// 更新期货数据
func (c *BnCostomAPIClient) UpdateFuturesDatas(ds []*data.SymbolData) (err error) {
	if err = c.updateFuturesDataInfo(ds); err != nil {
		return err
	}
	if err = c.updateFuturesFunding(ds); err != nil {
		return err
	}
	if err = c.updateFuturesFundingInfo(ds); err != nil {
		return err
	}
	if err = c.updateFuturesDataPrice(ds); err != nil {
		return err
	}
	c.updateDepthMaxSize(ds)
	return nil
}

// 更新现货数据
func (c *BnCostomAPIClient) UpdateSpotDatas(ds []*data.SymbolData) (err error) {
	if err = c.updateSpotDataInfo(ds); err != nil {
		return err
	}
	if err = c.updateSpotDataPrice(ds); err != nil {
		return err
	}
	return nil
}

// 查询期货交易规范
func (c *BnCostomAPIClient) updateFuturesDataInfo(ds []*data.SymbolData) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesExchangeInfo, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var exchangeInfoResponse FuturesExchangeInfoResponse
	err = json.Unmarshal(response, &exchangeInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}

	exchangeInfoResponse.UpdateData(ds)

	return nil
}

// 查询并更新期货资金费率
func (c *BnCostomAPIClient) updateFuturesFunding(ds []*data.SymbolData) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesFunding, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var fundingResponse FuturesFundingResponse
	err = json.Unmarshal(response, &fundingResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	fundingResponse.UpdateData(ds)
	return nil
}

// 查询期货资金费率信息
func (c *BnCostomAPIClient) updateFuturesFundingInfo(ds []*data.SymbolData) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesFundingInfo, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var fundingInfoResponse FuturesFundingInfoResponse
	err = json.Unmarshal(response, &fundingInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	fundingInfoResponse.UpdateData(ds)
	return nil
}

// 查询现货交易规范
func (c *BnCostomAPIClient) updateSpotDataInfo(ds []*data.SymbolData) error {
	response, err := c.sendSpot(map[string]any{"showPermissionSets": false}, c.APIs.GetSpotExchangeInfo, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var exchangeInfoResponse SpotExchangeInfoResponse
	err = json.Unmarshal(response, &exchangeInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}

	infos := []*data.ExchangeInfo{}
	for _, d := range ds {
		infos = append(infos, d.Info)
	}
	exchangeInfoResponse.UpdateData(infos)

	return nil
}

// 查询期货当前价格
func (c *BnCostomAPIClient) updateFuturesDataPrice(ds []*data.SymbolData) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesPrice, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var priceResponse FuturesPriceResponce
	err = json.Unmarshal(response, &priceResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	priceResponse.UpdateData(ds)
	return nil
}

// 查询现货当前价格
func (c *BnCostomAPIClient) updateSpotDataPrice(ds []*data.SymbolData) error {
	response, err := c.sendSpot(map[string]any{}, c.APIs.GetSpotPrice, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var priceResponse SpotPriceResponce
	err = json.Unmarshal(response, &priceResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	priceResponse.UpdateData(ds)
	return nil
}

func (c *BnCostomAPIClient) updateDepthMaxSize(ds []*data.SymbolData) {
	for _, d := range ds {
		d.OrderBooks.MaxSize = DepthLimit
	}
}

// 取消所有期货订单
func (c *BnCostomAPIClient) CancelAllFutureOrders(b *broker.Broker, d *data.SymbolData) error {
	if b.GetFuturesOrders(d.Symbol) == nil {
		return nil
	}
	_, err := c.sendFutures(map[string]any{"symbol": d.Symbol}, c.APIs.CancelAllFuturesOrder, b, true)
	if err != nil {
		return err
	}
	c.logger.Info("Futures Orders Cancelled", "symbol", d.Symbol)
	return nil
}

// 取消所有杠杆订单
func (c *BnCostomAPIClient) CancelAllMarginOrders(b *broker.Broker, d *data.SymbolData) error {
	if b.GetMarginOrders(d.Symbol) == nil {
		return nil
	}
	_, err := c.sendSpot(map[string]any{"symbol": d.Symbol}, c.APIs.CancelAllMarginOrder, b, true)
	if err != nil {
		// 判断异常类型
		if httpErr, ok := err.(*HTTPStatusError); ok {
			if httpErr.StatusCode != 400 {
				// 400错误表示没有找到任何杠杆订单, 合法操作
				return err
			}
		}
	}
	c.logger.Info("Margin Orders Cancelled", "symbol", d.Symbol)
	return nil
}

// 取消期货订单
func (c *BnCostomAPIClient) CancelFuturesOrder(b *broker.Broker, o *order.FuturesOrder) error {
	response, err := c.sendFutures(map[string]any{"symbol": o.Symbol, "origClientOrderId": o.ClOrdId}, c.APIs.CancelFuturesOrder, b, true)
	if err != nil {
		errMsg := ErrMsg{}
		json.Unmarshal(response, &errMsg)
		if errMsg.Code == -2011 {
			// 订单不存在, 已经被取消
			c.logger.Warn("期货订单不存在, 查询订单并手动删除", "order", o)
			// 查询一次订单状态
			err = c.UpdateFuturesOrder(b, o)
			if err != nil {
				c.logger.Warn("查询订单状态失败", "error", err)
			}
			c.logger.Info("更新撤销订单状态", "order", o)
			if !o.Alive() {
				b.RemoveFuturesOrder(o)
			}
			return nil
		}
		return err
	}
	c.logger.Info("Futures Order Cancelled", "order", o)
	return nil
}

// 取消杠杆订单
func (c *BnCostomAPIClient) CancelMarginOrder(b *broker.Broker, o *order.MarginOrder) error {
	response, err := c.sendSpot(map[string]any{"symbol": o.Symbol, "origClientOrderId": o.ClOrdId}, c.APIs.CancelMarginOrder, b, true)
	if err != nil {
		errMsg := ErrMsg{}
		json.Unmarshal(response, &errMsg)
		if errMsg.Code == -2011 {
			// 订单不存在, 已经被取消
			c.logger.Warn("杠杆订单不存在, 手动删除", "order", o)
			// 查询一次订单状态
			err = c.UpdateMarginOrder(b, o)
			if err != nil {
				c.logger.Warn("查询订单状态失败", "error", err)
			}
			c.logger.Info("更新撤销订单状态", "order", o)
			if !o.Alive() {
				b.RemoveMarginOrder(o)
			}
			return nil
		}
		return err
	}
	c.logger.Info("Margin Order Cancelled", "order", o)
	return nil
}

// 查询期货所有可交易品种
func (c *BnCostomAPIClient) QueryAllTradingFutures() []string {
	resp, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesExchangeInfo, nil, false)
	if err != nil {
		return nil
	}
	var exchangeInfoResponse FuturesExchangeInfoResponse
	err = json.Unmarshal(resp, &exchangeInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return nil
	}
	symbols := []string{}
	for _, s := range exchangeInfoResponse.Symbols {
		if s.Status == "TRADING" {
			symbols = append(symbols, s.Symbol)
		}
	}
	return symbols
}

// 查询期货所有资金费率
func (c *BnCostomAPIClient) QueryAllFunding() map[string]float64 {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetFuturesFunding, nil, false)
	if err != nil {
		return nil
	}
	// 解析响应
	var fundingResponse FuturesFundingResponse
	err = json.Unmarshal(response, &fundingResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return nil
	}
	r := make(map[string]float64)
	for _, f := range fundingResponse {
		r[f.Symbol] = utils.StringToFloat64(f.Funding)
	}
	return r
}

// 查询所有可交易杠杆品种
func (c *BnCostomAPIClient) QueryAllTradingMargin(b *broker.Broker) []string {
	resp, err := c.sendSpot(map[string]any{}, c.APIs.GetMarginPairs, b, true)
	if err != nil {
		return nil
	}
	var marginPairsResponse MarginPairsResponse
	err = json.Unmarshal(resp, &marginPairsResponse)
	if err != nil {
		c.logger.Info("Error unmarshalling JSON", "error", err)
		return nil
	}
	symbols := []string{}
	for _, s := range marginPairsResponse {
		if s.Quote == "USDT" && s.AllowTrade {
			symbols = append(symbols, s.Symbol)
		}
	}
	return symbols
}

// 期货平仓
func (c *BnCostomAPIClient) CloseFutures(b *broker.Broker, d *data.SymbolData) (err error) {
	// 期货多仓平仓
	if math.Abs(b.GetFuturesSizeLong(d.Symbol)) > 0 {
		futuresOrderLong := order.CreateFuturesOrder().SetSymbol(d.Symbol).
			SetSize(math.Abs(b.GetFuturesSizeLong(d.Symbol))).SetSide(order.SELL).SetPosSide(order.LONG).SetOrdType(order.MARKET).SetTimeInForce(order.GTC)
		_, err := c.SubmitFuturesOrder(
			b, futuresOrderLong, d.Info,
		)
		if err != nil {
			c.logger.Error(err.Error())
			return err
		}
	}
	if math.Abs(b.GetFuturesSizeShort(d.Symbol)) > 0 {
		// 期货空仓平仓
		futuresOrderShort := order.CreateFuturesOrder().SetSymbol(d.Symbol).
			SetSize(math.Abs(b.GetFuturesSizeShort(d.Symbol))).SetSide(order.BUY).SetPosSide(order.SHORT).SetOrdType(order.MARKET)
		_, err := c.SubmitFuturesOrder(
			b, futuresOrderShort, d.Info,
		)
		if err != nil {
			c.logger.Error(err.Error())
			return err
		}
	}
	return nil
}

// 现货平仓
func (c *BnCostomAPIClient) CloseMargin(b *broker.Broker, d *data.SymbolData) (err error) {
	size := b.GetAssetTotalFloor(d.Asset, d.Info)
	if size > 0 {
		marginOrder := order.CreateMarginOrder().SetSymbol(d.Symbol).SetPrice(d.Price.Last()).
			SetSize(size).SetSide(order.SELL).SetOrdType(order.MARKET)
		_, err := c.SubmitMarginOrder(
			b, marginOrder, d.Info,
		)
		if err != nil {
			c.logger.Error(err.Error())
		}
	}
	return nil
}

// 修改期货杠杆
func (c *BnCostomAPIClient) ModifyFuturesLeverage(b *broker.Broker, d *data.SymbolData, lever int64) (err error) {
	_, err = c.sendFutures(map[string]any{"symbol": d.Symbol, "leverage": lever}, c.APIs.ModifyFuturesLeverage, b, true)
	if err != nil {
		return err
	}
	c.logger.Info("Modify Futures Leverage Success", "symbol", d.Symbol, "lever", lever)
	return nil
}

// 查询单币种杠杆
func (c *BnCostomAPIClient) UpdateFuturesLeverageSingle(b *broker.Broker, d *data.SymbolData) error {
	return nil
}

// 请求账户资产
func (c *BnCostomAPIClient) UpdateBrokerBalance(b *broker.Broker) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetBalance, b, true)
	if err != nil {
		c.logger.Error("Error sending QueryBalance request", "error", err)
		return err
	}

	// 解析响应
	var balanceResponse FuturesBalanceResponse
	err = json.Unmarshal(response, &balanceResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	balanceResponse.UpdateBroker(b)

	return nil
}

// 请求现货账户信息
func (c *BnCostomAPIClient) updateBrokerAccount(b *broker.Broker) error {
	response, err := c.sendSpot(map[string]any{}, c.APIs.GetAccount, b, true)
	if err != nil {
		return err
	}
	// 解析响应
	var brokerInfoResponse SpotAccountResponse
	err = json.Unmarshal(response, &brokerInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	brokerInfoResponse.UpdateBroker(b)

	return nil
}

// 请求期货账户信息
func (c *BnCostomAPIClient) updateBrokerUMAccount(b *broker.Broker) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetUMAccount, b, true)
	if err != nil {
		return err
	}
	// 解析响应
	var brokerInfoResponse FuturesAccountResponse
	err = json.Unmarshal(response, &brokerInfoResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	brokerInfoResponse.UpdateBroker(b)

	return nil
}

// 请求期货账户信息并直接返回
func (c *BnCostomAPIClient) QueryAccount(b *broker.Broker) (string, error) {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetUMAccount, b, true)
	if err != nil {
		return "", err
	}

	return string(response), nil
}

// 查询UM账户未完成订单
func (c *BnCostomAPIClient) QueryUMPending(b *broker.Broker, s string) error {
	s = strings.ToUpper(s)
	response, err := c.sendFutures(map[string]any{"symbol": s}, c.APIs.GetUMPending, b, true)
	if err != nil {
		return err
	}

	// 解析响应
	var brokerUMPendingResponse BrokerUMPendingResponse
	err = json.Unmarshal(response, &brokerUMPendingResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	brokerUMPendingResponse.UpdateBroker(b)

	return nil
}

// 查询杠杆账户未完成订单
func (c *BnCostomAPIClient) QueryMarginPending(b *broker.Broker, s string) error {
	s = strings.ToUpper(s)
	response, err := c.sendSpot(map[string]any{"symbol": s}, c.APIs.GetMarginPending, b, true)
	if err != nil {
		return err
	}

	// 解析响应
	var brokerMarginPendingResponse BrokerMarginPendingResponse
	err = json.Unmarshal(response, &brokerMarginPendingResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}

	brokerMarginPendingResponse.UpdateBroker(b)

	return nil
}

// 查看合约账户杠杆
func (c *BnCostomAPIClient) updateBrokerUMLeverage(b *broker.Broker) error {
	response, err := c.sendFutures(map[string]any{}, c.APIs.GetUMConfig, b, true)
	if err != nil {
		return err
	}

	// 解析响应
	var brokerUMConfigResponse BrokerUMConfigResponse
	err = json.Unmarshal(response, &brokerUMConfigResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	brokerUMConfigResponse.UpdateBroker(b)

	return nil
}

func (c *BnCostomAPIClient) SubmitOrder(b *broker.Broker, o *order.Order, d *data.SymbolData) (client.OrderViewer, error) {
	switch d.DataType {
	case data.Futures:
		var posSide order.PosSide
		// 根据当前仓位和买卖方向判断posSide
		sizeLong, sizeShort := b.GetFuturesSize(d.Symbol)
		size := sizeLong + sizeShort
		switch {
		case size > 0:
			posSide = order.LONG
		case size < 0:
			posSide = order.SHORT
		case o.Side == order.SELL:
			posSide = order.SHORT
		case o.Side == order.BUY:
			posSide = order.LONG
		}
		fo := order.CreateFuturesOrderFromCommon(o, posSide)
		return c.SubmitFuturesOrder(b, fo, d.Info)
	case data.Spot:
		mo := order.CreateMarginOrderFromCommon(o)
		return c.SubmitMarginOrder(b, mo, d.Info)
	default:
		return nil, errors.New(ErrUnknownDataType)
	}
}

func (c *BnCostomAPIClient) CancelAllOrders(b *broker.Broker, d *data.SymbolData) error {
	switch d.DataType {
	case data.Futures:
		return c.CancelAllFutureOrders(b, d)
	case data.Spot:
		return c.CancelAllMarginOrders(b, d)
	}
	return nil
}

func (c *BnCostomAPIClient) Close(b *broker.Broker, d *data.SymbolData) error {
	switch d.DataType {
	case data.Futures:
		return c.CloseFutures(b, d)
	case data.Spot:
		return c.CloseMargin(b, d)
	}
	return nil
}

func (c *BnCostomAPIClient) UpdateDatas(ds []*data.SymbolData) error {
	fd := make([]*data.SymbolData, 0)
	sd := make([]*data.SymbolData, 0)

	for _, d := range ds {
		switch d.DataType {
		case data.Futures:
			fd = append(fd, d)
		case data.Spot:
			sd = append(sd, d)
		}
	}
	var err error
	err = c.UpdateFuturesDatas(fd)
	if err != nil {
		return err
	}
	err = c.UpdateSpotDatas(sd)
	if err != nil {
		return err
	}
	return nil
}

// 查询仓位档位
func (c *BnCostomAPIClient) UpdatePositionTiers(b *broker.Broker, d *data.SymbolData) error {
	// if d.DataType != data.Futures {
	// 	return nil
	// }
	response, err := c.sendFutures(map[string]any{"symbol": d.Symbol}, c.APIs.GetPositionTiers, b, true)
	if err != nil {
		return err
	}
	// 解析响应
	var positionTiersResponse PositionTiersResponse
	err = json.Unmarshal(response, &positionTiersResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	positionTiersResponse.UpdateBroker(b)
	return nil
}

// 查询张币系数
func (c *BnCostomAPIClient) UpdateCtVal(b *broker.Broker, d *data.SymbolData) error {
	b.CtVals[d.Symbol] = 1
	return nil
}

// 查询期货手续费
func (c *BnCostomAPIClient) updateFuturesDataComm(b *broker.Broker) error {
	response, err := c.sendFutures(map[string]any{"symbol": "BTCUSDT"}, c.APIs.GetFuturesCommissionRate, b, true)
	if err != nil {
		return err
	}
	// 解析响应
	var commissionResponse FuturesCommissionRateResponse
	err = json.Unmarshal(response, &commissionResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	commissionResponse.UpdateBroker(b)
	return nil
}

// 查询现货手续费
func (c *BnCostomAPIClient) updateSpotDataComm(b *broker.Broker) error {
	response, err := c.sendSpot(map[string]any{"symbol": "BTCUSDT"}, c.APIs.GetSpotCommissionRate, b, true)
	if err != nil {
		return err
	}
	// 解析响应
	var commissionResponse SpotCommissionRateResponse
	err = json.Unmarshal(response, &commissionResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	commissionResponse.UpdateBroker(b)
	return nil
}

func (c *BnCostomAPIClient) UpdateDepth(d *data.SymbolData) error {
	switch d.DataType {
	case data.Futures:
		return c.updateFuturesDepth(d)
	case data.Spot:
		return c.updateSpotDepth(d)
	}
	return nil
}

func (c *BnCostomAPIClient) updateFuturesDepth(d *data.SymbolData) error {
	response, err := c.sendFutures(map[string]any{"symbol": d.Symbol, "limit": DepthLimit}, c.APIs.GetFuturesDepth, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var depthResponse DepthResponse
	err = json.Unmarshal(response, &depthResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	depthResponse.UpdateData(d)
	return nil
}

func (c *BnCostomAPIClient) updateSpotDepth(d *data.SymbolData) error {
	response, err := c.sendSpot(map[string]any{"symbol": d.Symbol, "limit": DepthLimit}, c.APIs.GetSpotDepth, nil, false)
	if err != nil {
		return err
	}
	// 解析响应
	var depthResponse DepthResponse
	err = json.Unmarshal(response, &depthResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return err
	}
	depthResponse.UpdateData(d)
	return nil
}

func (c *BnCostomAPIClient) Query24hrQuote(d *data.SymbolData) (float64, error) {
	var response []byte
	var err error
	if d.DataType == data.Spot {
		response, err = c.sendSpot(map[string]any{"symbol": d.Symbol, "interval": "1h", "limit": KlineLimit}, c.APIs.GetSpotKlines, nil, false)
	}
	if d.DataType == data.Futures {
		response, err = c.sendFutures(map[string]any{"symbol": d.Symbol, "interval": "1h", "limit": KlineLimit}, c.APIs.GetFuturesKlines, nil, false)
	}
	if err != nil {
		return 0, err
	}
	// 解析响应
	var klines KLineResponse
	err = json.Unmarshal(response, &klines)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return 0, err
	}
	quote := 0.0
	errs := make([]error, 0)
	for _, kline := range klines {
		quote += utils.MustParseFloat64(kline[7].(string), &errs)
	}
	return quote, utils.CollectErrors(errs)
}

// 查询持仓量
func (c *BnCostomAPIClient) QueryOpenInterest(d *data.SymbolData) (float64, error) {
	response, err := c.sendFutures(map[string]any{"symbol": d.Symbol}, c.APIs.GetFuturesOpenInterest, nil, false)
	if err != nil {
		return 0, err
	}
	// 解析响应
	var openInterestResponse OpenInterestResponse
	err = json.Unmarshal(response, &openInterestResponse)
	if err != nil {
		c.logger.Error("Error unmarshalling JSON", "error", err)
		return 0, err
	}
	count := utils.StringToFloat64(openInterestResponse.OI)
	return count, nil
}

// 更新订单
func (c *BnCostomAPIClient) UpdateFuturesOrder(b *broker.Broker, o *order.FuturesOrder) error {
	var params map[string]any
	if o.ClOrdId != "" {
		params = map[string]any{"symbol": o.Symbol, "origClientOrderId": o.ClOrdId}
	} else {
		params = map[string]any{"symbol": o.Symbol, "orderId": o.OrdId}
	}
	response, err := c.sendFutures(params, c.APIs.QueryFuturesOrder, b, true)
	if err != nil {
		return err
	}
	// 解析响应
	var futuresOrderResponse FuturesOrderResponse
	err = c.parseJSONResponse(response, &futuresOrderResponse, "UpdateFuturesOrder")
	if err != nil {
		return err
	}
	futuresOrderResponse.UpdateBroker(b)
	return nil
}

// 更新订单
func (c *BnCostomAPIClient) UpdateMarginOrder(b *broker.Broker, o *order.MarginOrder) error {
	var params map[string]any
	if o.ClOrdId != "" {
		params = map[string]any{"symbol": o.Symbol, "origClientOrderId": o.ClOrdId}
	} else {
		params = map[string]any{"symbol": o.Symbol, "orderId": o.OrdId}
	}
	response, err := c.sendSpot(params, c.APIs.QueryMarginOrder, b, true)
	if err != nil {
		return err
	}
	// 解析响应
	var marginOrderResponse MarginOrderResponse
	err = c.parseJSONResponse(response, &marginOrderResponse, "UpdateMarginOrder")
	if err != nil {
		return err
	}
	marginOrderResponse.UpdateBroker(b)
	return nil
}
