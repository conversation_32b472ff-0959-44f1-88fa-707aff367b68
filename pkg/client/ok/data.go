package ok

import (
	"GoTrader/internal/ok"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/handler"
	"GoTrader/pkg/utils"
	"GoTrader/pkg/wsclient"
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"
)

var _ client.DataClient = (*OkDataClient)(nil) // 确保实现了DataClient接口

// 欧意数据源客户端
type OkDataClient struct {
	url      string                                                                        // WebsocketURL
	ws       wsclient.WSManager                                                            // WebSocket 行情推送连接实例
	channels []string                                                                      // 订阅的数据频道
	datas    map[string]*data.SymbolData                                                   // 数据对象, map[交易对]数据对象
	handlers map[handler.WSStreamType][]func([]byte, *data.SymbolData, *slog.Logger) error // 数据处理函数, map[事件类型][交易对]处理函数
	dataMu   sync.RWMutex                                                                  // 数据锁
	token    *utils.TokenBucket                                                            // 令牌桶
	logger   *slog.Logger                                                                  // 日志对象
}

func NewDataClient(url string, channels []string, ws wsclient.WSManager, logger *slog.Logger) *OkDataClient {
	logger = logger.With("client", "okData").With("url", url).With("channels", fmt.Sprintf("%v", channels))
	c := &OkDataClient{
		url:      url,
		ws:       ws,
		channels: channels,
		datas:    make(map[string]*data.SymbolData),
		handlers: map[handler.WSStreamType][]func([]byte, *data.SymbolData, *slog.Logger) error{},
		dataMu:   sync.RWMutex{},
		token:    nil,
		logger:   logger,
	}
	ws.SetReInitFunc(c.reSubscribeDatas)
	return c
}

// 启动数据源
func (c *OkDataClient) Run(ctx context.Context) error {
	err := c.connect(ctx)
	if err != nil {
		c.logger.Error("error connect ws", "error", err)
		return err
	}
	c.listen(ctx)
	return nil
}

func (c *OkDataClient) AddStreamHandler(event handler.WSStreamType, handler func([]byte, *data.SymbolData, *slog.Logger) error) client.DataClient {
	c.handlers[event] = append(c.handlers[event], handler)
	return c
}

// 订阅数据源
func (c *OkDataClient) SubscribeDatas(ds []*data.SymbolData) error {
	c.dataMu.Lock()
	for _, d := range ds {
		key, err := DataToInstId(d)
		if err != nil {
			c.logger.Error("转换失败", "error", err)
			return err
		}
		c.datas[key] = d
	}
	c.dataMu.Unlock()
	for _, channel := range c.channels {
		ctx, cancel := context.WithCancel(context.Background())
		ver := c.ws.GetVersion()
		err := WriteWSChannelMessage(ctx, ver, c.ws, ds, c.token, "subscribe", channel, c.logger)
		cancel()
		if err != nil {
			c.logger.Error("SubScribe aggTrade error")
			return err
		}
	}
	return nil
}

// 取消订阅数据
func (c *OkDataClient) UnsubscribeDatas(ds []*data.SymbolData) error {
	for _, channel := range c.channels {
		ctx, cancel := context.WithCancel(context.Background())
		ver := c.ws.GetVersion()
		err := WriteWSChannelMessage(ctx, ver, c.ws, ds, c.token, "unsubscribe", channel, c.logger)
		cancel()
		if err != nil {
			c.logger.Error("UnsubScribe aggTrade error")
			return err
		}
	}
	return nil
}

// 连接数据源
func (c *OkDataClient) connect(ctx context.Context) error {
	c.token = utils.NewTokenBucket(ctx, 50, 50)
	err := c.ws.Connect(ctx, c.url, c.logger)
	if err != nil {
		c.logger.Error("error connect ws", "error", err)
		return err
	} else {
		c.logger.Info("connect ws success")
	}
	return nil
}

// 监听行情
func (c *OkDataClient) listen(ctx context.Context) {
	go ListenStreamWS(ctx, c.ws, c.handlers, c.datas, &c.dataMu, c.logger)
}

// 重新订阅已有数据
func (c *OkDataClient) reSubscribeDatas(ctx context.Context, ver int64) error {
	// 将datamap中的data生成列表
	ds := make([]*data.SymbolData, 0, len(c.datas))
	for _, v := range c.datas {
		ds = append(ds, v)
	}
	c.logger.Info("reSubscribeDatas", "count", len(ds))
	for _, channel := range c.channels {
		err := WriteWSChannelMessage(ctx, ver, c.ws, ds, c.token, "subscribe", channel, c.logger)
		if err != nil {
			c.logger.Error("SubScribe channel error", "channel", channel)
			return err
		}
	}
	return nil
}

// 重新订阅深度
// 不对回执作判断, 交给eventhandle处理; 订阅失败会导致数据获取超时
func (c *OkDataClient) ReSubscribeDepth(ctx context.Context, d *data.SymbolData) error {
	// chanName := "reSubscribeDepth"
	// c.ws.SubscribeReadChan(chanName)
	// defer c.ws.UnsubscribeReadChan(chanName)
	sendCtx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()
	ver := c.ws.GetVersion()
	// 取消订阅
	err := WriteWSChannelMessage(sendCtx, ver, c.ws, []*data.SymbolData{d}, c.token, "unsubscribe", ok.Depth400Chan, c.logger)
	if err != nil {
		c.logger.Error("UnsubScribe channel error", "channel", ok.Depth400Chan)
		return err
	}
	// receiveCtx, cancel := context.WithTimeout(ctx, time.Second*5)
	// defer cancel()
	// _, _, err = getEventFromWS(receiveCtx, c.ws, chanName, "unsubscribe", c.logger)
	// if err != nil && err != context.DeadlineExceeded {
	// 	c.logger.Error("UnsubScribe response check failed", "error", err)
	// 	return err
	// }
	// c.logger.Info("UnsubScribe response check success")
	// 重新订阅
	sendCtx, cancel = context.WithTimeout(ctx, time.Second*5)
	defer cancel()
	err = WriteWSChannelMessage(sendCtx, ver, c.ws, []*data.SymbolData{d}, c.token, "subscribe", ok.Depth400Chan, c.logger)
	if err != nil {
		c.logger.Error("SubScribe channel error", "channel", ok.Depth400Chan)
		return err
	}
	// receiveCtx, cancel = context.WithTimeout(ctx, time.Second*5)
	// defer cancel()
	// _, _, err = getEventFromWS(receiveCtx, c.ws, chanName, "subscribe", c.logger)
	// if err != nil && err != context.DeadlineExceeded {
	// 	c.logger.Error("Subscribe response check failed", "error", err)
	// 	return err
	// }
	// c.logger.Info("Subscribe response check success")
	return nil
}
