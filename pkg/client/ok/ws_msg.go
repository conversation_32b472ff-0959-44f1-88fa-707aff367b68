package ok

// 账户信息推送
type BrokerWebSocketEvent struct {
	Event     string `json:"e"`
	EventTime int64  `json:"E"`
}

// 账户信息推送
type BrokerWebSocketEventOk struct {
	Event string `json:"event"`
	Arg   struct {
		Channel string `json:"channel"`
	} `json:"arg"`
	Msg  string `json:"msg"`
	Code string `json:"code"`
}

// 数据订阅信息推送
type StreamWebSocketEvent struct {
	Stream string `json:"stream"`
	Data   struct {
		Event  string `json:"e"`
		Symbol string `json:"s"`
		Time   int64  `json:"E"`
	} `json:"data"`
}

// 数据订阅信息推送
type StreamWebSocketEventOk struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
}

// 现货深度信息推送
type SpotStreamDepthWebSocketEvent struct {
	Stream string `json:"stream"`
	Data   struct {
		LastUpdateId int64 `json:"lastUpdateId"`
	} `json:"data"`
}
type SpotStreamDepthWebSocketEventOk struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
}

type FuturesStreamDepthWebSocketEvent struct {
	Stream string `json:"stream"`
	Data   struct {
		Event     string `json:"e"`
		EventTime int64  `json:"E"`
	} `json:"data"`
}
type FuturesStreamDepthWebSocketEventOk struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
}
