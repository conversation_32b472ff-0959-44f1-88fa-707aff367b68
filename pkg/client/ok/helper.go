package ok

import (
	"GoTrader/internal/ok"
	"GoTrader/pkg/data"
	"GoTrader/pkg/handler"
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	okUtils "GoTrader/pkg/utils/ok"
	ws "GoTrader/pkg/wsclient"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"math/rand"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

func DataToInstId(d *data.SymbolData) (string, error) {
	switch d.DataType {
	case data.Futures:
		return okUtils.SymbolToInstIdFutures(d.Symbol), nil
	case data.Spot:
		return okUtils.SymbolToInstIdSpot(d.Symbol), nil
	default:
		return "", fmt.Errorf("unknown data type: %s", d.DataType)
	}
}

func ListenStreamWS(
	ctx context.Context,
	wsManager ws.WSManager,
	handlerMap map[handler.WSStreamType][]func([]byte, *data.SymbolData, *slog.Logger) error,
	dataMap map[string]*data.SymbolData,
	mu *sync.RWMutex,
	logger *slog.Logger,
) {
	for {
		// 等待接收数据
		_, msg, err := wsManager.ReadMessage()
		if err != nil {
			if ctx.Err() != nil {
				// ctx结束
				return
			}
			if errors.Is(err, ws.ErrReconnected) {
				// 重连成功处理逻辑
				logger.Warn("Data ws reconnected", "error", err)
				continue
			}
			logger.Error("data ws 接收消息失败", "error", err)
			continue
		}
		if string(msg) == "pong" {
			logger.Info("Received pong")
			continue
		}
		// 解析消息
		var streamEvent StreamWebSocketEventOk
		err = json.Unmarshal(msg, &streamEvent)
		if err != nil {
			logger.Error("解析消息失败: ", "error", err, "msg", string(msg))
			continue
		}
		// 执行事件处理函数
		switch streamEvent.Arg.Channel {
		case "error":
			logger.Warn("data ws 订阅错误", "msg", string(msg))
			return
		case "subscribe":
			logger.Info("data ws 订阅成功", "channel", streamEvent.Arg.Channel)
			continue
		default:
			// 执行事件处理函数
			event := handler.WSStreamType(streamEvent.Arg.Channel)
			handlers, exists := handlerMap[event]
			if !exists {
				logger.Error("未找到事件处理函数: ", "event", event, "msg", string(msg))
				continue
			}
			s := streamEvent.Arg.InstId
			// 指数价格更新特殊处理
			if streamEvent.Arg.Channel == ok.IndexPriceChan {
				// 指数价格更新在期货数据中
				s = okUtils.InstIdSpotToFutures(s)
			}
			mu.RLock()
			d, exists := dataMap[s]
			mu.RUnlock()
			if !exists {
				logger.Error("未找到数据", "error", s, "msg", string(msg))
				continue
			}
			for _, handler := range handlers {
				handler(msg, d, logger)
			}
		}

	}
}

func WriteWSChannelMessage(ctx context.Context, ver int64, ws ws.WSManager, ds []*data.SymbolData, tb *utils.TokenBucket, method string, channel string, logger *slog.Logger) error {
	for len(ds) > 0 {
		if ctx.Err() != nil {
			logger.Warn("ctx canceled, stop write ws channel message")
			return ctx.Err()
		}
		tokenCtx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		defer cancel()
		// 一次占用2个令牌, 防止速度过快
		token := tb.Take(tokenCtx, len(ds), 2)
		subds := ds[:token]
		ds = ds[token:]
		if len(subds) == 0 {
			continue
		}
		params := []data.ArgItem{}
		for _, d := range subds {
			var instId string
			var err error
			// indexprice需要特殊处理, 数据更新在期货data, 订阅字段需要按照现货方式订阅
			if channel == ok.IndexPriceChan {
				instId = okUtils.SymbolToInstIdSpot(d.Symbol)
			} else {
				instId, err = DataToInstId(d)
			}
			if err != nil {
				logger.Error("数据转换失败", "error", err)
				return err
			}

			itemTrades := data.ArgItem{
				Channel: channel,
				InstId:  instId,
			}
			params = append(params, itemTrades)
		}

		aggSubscribeRequest := data.SubscribeRequestOk{
			Method: method,
			Params: params,
		}

		var subscribeMessage []byte
		subscribeMessage, err := json.Marshal(aggSubscribeRequest)
		if err != nil {
			logger.Error("序列化订阅失败", "error", err)
		}

		// 验证版本号
		if !ws.IsVersionValid(ver) {
			logger.Info("ws version changed, stop write ws channel message")
			return fmt.Errorf("ws version changed")
		}
		err = ws.WriteMessage(websocket.TextMessage, subscribeMessage)
		if err != nil {
			logger.Error("订阅发送失败", "error", err)
			return err
		}
		logger.Info("订阅发送成功", "subscribeMessage", string(subscribeMessage))
	}
	//再次验证版本号
	if !ws.IsVersionValid(ver) {
		logger.Info("ws version changed, stop write ws channel message")
		return fmt.Errorf("ws version changed")
	}
	logger.Info("全部频道订阅完毕")
	return nil
}

// 期货订单填充clordid
func fixFuturesOrderClOrdId(o *order.FuturesOrder) *order.FuturesOrder {
	o.ClOrdId = generateValidClordId(o.ClOrdId)
	return o
}

// 杠杆订单填充clordid
func fixMarginOrderClOrdId(o *order.MarginOrder) *order.MarginOrder {
	o.ClOrdId = generateValidClordId(o.ClOrdId)
	return o
}

// 填充 ID 长度直到 32 位
func generateValidClordId(id string) string {
	// 2. 检查长度是否符合要求
	if len(id) < 32 {
		// 生成随机字母
		randomSuffix := generateRandomString(32 - len(id))
		id += randomSuffix
	}

	// 3. 确保长度在 32 位以内
	if len(id) > 32 {
		id = id[:32] // 截取多余部分
	}

	return id
}

// 生成指定长度的随机字母字符串
func generateRandomString(length int) string {
	const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	result := make([]byte, length)
	rng := rand.New(rand.NewSource(time.Now().UnixNano())) // 创建新的随机数生成器
	for i := range result {
		// 随机选择字母
		result[i] = letters[rng.Intn(len(letters))]
	}
	return string(result)
}

// 订单参数添加tag
func addTagToOrderParams(p map[string]any) map[string]any {
	p["tag"] = "059de09b75e8BCDE"
	return p
}
