package ok

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	okUtils "GoTrader/pkg/utils/ok"
	"math"
	"strconv"
)

// ListenKey响应
type ListenKeyResponse struct {
	ListenKey string `json:"listenKey"`
}

// 撮合交易响应
type TradeResponse struct {
	Code string       `json:"code"`
	Msg  string       `json:"msg"`
	Data []ErrCodeMsg `json:"data"`
}

// CodeMsg
type ErrCodeMsg struct {
	Code string `json:"sCode"`
	Msg  string `json:"sMsg"`
}

func (r ErrCodeMsg) Error() string {
	return r.Msg
}

// 账户资产响应
type BalanceResponse struct {
	Code string               `json:"code"`
	Data []BalanceAccountData `json:"data"`
	Msg  string               `json:"msg"`
}

// Data 部分的结构体
type BalanceAccountData struct {
	AdjEq       string                 `json:"adjEq"`
	BorrowFroz  string                 `json:"borrowFroz"`
	Details     []BalanceAccountDetail `json:"details"`
	Imr         string                 `json:"imr"`
	IsoEq       string                 `json:"isoEq"`
	MgnRatio    string                 `json:"mgnRatio"`
	Mmr         string                 `json:"mmr"`
	NotionalUsd string                 `json:"notionalUsd"`
	OrdFroz     string                 `json:"ordFroz"`
	TotalEq     string                 `json:"totalEq"`
	UTime       string                 `json:"uTime"`
	Upl         string                 `json:"upl"`
}

// AccountDetail 部分的结构体
type BalanceAccountDetail struct {
	AvailBal          string `json:"availBal"`
	AvailEq           string `json:"availEq"`
	BorrowFroz        string `json:"borrowFroz"`
	CashBal           string `json:"cashBal"`
	Ccy               string `json:"ccy"`
	CrossLiab         string `json:"crossLiab"`
	DisEq             string `json:"disEq"`
	Eq                string `json:"eq"`
	EqUsd             string `json:"eqUsd"`
	SmtSyncEq         string `json:"smtSyncEq"`
	SpotCopyTradingEq string `json:"spotCopyTradingEq"`
	FixedBal          string `json:"fixedBal"`
	FrozenBal         string `json:"frozenBal"`
	Imr               string `json:"imr"`
	Interest          string `json:"interest"`
	IsoEq             string `json:"isoEq"`
	IsoLiab           string `json:"isoLiab"`
	IsoUpl            string `json:"isoUpl"`
	Liab              string `json:"liab"`
	MaxLoan           string `json:"maxLoan"`
	MgnRatio          string `json:"mgnRatio"`
	Mmr               string `json:"mmr"`
	NotionalLever     string `json:"notionalLever"`
	OrdFrozen         string `json:"ordFrozen"`
	RewardBal         string `json:"rewardBal"`
	SpotInUseAmt      string `json:"spotInUseAmt"`
	ClSpotInUseAmt    string `json:"clSpotInUseAmt"`
	MaxSpotInUse      string `json:"maxSpotInUse"`
	SpotIsoBal        string `json:"spotIsoBal"`
	StgyEq            string `json:"stgyEq"`
	Twap              string `json:"twap"`
	UTime             string `json:"uTime"`
	Upl               string `json:"upl"`
	UplLiab           string `json:"uplLiab"`
	SpotBal           string `json:"spotBal"`
	OpenAvgPx         string `json:"openAvgPx"`
	AccAvgPx          string `json:"accAvgPx"`
	SpotUpl           string `json:"spotUpl"`
	SpotUplRatio      string `json:"spotUplRatio"`
	TotalPnl          string `json:"totalPnl"`
	TotalPnlRatio     string `json:"totalPnlRatio"`
}

// 账户余额响应
type BrokerInfoResponse struct {
	Equity         string `json:"accountEquity"`
	Available      string `json:"totalAvailableBalance"`
	MarginOpenLoss string `json:"totalMarginOpenLoss"`
}

// 账户配置响应
type AccountConfigResponse struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		AccountLevel string `json:"acctLv"`
	} `json:"data"`
}

// 交易对信息结构体
type FuturesExchangeInfoResponse struct {
	Code string         `json:"code"`
	Msg  string         `json:"msg"`
	Data []ExchangeInfo `json:"data"`
}

// Instrument 结构体表示返回数据中的每个交易品种
type ExchangeInfo struct {
	Alias          string `json:"alias"`
	AuctionEndTime string `json:"auctionEndTime"`
	BaseCcy        string `json:"baseCcy"`
	Category       string `json:"category"`
	CtMult         string `json:"ctMult"`
	CtType         string `json:"ctType"`
	CtVal          string `json:"ctVal"`
	CtValCcy       string `json:"ctValCcy"`
	ExpTime        string `json:"expTime"`
	InstFamily     string `json:"instFamily"`
	InstId         string `json:"instId"`
	InstType       string `json:"instType"`
	Lever          string `json:"lever"`
	ListTime       string `json:"listTime"`
	LotSz          string `json:"lotSz"`
	MaxIcebergSz   string `json:"maxIcebergSz"`
	MaxLmtAmt      string `json:"maxLmtAmt"`
	MaxLmtSz       string `json:"maxLmtSz"`
	MaxMktAmt      string `json:"maxMktAmt"`
	MaxMktSz       string `json:"maxMktSz"`
	MaxStopSz      string `json:"maxStopSz"`
	MaxTriggerSz   string `json:"maxTriggerSz"`
	MaxTwapSz      string `json:"maxTwapSz"`
	MinSz          string `json:"minSz"`
	OptType        string `json:"optType"`
	QuoteCcy       string `json:"quoteCcy"`
	SettleCcy      string `json:"settleCcy"`
	State          string `json:"state"`
	RuleType       string `json:"ruleType"`
	Stk            string `json:"stk"`
	TickSz         string `json:"tickSz"`
	Uly            string `json:"uly"`
}

type SpotExchangeInfoResponse FuturesExchangeInfoResponse

type FilterType string

const (
	PRICE_FILTER        FilterType = "PRICE_FILTER"
	LOT_SIZE            FilterType = "LOT_SIZE"
	MARKET_LOT_SIZE     FilterType = "MARKET_LOT_SIZE"
	MAX_NUM_ORDERS      FilterType = "MAX_NUM_ORDERS"
	MAX_NUM_ALGO_ORDERS FilterType = "MAX_NUM_ALGO_ORDERS"
	MIN_NOTIONAL        FilterType = "MIN_NOTIONAL"
	NOTIONAL            FilterType = "NOTIONAL"
	PERCENT_PRICE       FilterType = "PERCENT_PRICE"
)

// 交易规则
type ExchangeFilter struct {
	FilterType  string `json:"filterType"`
	MinPrice    string `json:"minPrice,omitempty"`
	MaxPrice    string `json:"maxPrice,omitempty"`
	TickSize    string `json:"tickSize,omitempty"`
	MinQty      string `json:"minQty,omitempty"`
	MaxQty      string `json:"maxQty,omitempty"`
	StepSize    string `json:"stepSize,omitempty"`
	Notional    string `json:"notional,omitempty"`
	MinNotional string `json:"minNotional,omitempty"`
}

type FuturesFundingResponse struct {
	Code string        `json:"code"`
	Msg  string        `json:"msg"`
	Data []FundingData `json:"data"`
}

type FuturesFundingHistResponse struct {
	Code string        `json:"code"`
	Msg  string        `json:"msg"`
	Data []FundingData `json:"data"`
}

// FundingData 结构体表示每个资金信息的详细数据
type FundingData struct {
	FundingRate     string `json:"fundingRate"`
	FundingTime     string `json:"fundingTime"`
	InstId          string `json:"instId"`
	InstType        string `json:"instType"`
	Method          string `json:"method"`
	MaxFundingRate  string `json:"maxFundingRate"`
	MinFundingRate  string `json:"minFundingRate"`
	NextFundingRate string `json:"nextFundingRate"`
	NextFundingTime string `json:"nextFundingTime"`
	Premium         string `json:"premium"`
	SettFundingRate string `json:"settFundingRate"`
	SettState       string `json:"settState"`
	Ts              string `json:"ts"`
}

// 期货交易记录响应
type UmTradeResponse struct {
	Symbol          string `json:"symbol"`
	Price           string `json:"price"`
	Size            string `json:"qty"`
	Time            int64  `json:"time"`
	Commission      string `json:"commission"`
	CommissionAsset string `json:"commissionAsset"`
	IsMaker         bool   `json:"maker"`
	IsBuy           bool   `json:"buyer"`
	PosSide         string `json:"positionSide"`
	Pnl             string `json:"realizedPnl"`
}

// 期货交易记录响应
type UmTradesResponse []UmTradeResponse

// 期货挂单查询响应
type FuturesPendingResponse struct {
	Code string          `json:"code"`
	Data []OrderResponse `json:"data"`
	Msg  string          `json:"msg"`
}
type MarginPendingResponse FuturesPendingResponse

// 现货挂单查询响应
type BrokerMarginPendingResponse []OrderResponse

// 订单信息结构体
type OrderResponse struct {
	AccFillSz          string        `json:"accFillSz"`
	AlgoClOrdId        string        `json:"algoClOrdId"`
	AlgoId             string        `json:"algoId"`
	AttachAlgoClOrdId  string        `json:"attachAlgoClOrdId"`
	AttachAlgoOrds     []any         `json:"attachAlgoOrds"`
	AvgPx              string        `json:"avgPx"`
	CTime              string        `json:"cTime"`
	CancelSource       string        `json:"cancelSource"`
	CancelSourceReason string        `json:"cancelSourceReason"`
	Category           string        `json:"category"`
	Ccy                string        `json:"ccy"`
	ClOrdId            string        `json:"clOrdId"`
	Fee                string        `json:"fee"`
	FeeCcy             string        `json:"feeCcy"`
	FillPx             string        `json:"fillPx"`
	FillSz             string        `json:"fillSz"`
	FillTime           string        `json:"fillTime"`
	InstId             string        `json:"instId"`
	InstType           string        `json:"instType"`
	IsTpLimit          string        `json:"isTpLimit"`
	Lever              string        `json:"lever"`
	LinkedAlgoOrd      LinkedAlgoOrd `json:"linkedAlgoOrd"`
	OrdId              string        `json:"ordId"`
	OrdType            string        `json:"ordType"`
	Pnl                string        `json:"pnl"`
	PosSide            string        `json:"posSide"`
	Px                 string        `json:"px"`
	PxType             string        `json:"pxType"`
	PxUsd              string        `json:"pxUsd"`
	PxVol              string        `json:"pxVol"`
	QuickMgnType       string        `json:"quickMgnType"`
	Rebate             string        `json:"rebate"`
	RebateCcy          string        `json:"rebateCcy"`
	ReduceOnly         string        `json:"reduceOnly"`
	Side               string        `json:"side"`
	SlOrdPx            string        `json:"slOrdPx"`
	SlTriggerPx        string        `json:"slTriggerPx"`
	SlTriggerPxType    string        `json:"slTriggerPxType"`
	Source             string        `json:"source"`
	State              string        `json:"state"`
	StpId              string        `json:"stpId"`
	StpMode            string        `json:"stpMode"`
	Sz                 string        `json:"sz"`
	Tag                string        `json:"tag"`
	TdMode             string        `json:"tdMode"`
	TgtCcy             string        `json:"tgtCcy"`
	TpOrdPx            string        `json:"tpOrdPx"`
	TpTriggerPx        string        `json:"tpTriggerPx"`
	TpTriggerPxType    string        `json:"tpTriggerPxType"`
	TradeId            string        `json:"tradeId"`
	UTime              string        `json:"uTime"`
}

// LinkedAlgoOrd 部分的结构体
type LinkedAlgoOrd struct {
	AlgoId string `json:"algoId"`
}

// 定义结构体
type Position struct {
	Ccy     string `json:"ccy"`
	InstId  string `json:"instId"`
	MgnMode string `json:"mgnMode"`
	PosSide string `json:"posSide"`
	Lever   string `json:"lever"`
}

// 期货交易对配置响应
type BrokerUMConfigResponse struct {
	Code string     `json:"code"`
	Msg  string     `json:"msg"`
	Data []Position `json:"data"`
}

// 期货价格查询
type KLineResponce []struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		Price string `json:"c"`
	} `json:"data"`
}

// 期货持仓查询
type PositionsResponse struct {
	Code string `json:"code"`
	Data []struct {
		InstType string `json:"instType"`
		InstId   string `json:"instId"`
		PosSide  string `json:"posSide"`
		Pos      string `json:"pos"`
		AvgPx    string `json:"avgPx"`
	} `json:"data"`
}

// 仓位档位查询
type PositionTiersResponse struct {
	Code string `json:"code"`
	Data []struct {
		InstId     string `json:"instId"`
		InstFamily string `json:"instFamily"`
		MaxLever   string `json:"maxLever"`
		MaxSz      string `json:"maxSz"`
		MinSz      string `json:"minSz"`
		MMR        string `json:"mmr"`
		Tier       string `json:"tier"`
	} `json:"data"`
}

// 手续费查询
type CommissionRateResponse struct {
	Code string `json:"code"`
	Data []struct {
		InstType string `json:"instType"`
		Maker    string `json:"maker"`
		Taker    string `json:"taker"`
		MakerU   string `json:"makerU"`
		TakerU   string `json:"takerU"`
	} `json:"data"`
}

// Kline
type KLineResponse struct {
	Code string     `json:"code"`
	Msg  string     `json:"msg"`
	Data [][]string `json:"data"`
}

type OpenInterestResponse struct {
	Code string `json:"code"`
	Data []struct {
		InstId     string `json:"instId"`
		OIContract string `json:"oi"`
		OICcy      string `json:"oiCcy"`
		OIUsd      string `json:"oiUsd"`
		Ts         string `json:"ts"`
	} `json:"data"`
}

func (r BalanceResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range r.Data[0].Details {
		if _, exists := b.AssetWallets[item.Ccy]; !exists {
			b.AssetWallets[item.Ccy] = &broker.AssetWallet{}
		}
		b.AssetWallets[item.Ccy].Asset = item.Ccy
		b.AssetWallets[item.Ccy].Free, _ = strconv.ParseFloat(item.AvailBal, 64)
		b.AssetWallets[item.Ccy].Locked, _ = strconv.ParseFloat(item.FrozenBal, 64)
		if item.Ccy == "USDT" {
			b.Account.Equity, _ = strconv.ParseFloat(item.AvailEq, 64)
			b.Account.Available, _ = strconv.ParseFloat(item.AvailBal, 64)
			b.Account.MarginOpenLoss, _ = strconv.ParseFloat(item.FrozenBal, 64)
		}
	}
}

func (r FuturesPendingResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range r.Data {
		// 更新订单状态
		// ClOrdId可能为空, 此时用OrderId作为key
		ordId := item.ClOrdId
		if ordId == "" {
			ordId = item.OrdId
		}
		symbol := okUtils.InstIdFuturesToSymbol(item.InstId)

		ctVal := b.CtVals[symbol]
		o := b.FuturesPending.Pending[ordId]

		if o == nil {
			o = &order.FuturesOrder{
				OrdId:   item.OrdId,
				ClOrdId: item.ClOrdId,
				Symbol:  symbol,
				Side:    okUtils.UnmarshalSide(item.Side),
				PosSide: okUtils.UnmarshalPosSide(item.PosSide),
			}
			o.OrdType, o.TimeInForce = okUtils.UnmarshalOrdType(item.OrdType)
			b.FuturesPending.Pending[ordId] = o
		}
		o.Status = okUtils.UnmarshalOrdStatus(item.State)
		o.ExecutedSize, _ = strconv.ParseFloat(item.AccFillSz, 64)
		o.ExecutedSize = o.ExecutedSize * ctVal
		o.AvgPrice, _ = strconv.ParseFloat(item.AvgPx, 64)
		o.Price, _ = strconv.ParseFloat(item.Px, 64)
		o.Size, _ = strconv.ParseFloat(item.Sz, 64)
		o.Size = o.Size * ctVal
	}
}
func (r MarginPendingResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range r.Data {
		// 更新订单状态
		// ClOrdId可能为空, 此时用OrderId作为key
		ordId := item.ClOrdId
		if ordId == "" {
			ordId = item.OrdId
		}
		symbol := okUtils.InstIdSpotToSymbol(item.InstId)
		o := b.MarginPending.Pending[ordId]
		if o == nil {
			o = &order.MarginOrder{
				OrdId:   item.OrdId,
				ClOrdId: item.ClOrdId,
				Symbol:  symbol,
				Side:    okUtils.UnmarshalSide(item.Side),
			}
			o.OrdType, o.TimeInForce = okUtils.UnmarshalOrdType(item.OrdType)
			b.MarginPending.Pending[ordId] = o
		}
		o.Status = okUtils.UnmarshalOrdStatus(item.State)
		o.ExecutedSize, _ = strconv.ParseFloat(item.AccFillSz, 64)
		o.Price, _ = strconv.ParseFloat(item.Px, 64)
		o.Size, _ = strconv.ParseFloat(item.Sz, 64)
	}
}

// 更新交易信息
func (r *FuturesExchangeInfoResponse) UpdateData(ds []*data.SymbolData) {
	// data list转换为map
	dataMap := make(map[string]*data.ExchangeInfo)
	for _, d := range ds {
		dataMap[d.Symbol] = d.Info
	}
	// 遍历filters更新data
	for _, info := range r.Data {
		d := dataMap[okUtils.InstIdFuturesToSymbol(info.InstId)]
		if d == nil {
			continue
		}
		d.CtVal = utils.StringToFloat64(info.CtVal)
		d.Status = data.ContractStatus(info.State)
		d.TickSize = utils.StringToFloat64(info.TickSz)
		d.StepSize = utils.StringToFloat64(info.LotSz)
		d.MinSize = utils.StringToFloat64(info.MinSz)
		d.QuantityPrecision = int(math.Log10(1 / d.StepSize))
		d.PricePrecision = int(math.Log10(1 / d.TickSize))
	}
}

// 更新张币信息
func (r *FuturesExchangeInfoResponse) UpdateBroker(b *broker.Broker) {
	for _, info := range r.Data {
		if info.InstType == "SWAP" {
			symbol := okUtils.InstIdFuturesToSymbol(info.InstId)
			ctVal := utils.StringToFloat64(info.CtVal)
			b.CtVals[symbol] = ctVal
		}
	}
}

func (rs *SpotExchangeInfoResponse) UpdateData(ds []*data.SymbolData) {
	// data list转换为map
	dataMap := make(map[string]*data.ExchangeInfo)
	for _, d := range ds {
		dataMap[d.Symbol] = d.Info
	}
	// 遍历filters更新data
	for _, info := range rs.Data {
		d := dataMap[okUtils.InstIdSpotToSymbol(info.InstId)]
		if d == nil {
			continue
		}
		d.Status = data.ContractStatus(info.State)
		d.TickSize = utils.StringToFloat64(info.TickSz)
		d.StepSize = utils.StringToFloat64(info.LotSz)
		d.MinSize = utils.StringToFloat64(info.MinSz)
		d.QuantityPrecision = int(math.Log10(1 / d.StepSize))
		d.PricePrecision = int(math.Log10(1 / d.TickSize))
	}
}

func (rs FuturesFundingResponse) UpdateData(ds []*data.SymbolData) {
	dsMap := make(map[string]*data.SymbolData)
	for _, d := range ds {
		dsMap[okUtils.SymbolToInstIdFutures(d.Symbol)] = d
	}
	for _, r := range rs.Data {
		d := dsMap[r.InstId]
		if d == nil {
			continue
		}
		d.Funding.Rate, _ = strconv.ParseFloat(r.FundingRate, 64)
		d.Funding.Time, _ = strconv.ParseInt(r.FundingTime, 10, 64)
		d.Funding.Cap, _ = strconv.ParseFloat(r.MaxFundingRate, 64)
		d.Funding.Floor, _ = strconv.ParseFloat(r.MinFundingRate, 64)
		// 假设收取本轮收取周期等于次轮收取周期
		nextFundingTime, _ := strconv.ParseInt(r.NextFundingTime, 10, 64)
		d.Funding.PrevTime = d.Funding.Time - (nextFundingTime - d.Funding.Time)
	}
}

func (rs FuturesFundingHistResponse) UpdateData(d *data.SymbolData) {
	if len(rs.Data) == 0 {
		return
	}
	d.Funding.PrevTime, _ = strconv.ParseInt(rs.Data[0].FundingTime, 10, 64)
}

func (rs *BrokerUMConfigResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range rs.Data {
		b.Leverages[okUtils.InstIdFuturesToSymbol(item.InstId)], _ = strconv.ParseInt(item.Lever, 10, 64)
	}
}

func (rs *PositionsResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	for _, item := range rs.Data {
		// 只更新期货
		if item.InstType != "SWAP" {
			continue
		}
		symbol := okUtils.InstIdFuturesToSymbol(item.InstId)
		if _, exists := b.Positions[symbol]; !exists {
			b.Positions[symbol] = &broker.UMPosition{Symbol: symbol}
		}
		posObj := b.Positions[symbol]
		ctval := b.CtVals[symbol]
		switch item.PosSide {
		case "long":
			posObj.SizeLong, _ = strconv.ParseFloat(item.Pos, 64)
		case "short":
			posObj.SizeShort, _ = strconv.ParseFloat(item.Pos, 64)
			posObj.SizeShort *= -1
		case "both":
			posObj.SizeLong, _ = strconv.ParseFloat(item.Pos, 64)
			posObj.SizeShort, _ = strconv.ParseFloat(item.Pos, 64)
			posObj.SizeShort *= -1
			posObj.SizeLong = max(posObj.SizeLong, 0)
			posObj.SizeShort = min(posObj.SizeShort, 0)
		}
		// 张币转换
		posObj.SizeLong *= ctval
		posObj.SizeShort *= ctval

		// 开仓价格调整
		if posObj.SizeLong == 0 {
			posObj.OpenPriceLong = 0
		} else {
			posObj.OpenPriceLong = utils.StringToFloat64(item.AvgPx)
		}
		if posObj.SizeShort == 0 {
			posObj.OpenPriceShort = 0
		} else {
			posObj.OpenPriceShort = utils.StringToFloat64(item.AvgPx)
		}
	}
}

func (rs *PositionTiersResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	errs := make([]error, 0)
	// // 只保存前10档位
	// length := min(len(rs.Data), 10)
	for _, item := range rs.Data {
		symbol := okUtils.InstFamilyToSymbol(item.InstFamily)
		// 获取张币比值
		ctVal := 1.0
		if _, ok := b.CtVals[symbol]; ok {
			ctVal = b.CtVals[symbol]
		}
		tierObj := &broker.PositionTier{}
		tierObj.Tier = utils.MustParseInt64(item.Tier, &errs)
		tierObj.MaxSize = utils.MustParseFloat64(item.MaxSz, &errs) * ctVal
		tierObj.MinSize = utils.MustParseFloat64(item.MinSz, &errs) * ctVal
		tierObj.MaxNotional = math.Inf(1)
		tierObj.MinNotional = math.Inf(-1)
		tierObj.MaintMarginRatio = utils.MustParseFloat64(item.MMR, &errs)
		tierObj.InitialLeverage = utils.MustParseFloat64(item.MaxLever, &errs)
		tierObj.Cum = 0
		// if tierObj.Tier == 1 {
		// 	b.PositionTiers[symbol] = []broker.PositionTier{}
		// 	tierObj.Cum = 0
		// } else {
		// 	prevTier := b.PositionTiers[symbol][tierObj.Tier-2]
		// 	tierObj.Cum = prevTier.Cum + (tierObj.MaintMarginRatio-prevTier.MaintMarginRatio)*prevTier.MaxSize + prevTier.Cum
		// }
		b.PositionTiers[symbol] = append(b.PositionTiers[symbol], *tierObj)
	}
}

func (rs *CommissionRateResponse) UpdateBroker(b *broker.Broker) {
	b.Lock()
	defer b.Unlock()
	if rs.Data[0].InstType == "SWAP" {
		b.FuturesCommission = broker.Commission{
			Maker: math.Abs(utils.StringToFloat64(rs.Data[0].MakerU)),
			Taker: math.Abs(utils.StringToFloat64(rs.Data[0].TakerU)),
		}
	}
	if rs.Data[0].InstType == "SPOT" {
		b.SpotCommission = broker.Commission{
			Maker: math.Abs(utils.StringToFloat64(rs.Data[0].Maker)),
			Taker: math.Abs(utils.StringToFloat64(rs.Data[0].Taker)),
		}
	}
}
