package ok

import (
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/handler"
	"context"
	"log/slog"
)

var _ client.DataClient = (*OkCombinedDataClient)(nil) // 确保实现了DataClient接口

// 欧意组合数据源客户端
// 区分单独的期货数据源
type OkCombinedDataClient struct {
	dataClient       client.DataClient
	futureOnlyClient client.DataClient
}

func NewCombinedDataClient(d client.DataClient, fd client.DataClient) *OkCombinedDataClient {
	c := &OkCombinedDataClient{
		dataClient:       d,
		futureOnlyClient: fd,
	}
	return c
}

// 启动数据源
func (c *OkCombinedDataClient) Run(ctx context.Context) (err error) {
	err = c.dataClient.Run(ctx)
	if err != nil {
		return err
	}
	err = c.futureOnlyClient.Run(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (c *OkCombinedDataClient) AddStreamHandler(event handler.WSStreamType, handler func([]byte, *data.SymbolData, *slog.Logger) error) client.DataClient {
	c.dataClient.AddStreamHandler(event, handler)
	c.futureOnlyClient.AddStreamHandler(event, handler)
	return c
}

// 订阅数据源
func (c *OkCombinedDataClient) SubscribeDatas(ds []*data.SymbolData) (err error) {
	fds := []*data.SymbolData{}
	for _, d := range ds {
		if d.DataType == data.Futures {
			fds = append(fds, d)
		}
	}
	err = c.dataClient.SubscribeDatas(ds)
	if err != nil {
		return err
	}
	err = c.futureOnlyClient.SubscribeDatas(fds)
	if err != nil {
		return err
	}
	return nil
}

// 取消订阅数据
func (c *OkCombinedDataClient) UnsubscribeDatas(ds []*data.SymbolData) (err error) {
	fds := []*data.SymbolData{}
	for _, d := range ds {
		if d.DataType == data.Futures {
			fds = append(fds, d)
		}
	}
	err = c.dataClient.UnsubscribeDatas(ds)
	if err != nil {
		return err
	}
	err = c.futureOnlyClient.UnsubscribeDatas(fds)
	if err != nil {
		return err
	}
	return nil
}
