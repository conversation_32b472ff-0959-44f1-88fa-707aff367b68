package ok

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/handler"
	ws "GoTrader/pkg/wsclient"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"github.com/gorilla/websocket"
)

var _ client.BrokerClient = (*OkBrokerClient)(nil) // 确保实现了DataClient接口

// 用于监听broker的client
type OkBrokerClient struct {
	url           string                                                                     // ws监听 url
	ws            ws.WSManager                                                               // ws 监听连接实例
	broker        *broker.Broker                                                             // 统一账户对象
	eventHandlers map[handler.WSEventType][]func([]byte, *broker.Broker, *slog.Logger) error // 事件处理函数, map[事件类型]处理函数
	logger        *slog.Logger                                                               // 日志对象
}

// 创建客户端
func NewBrokerClient(url string, wsManager ws.WSManager, logger *slog.Logger) *OkBrokerClient {
	logger = logger.With("client", "okBroker")
	c := &OkBrokerClient{
		url:           url,
		ws:            wsManager,
		broker:        nil,
		eventHandlers: map[handler.WSEventType][]func([]byte, *broker.Broker, *slog.Logger) error{},
		logger:        logger,
	}
	wsManager.SetReInitFunc(c.reInit)
	wsManager.SetCheckFunc(c.subscribeChannels)
	return c
}

// 注册Broker事件处理函数
func (c *OkBrokerClient) AddEventHandler(event handler.WSEventType, handler func([]byte, *broker.Broker, *slog.Logger) error) client.BrokerClient {
	c.eventHandlers[event] = append(c.eventHandlers[event], handler)
	return c
}

// 添加broker
func (c *OkBrokerClient) AddBroker(broker *broker.Broker) client.BrokerClient {
	if c.broker != nil {
		c.logger.Warn("broker is not empty, skip add broker")
		return c
	}
	c.broker = broker
	return c
}

// 启动broker数据源
func (c *OkBrokerClient) Run(ctx context.Context) (err error) {
	err = c.connect(ctx)
	if err != nil {
		c.logger.Error("error connect ws", "error", err)
		return err
	}
	return nil
}

func (c *OkBrokerClient) ReconnectManual() error {
	return c.ws.ReconnectManual()
}

// 连接broker数据源
func (c *OkBrokerClient) connect(ctx context.Context) error {
	if c.broker == nil {
		c.logger.Error("Broker is empty, skip connect")
		return errors.New("broker is empty")
	}
	// 建立ws连接
	err := c.ws.Connect(ctx, c.url, c.logger)
	if err != nil {
		c.logger.Error("error connect broker ws", "error", err)
		return err
	}
	// 打印连接成功
	c.logger.Info("Connected to Broker ws")

	c.listen(ctx)

	ver := c.ws.GetVersion()

	//登录认证
	err = c.login(ctx)
	if err != nil {
		c.logger.Error("login error", "error", err)
		return err
	}

	//订阅交易，余额与持仓频道
	err = c.subscribeChannels(ctx, ver)
	if err != nil {
		c.logger.Error("subscribe error", "error", err)
		return err
	}
	return nil
}

// 开始监听broker
func (c *OkBrokerClient) listen(ctx context.Context) {
	// 在单独的 goroutine 中接收 WebSocket 消息
	c.logger.Info("broker ws 开始监听")
	go func() {
		for {
			// 读取 WebSocket 消息
			_, msg, err := c.ws.ReadMessage()
			if err != nil {
				if ctx.Err() != nil {
					c.logger.Info("broker ws ctx 关闭")
					return
				}
				if errors.Is(err, ws.ErrReconnected) {
					// 重连成功
					c.logger.Info("broker ws reconnect success")
					continue
				}
				c.logger.Error("broker ws 接收消息失败", "error", err)
				continue
			}
			if string(msg) == "pong" {
				continue
			}
			// 处理 WebSocket 事件
			var WebSocketEvent BrokerWebSocketEventOk
			err = json.Unmarshal(msg, &WebSocketEvent)
			if err != nil {
				c.logger.Error("Error unmarshalling JSON", "error", err, "msg", string(msg))
				continue
			}

			switch WebSocketEvent.Event {
			case "error":
				c.logger.Error("broker ws 订阅失败", "errorMsg", WebSocketEvent.Msg)
				return
			case "channel-conn-count-error":
				c.logger.Error("broker ws 订阅超出数量限制")
				return
			case "channel-conn-count":
				c.logger.Info("broker ws 订阅数量同步")
				continue
			case "subscribe":
				c.logger.Info("broker ws 订阅成功", "channel", WebSocketEvent.Arg.Channel)
				continue
			case "login":
				c.logger.Info("broker ws 登录成功")
				continue
			case "notice":
				c.logger.Info("broker ws 通知", "notice", WebSocketEvent.Msg)
				if WebSocketEvent.Msg == "The connection will soon be closed for a service upgrade. Please reconnect." {
					// 重连
					c.ws.Close()
					continue
				}
			default:
				// 执行事件处理函数
				event := handler.WSEventType(WebSocketEvent.Arg.Channel)
				if handlers, ok := c.eventHandlers[event]; ok {
					for _, handler := range handlers {
						handler(msg, c.broker, c.logger)
					}
					continue
				}
			}

			c.logger.Info("Unknown event", "event", WebSocketEvent.Event, "message", string(msg))
		}
	}()
}

// Authenticate 认证
func (c *OkBrokerClient) brokerAuthenticate() error {
	timestamp := fmt.Sprintf("%.3f", float64(time.Now().UnixNano())/1e9)
	payload := fmt.Sprintf("%s%s%s", timestamp, "GET", "/users/self/verify")
	sign := c.generateOkSign(payload, c.broker.Secret)
	args := map[string]string{
		"apiKey":     c.broker.ApiKey,
		"passphrase": c.broker.PassPhrase,
		"timestamp":  timestamp,
		"sign":       sign,
	}
	msg := map[string]any{
		"op":   "login",
		"args": []any{args},
	}
	authMsg, _ := json.Marshal(msg)
	if err := c.ws.WriteMessage(websocket.TextMessage, authMsg); err != nil {
		return fmt.Errorf("failed to send authentication message: %w", err)
	}
	return nil
}

// OKX签名生成函数
func (c *OkBrokerClient) generateOkSign(signString, secret string) string {
	// 使用HMAC SHA256加密
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(signString))
	// Base64编码输出
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

// 重新初始化订阅
func (c *OkBrokerClient) reInit(ctx context.Context, ver int64) error {
	c.logger.Info("broker ws 重新初始化订阅")
	err := c.login(ctx)
	if err != nil {
		c.logger.Error("login error", "error", err)
		return err
	}
	c.logger.Info("broker ws 登录成功, 开始订阅频道")

	//订阅交易，余额与持仓频道
	err = c.subscribeChannels(ctx, ver)
	if err != nil {
		c.logger.Error("reSubscribe error", "error", err)
		return err
	}
	c.logger.Info("broker ws 重置成功")
	return nil
}

// 登录
func (c *OkBrokerClient) login(ctx context.Context) error {
	chanName := "login"
	c.ws.SubscribeReadChan(chanName)
	defer c.ws.UnsubscribeReadChan(chanName)

	// 登录认证
	err := c.brokerAuthenticate()
	if err != nil {
		c.logger.Error("Authenticate send error", "error", err)
		return err
	}
	// TODO 可能超时
	c.logger.Info("broker ws authenticate success, waiting for login response")

	loginEvent, loginMsg, err := getEventFromWS(ctx, c.ws, chanName, "login", c.logger)
	if err != nil {
		c.logger.Error("Error reading Broker WebSocket message", "error", err)
		return err
	}

	if loginEvent.Code != "0" {
		c.logger.Error("okex broker ws login error", "error", err, "message", loginMsg)
		return err
	}
	c.logger.Info("broker ws login success", "message", loginEvent)
	return nil
}

// 订阅频道
func (c *OkBrokerClient) subscribeChannels(ctx context.Context, ver int64) error {
	chanName := "subscribe"
	c.ws.SubscribeReadChan(chanName)
	defer c.ws.UnsubscribeReadChan(chanName)
	//订阅交易，余额与持仓频道
	params := []data.ArgItemPrivate{}
	itemPos := data.ArgItemPrivate{
		Channel: "balance_and_position",
	}
	itemAcc := data.ArgItemPrivate{
		Channel: "account",
	}
	itemOrdersFutures := data.ArgItemPrivate{
		Channel:  "orders",
		InstType: "SWAP",
	}
	itemOrdersSpot := data.ArgItemPrivate{
		Channel:  "orders",
		InstType: "SPOT",
	}
	params = append(params, itemPos, itemAcc, itemOrdersFutures, itemOrdersSpot)
	aggSubscribeRequest := data.SubscribeRequestPrivateOk{
		Method: "subscribe",
		Params: params,
	}
	var subscribeMessage []byte
	subscribeMessage, _ = json.Marshal(aggSubscribeRequest)

	// 验证版本号
	if !c.ws.IsVersionValid(ver) {
		c.logger.Info("ws version changed, stop write ws channel message")
		return fmt.Errorf("ws version changed")
	}
	err := c.ws.WriteMessage(websocket.TextMessage, []byte(subscribeMessage))
	if err != nil {
		c.logger.Error("订阅频道失败", "error", err)
		return err
	}

	c.logger.Info("订阅频道成功, 等待订阅响应")
	// 检查最近的4次订阅响应是否包含了4次订阅频道
	for range 4 {
		_, _, err = getEventFromWS(ctx, c.ws, chanName, "subscribe", c.logger)
		if err != nil {
			c.logger.Error("订阅响应检查失败", "error", err)
			return err
		}
	}

	c.logger.Info("broker ws 订阅检查完成")
	return nil
}

// 获取指定WebSocket事件
func getEventFromWS(ctx context.Context, ws ws.WSManager, wsChannel string, event string, logger *slog.Logger) (*BrokerWebSocketEventOk, []byte, error) {
	WebSocketEvent := &BrokerWebSocketEventOk{}
	for {
		// 读取 WebSocket 消息
		_, msg, err := ws.ReadFromChan(ctx, wsChannel)
		if err != nil {
			logger.Error("Error reading WebSocket message", "error", err)
			return WebSocketEvent, msg, err
		}
		err = json.Unmarshal(msg, WebSocketEvent)
		if err != nil {
			logger.Error("Error unmarshalling JSON", "error", err, "message", string(msg))
			continue
		}
		if WebSocketEvent.Event == event {
			return WebSocketEvent, msg, nil
		}
		logger.Info("Waiting for correct event", "currEvent", WebSocketEvent.Event, "expectEvent", event, "message", string(msg))
	}
}
