// Code generated by MockGen. DO NOT EDIT.
// Source: ./pkg/websocket/connector.go
//
// Generated by this command:
//
//	mockgen -source=./pkg/websocket/connector.go -destination=./pkg/websocket/mock.go
//

// Package mock_websocket is a generated GoMock package.
package mock

import (
	context "context"
	slog "log/slog"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockWSManager is a mock of WSManager interface.
type MockWSManager struct {
	ctrl     *gomock.Controller
	recorder *MockWSManagerMockRecorder
	isgomock struct{}
}

// MockWSManagerMockRecorder is the mock recorder for MockWSManager.
type MockWSManagerMockRecorder struct {
	mock *MockWSManager
}

// NewMockWSManager creates a new mock instance.
func NewMockWSManager(ctrl *gomock.Controller) *MockWSManager {
	mock := &MockWSManager{ctrl: ctrl}
	mock.recorder = &MockWSManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWSManager) EXPECT() *MockWSManagerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockWSManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockWSManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockWSManager)(nil).Close))
}

// Connect mocks base method.
func (m *MockWSManager) Connect(ctx context.Context, url string, logger *slog.Logger) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Connect", ctx, url, logger)
	ret0, _ := ret[0].(error)
	return ret0
}

// Connect indicates an expected call of Connect.
func (mr *MockWSManagerMockRecorder) Connect(ctx, url, logger any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Connect", reflect.TypeOf((*MockWSManager)(nil).Connect), ctx, url, logger)
}

// ReadMessage mocks base method.
func (m *MockWSManager) ReadMessage() (int, []byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadMessage")
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].([]byte)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ReadMessage indicates an expected call of ReadMessage.
func (mr *MockWSManagerMockRecorder) ReadMessage() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadMessage", reflect.TypeOf((*MockWSManager)(nil).ReadMessage))
}

// WriteMessage mocks base method.
func (m *MockWSManager) WriteMessage(arg0 int, arg1 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WriteMessage", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// WriteMessage indicates an expected call of WriteMessage.
func (mr *MockWSManagerMockRecorder) WriteMessage(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WriteMessage", reflect.TypeOf((*MockWSManager)(nil).WriteMessage), arg0, arg1)
}
