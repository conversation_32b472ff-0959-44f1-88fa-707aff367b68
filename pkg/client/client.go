package client

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"GoTrader/pkg/handler"
	"GoTrader/pkg/order"
	"context"
	"log/slog"
)

// Data WebSocket 客户端
type DataClient interface {
	AddStreamHandler(event handler.WSStreamType, handler func([]byte, *data.SymbolData, *slog.Logger) error) DataClient // 添加数据事件处理函数
	Run(ctx context.Context) error                                                                                      // 启动数据源
	SubscribeDatas(ds []*data.SymbolData) error                                                                         // 订阅数据
	UnsubscribeDatas(ds []*data.SymbolData) error                                                                       // 取消订阅数据
}

// 账户 WebSocket 客户端
type BrokerClient interface {
	AddEventHandler(event handler.WSEventType, handler func([]byte, *broker.Broker, *slog.Logger) error) BrokerClient // 添加Broker事件处理函数
	AddBroker(broker *broker.Broker) BrokerClient                                                                     // 添加broker
	Run(ctx context.Context) error                                                                                    // 启动数据源
}

// API 客户端
type APIClient interface {
	SubmitFuturesOrder(b *broker.Broker, o *order.FuturesOrder, info *data.ExchangeInfo) (OrderViewer, error) // 提交期货订单
	SubmitMarginOrder(b *broker.Broker, o *order.MarginOrder, info *data.ExchangeInfo) (OrderViewer, error)   // 提交杠杆订单
	ModifyFuturesOrder(b *broker.Broker, o order.FuturesOrder, info *data.ExchangeInfo) error                 // 修改期货订单
	CancelAllFutureOrders(b *broker.Broker, d *data.SymbolData) error                                         // 取消所有期货订单
	CancelAllMarginOrders(b *broker.Broker, d *data.SymbolData) error                                         // 取消所有杠杆订单
	CancelFuturesOrder(b *broker.Broker, o *order.FuturesOrder) error                                         // 取消期货订单
	CancelMarginOrder(b *broker.Broker, o *order.MarginOrder) error                                           // 取消杠杆订单
	CloseFutures(b *broker.Broker, d *data.SymbolData) error                                                  // 期货平仓
	CloseMargin(b *broker.Broker, d *data.SymbolData) error                                                   // 杠杆平仓
	UpdateBroker(b *broker.Broker) error                                                                      // 更新broker
	UpdateBrokerUMPending(b *broker.Broker, ds []*data.SymbolData) error                                      // 更新broker期货挂单
	UpdateBrokerMarginPending(b *broker.Broker, ds []*data.SymbolData) error                                  // 更新broker杠杆挂单
	UpdateFuturesDatas(ds []*data.SymbolData) error                                                           // 更新期货数据
	UpdateSpotDatas(ds []*data.SymbolData) error                                                              // 更新现货数据
	UpdateFuturesLeverageSingle(b *broker.Broker, d *data.SymbolData) error                                   // 更新单币种杠杆
	UpdatePositionTiers(b *broker.Broker, d *data.SymbolData) error                                           // 更新期货仓位档位
	UpdateCtVal(b *broker.Broker, d *data.SymbolData) error                                                   // 更新张币系数
	ModifyFuturesLeverage(b *broker.Broker, d *data.SymbolData, lever int64) error                            // 修改期货杠杆
	QueryAllFunding() map[string]float64                                                                      // 查询期货所有资金费率
	QueryAllTradingMargin(b *broker.Broker) []string                                                          // 查询杠杆所有可交易品种
	QueryAllTradingFutures() []string                                                                         // 查询期货所有可交易品种
	UpdateDepth(d *data.SymbolData) error                                                                     // 更新深度
	Query24hrQuote(d *data.SymbolData) (float64, error)                                                       // 查询24h成交额
	QueryOpenInterest(d *data.SymbolData) (float64, error)                                                    // 查询持仓量
	QueryAccount(b *broker.Broker) (string, error)                                                            // 查询账户信息

	// 下列方法为通用方法衍生, 理论上拥有相同实现
	SubmitOrder(b *broker.Broker, o *order.Order, d *data.SymbolData) (OrderViewer, error) // 根据数据类型提交订单
	CancelAllOrders(b *broker.Broker, d *data.SymbolData) error                            // 根据数据类型取消订单
	Close(b *broker.Broker, d *data.SymbolData) error                                      // 根据数据类型平仓
	UpdateDatas(ds []*data.SymbolData) error                                               // 根据数据类型更新数据

}
