package utils

import "sync/atomic"

type BoolFlag struct {
	flag int32
}

func NewBoolFlag(trueOrFalse bool) *BoolFlag {
	if trueOrFalse {
		return &BoolFlag{flag: 1}
	}
	return &BoolFlag{}
}

func (b *BoolFlag) IsTrue() bool {
	return atomic.LoadInt32(&b.flag) == 1
}

func (b *BoolFlag) TrySetTrue() bool {
	return atomic.CompareAndSwapInt32(&b.flag, 0, 1)
}

func (b *BoolFlag) TrySetFalse() bool {
	return atomic.CompareAndSwapInt32(&b.flag, 1, 0)
}

func (b *BoolFlag) SetTrue() {
	atomic.StoreInt32(&b.flag, 1)
}

func (b *BoolFlag) SetFalse() {
	atomic.StoreInt32(&b.flag, 0)
}
