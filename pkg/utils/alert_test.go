package utils

import (
	"log/slog"
	"os"
	"testing"
	"time"
)

func TestBuildUsers(t *testing.T) {
	tests := []struct {
		name     string
		users    []AlertUser
		expected string
	}{
		{
			name:     "Empty users should return all",
			users:    []AlertUser{},
			expected: "all",
		},
		{
			name:     "Single user",
			users:    []AlertUser{UserLiquid},
			expected: "liquid",
		},
		{
			name:     "Multiple users",
			users:    []Alert<PERSON>ser{UserLiquid, UserJiaoyin, UserSmall},
			expected: "liquid_jiaoyin_small",
		},
		{
			name:     "All predefined users",
			users:    []AlertUser{UserJiaoyin, UserLiquid, UserTanli, UserSmall, User007},
			expected: "jiaoyin_liquid_tanli_small_007",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BuildUsers(tt.users...)
			if result != tt.expected {
				t.<PERSON>("BuildUsers() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestAlertRequest_Validation(t *testing.T) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	client := NewAlertClient(logger)

	tests := []struct {
		name    string
		req     *AlertRequest
		wantErr bool
	}{
		{
			name:    "Nil request should return error",
			req:     nil,
			wantErr: true,
		},
		{
			name: "Empty content should return error",
			req: &AlertRequest{
				Type:    AlertTypeVoice,
				User:    string(UserLiquid),
				Content: "",
				Channel: ChannelWarning,
			},
			wantErr: true,
		},
		{
			name: "Valid request should not return error",
			req: &AlertRequest{
				Type:    AlertTypeVoice,
				User:    string(UserLiquid),
				Content: "Test alert",
				Channel: ChannelWarning,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 注意：这里只测试参数验证，不实际发送请求
			// 实际发送会因为网络问题而失败，这是正常的
			_, err := client.SendAlert(tt.req)

			if tt.wantErr && err == nil {
				t.Errorf("SendAlert() should return error but got nil")
			}
			if !tt.wantErr && tt.req != nil && tt.req.Content != "" {
				// 对于有效请求，我们期望网络错误而不是参数验证错误
				// 所以这里不检查err是否为nil
			}
		})
	}
}

func TestAlertClient_DefaultValues(t *testing.T) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	_ = NewAlertClient(logger) // 创建客户端但不使用，避免未使用变量错误

	// 测试默认值设置
	req := &AlertRequest{
		Content: "Test alert with defaults",
		Channel: ChannelWarning,
		// Type 和 User 留空，应该设置默认值
	}

	// 由于我们不能实际发送请求，我们创建一个模拟的sendRequest方法来测试
	// 这里我们只验证默认值是否正确设置
	originalReq := *req

	// 模拟SendAlert中的默认值设置逻辑
	if req.Type == "" {
		req.Type = AlertTypeVoice
	}
	if req.User == "" {
		req.User = string(UserAll)
	}
	if req.TagTime == 0 && req.Tag != "" {
		req.TagTime = 3600
	}

	if req.Type != AlertTypeVoice {
		t.Errorf("Expected default type to be %v, got %v", AlertTypeVoice, req.Type)
	}
	if req.User != string(UserAll) {
		t.Errorf("Expected default user to be %v, got %v", string(UserAll), req.User)
	}

	// 验证原始请求没有被修改（除非我们明确修改）
	if originalReq.Content != req.Content {
		t.Errorf("Original content should not be modified")
	}
}

func TestAlertClient_ConvenienceMethods(t *testing.T) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	client := NewAlertClient(logger)

	// 测试便捷方法的参数构建
	testCases := []struct {
		name     string
		method   func() (*AlertResponse, error)
		expected AlertRequest
	}{
		{
			name: "SendErrorAlert",
			method: func() (*AlertResponse, error) {
				return client.SendErrorAlert("Error message", UserLiquid, UserJiaoyin)
			},
			expected: AlertRequest{
				Type:    AlertTypeVoice,
				User:    "liquid_jiaoyin",
				Content: "Error message",
				Channel: ChannelError,
			},
		},
		{
			name: "SendWarningAlert",
			method: func() (*AlertResponse, error) {
				return client.SendWarningAlert("Warning message", UserSmall)
			},
			expected: AlertRequest{
				Type:    AlertTypeVoice,
				User:    "small",
				Content: "Warning message",
				Channel: ChannelWarning,
			},
		},
		{
			name: "SendStrategyAlert",
			method: func() (*AlertResponse, error) {
				return client.SendStrategyAlert("Strategy message", UserAll)
			},
			expected: AlertRequest{
				Type:    AlertTypeVoice,
				User:    "all",
				Content: "Strategy message",
				Channel: ChannelStrategy,
			},
		},
		{
			name: "SendArbitrageAlert",
			method: func() (*AlertResponse, error) {
				return client.SendArbitrageAlert("Arbitrage message", User007)
			},
			expected: AlertRequest{
				Type:    AlertTypeVoice,
				User:    "007",
				Content: "Arbitrage message",
				Channel: ChannelArbitrage,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 由于我们不能实际发送请求，这里只测试方法不会panic
			// 实际的网络请求会失败，这是预期的
			_, err := tc.method()

			// 我们期望网络错误，而不是参数错误
			if err == nil {
				t.Logf("Method %s executed without error (unexpected but not necessarily wrong)", tc.name)
			}
		})
	}
}

func TestAlertClient_WithTimeout(t *testing.T) {
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	timeout := 5 * time.Second
	client := NewAlertClientWithTimeout(timeout, logger)

	if client.httpClient.Timeout != timeout {
		t.Errorf("Expected timeout to be %v, got %v", timeout, client.httpClient.Timeout)
	}
}

func TestAlertTypes(t *testing.T) {
	// 测试告警类型常量
	if AlertTypeVoice != "1" {
		t.Errorf("Expected AlertTypeVoice to be '1', got %v", AlertTypeVoice)
	}
	if AlertTypeSMS != "2" {
		t.Errorf("Expected AlertTypeSMS to be '2', got %v", AlertTypeSMS)
	}
}

func TestAlertUsers(t *testing.T) {
	// 测试用户常量
	expectedUsers := map[AlertUser]string{
		UserJiaoyin: "jiaoyin",
		UserLiquid:  "liquid",
		UserTanli:   "tanli",
		UserSmall:   "small",
		User007:     "007",
		UserAll:     "all",
	}

	for user, expected := range expectedUsers {
		if string(user) != expected {
			t.Errorf("Expected user %v to be %v, got %v", user, expected, string(user))
		}
	}
}

func TestAlertChannels(t *testing.T) {
	// 测试频道常量
	expectedChannels := map[AlertChannel]string{
		ChannelWarning:   "warning",
		ChannelError:     "error",
		ChannelStrategy:  "strategy",
		ChannelArbitrage: "arbitrage",
	}

	for channel, expected := range expectedChannels {
		if string(channel) != expected {
			t.Errorf("Expected channel %v to be %v, got %v", channel, expected, string(channel))
		}
	}
}

// 基准测试
func BenchmarkBuildUsers(b *testing.B) {
	users := []AlertUser{UserLiquid, UserJiaoyin, UserSmall, UserTanli, User007}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		BuildUsers(users...)
	}
}

func BenchmarkAlertRequestCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := &AlertRequest{
			Type:    AlertTypeVoice,
			User:    BuildUsers(UserLiquid, UserJiaoyin),
			Content: "Benchmark test alert",
			Channel: ChannelWarning,
			Tag:     "benchmark_tag",
			TagTime: 3600,
		}
		_ = req
	}
}
