package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"strconv"
	"time"
)

// struct转换为map
func StructToMap(data any) (map[string]any, error) {
	// 第一步：将结构体转换为 JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		log.Println("Error marshalling struct:", err)
		return nil, err
	}
	// 第二步：将 JSON 数据转换为 map[string]any
	var result map[string]any
	err = json.Unmarshal(jsonData, &result)
	if err != nil {
		log.Println("Error unmarshalling JSON:", err)
		return nil, err
	}
	return result, nil
}

// url合并
func URLJoin(base, relative string) string {
	baseURL, err := url.Parse(base)
	if err != nil {
		return ""
	}
	relURL, err := url.Parse(relative)
	if err != nil {
		return ""
	}
	return baseURL.ResolveReference(relURL).String()
}

// map转换为url参数
func MapToURLValues(data map[string]any) string {
	values := url.Values{}
	if data == nil {
		return values.Encode()
	}
	for key, value := range data {
		values.Set(key, fmt.Sprintf("%v", value))
	}
	return values.Encode()
}

// StringToFloat64 将字符串转换为浮点数
func StringToFloat64(str string) float64 {
	f, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return 0
	}
	return f
}

// truncateString 截取字符串为指定长度，并在末尾加上标识
func TruncateString(input string, maxLength int) string {
	// 如果输入字符串的长度小于等于最大长度，则直接返回
	if len(input) <= maxLength {
		return input
	}

	// 截取字符串并添加标识
	return input[:maxLength] + "[truncated]"
}

// 统计时间
func TrackTime(pre time.Time) time.Duration {
	elapsed := time.Since(pre)
	fmt.Println("elapsed:", elapsed)
	return elapsed
}

// 无阻塞发送
func SendNonBlocking[T any](ch chan<- T, value T) error {
	select {
	case ch <- value:
		return nil
	default:
		return fmt.Errorf("channel is full")
	}
}

// 超时发送
func SendWithTimeout[T any](ctx context.Context, ch chan<- T, value T, timeout time.Duration) error {
	timer := time.NewTimer(timeout) // 创建定时器
	select {
	case <-ctx.Done():
		timer.Stop()
		return ctx.Err()
	case ch <- value:
		timer.Stop()
		return nil
	case <-timer.C: // 超时
		// 处理超时情况
		timer.Stop()
		return fmt.Errorf("send timeout")
	}
}
