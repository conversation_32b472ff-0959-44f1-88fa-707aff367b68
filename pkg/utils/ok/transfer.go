package ok

import (
	"GoTrader/pkg/order"
	"strings"
)

func init() {
	sideToJson = make(map[order.Side]string)
	for k, v := range sideFromJson {
		sideToJson[v] = k
	}
	posSideToJson = make(map[order.PosSide]string)
	for k, v := range posSideFromJson {
		posSideToJson[v] = k
	}
	ordStatusToJson = make(map[order.OrdStatus]string)
	for k, v := range ordStatusFromJson {
		ordStatusToJson[v] = k
	}
}

var sideFromJson = map[string]order.Side{
	"buy":  order.BUY,
	"sell": order.SELL,
}
var sideToJson map[order.Side]string

var posSideFromJson = map[string]order.PosSide{
	"long":  order.LONG,
	"short": order.SHORT,
	"net":   order.BOTH,
}
var posSideToJson map[order.PosSide]string

var ordStatusFromJson = map[string]order.OrdStatus{
	"live":             order.NEW,
	"partially_filled": order.PARTIALLY_FILLED,
	"filled":           order.FILLED,
	"canceled":         order.CANCELED,
	"mmp_canceled":     order.EXPIRED_IN_MATCH,
}
var ordStatusToJson map[order.OrdStatus]string

func UnmarshalSide(s string) order.Side {
	return sideFromJson[s]
}

func MarshalSide(s order.Side) string {
	return sideToJson[s]
}

func UnmarshalPosSide(s string) order.PosSide {
	return posSideFromJson[s]
}

func MarshalPosSide(s order.PosSide) string {
	return posSideToJson[s]
}

func UnmarshalOrdType(s string) (order.OrdType, order.TimeInForce) {
	switch s {
	case "market":
		return order.MARKET, order.GTC
	case "limit":
		return order.LIMIT, order.GTC
	case "ioc":
		return order.LIMIT, order.IOC
	case "fok":
		return order.LIMIT, order.FOK
	}
	return order.OrdType("UNKNOWN"), order.TimeInForce("UNKNOWN")
}

func MarshalOrdTypeTimeInForce(s order.OrdType, t order.TimeInForce) string {
	switch t {
	case order.GTC:
		if s == order.MARKET {
			return "market"
		}
		return "limit"
	case order.IOC:
		return "ioc"
	case order.FOK:
		return "fok"
	}
	return "limit"
}

func UnmarshalOrdStatus(s string) order.OrdStatus {
	return ordStatusFromJson[s]
}

func MarshalOrdStatus(s order.OrdStatus) string {
	return ordStatusToJson[s]
}

func SymbolToInstIdSpot(symbol string) string {
	return strings.TrimSuffix(symbol, "USDT") + "-USDT"
}
func SymbolToInstIdFutures(symbol string) string {
	return strings.TrimSuffix(symbol, "USDT") + "-USDT-SWAP"
}
func InstIdSpotToFutures(instId string) string {
	return instId + "-SWAP"
}
func InstIdSpotToSymbol(instId string) string {
	return strings.TrimSuffix(instId, "-USDT") + "USDT"
}
func InstIdFuturesToSymbol(instId string) string {
	return strings.TrimSuffix(instId, "-USDT-SWAP") + "USDT"
}
func InstIdSpotToAsset(instId string) string {
	return strings.TrimSuffix(instId, "-USDT")
}
func SymbolToInstFamily(instId string) string {
	return strings.TrimSuffix(instId, "USDT") + "-USDT"
}
func InstFamilyToSymbol(instFamily string) string {
	return strings.TrimSuffix(instFamily, "-USDT") + "USDT"
}
