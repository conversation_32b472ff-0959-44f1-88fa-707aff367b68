package utils

import (
	"math"
)

// EMA 结构体，用于存储 EMA 的状态
type EMA struct {
	alpha             float64 // 平滑系数
	emaValue          float64 // 当前的 EMA 值
	firstData         bool    // 是否已经接收到第一个数据点
	dataCount         int64   // 当前已接收的数据点数量
	windowLength      int64   // 窗口长度
	requireFullWindow bool    // 是否需要满足窗口长度才返回有效值
}

// NewEMA 初始化 EMA 计算器
func NewEMA(window int64, requireFullWindow bool) *EMA {
	if window <= 0 {
		return &EMA{
			alpha:             0,
			emaValue:          0,
			firstData:         false,
			dataCount:         0,
			windowLength:      0,
			requireFullWindow: false,
		}
	}
	alpha := 2.0 / float64(window+1) // 计算平滑系数
	return &EMA{
		alpha:             alpha,
		emaValue:          math.NaN(), // 初始值为无效值
		firstData:         true,
		dataCount:         0,
		windowLength:      window,
		requireFullWindow: requireFullWindow,
	}
}

// Update 更新 EMA 值
func (e *EMA) Update(price float64) float64 {
	if e.windowLength <= 0 {
		return math.NaN()
	}

	if math.IsNaN(price) || math.IsInf(price, -1) || math.IsInf(price, 1) {
		return math.NaN()
	}
	if e.dataCount < e.windowLength {
		e.dataCount++ // 增加数据点计数
	}

	if e.firstData {
		// 如果是第一个数据点,直接赋值
		e.emaValue = price
		e.firstData = false
	} else {
		// 增量计算 EMA
		e.emaValue = e.alpha*price + (1-e.alpha)*e.emaValue
	}

	// 如果数据点数量小于窗口长度，且需要跳过返回值
	if e.requireFullWindow && e.dataCount < e.windowLength {
		return math.NaN() // 返回无效值
	}

	// 数据点数量达到窗口长度，返回有效值
	return e.emaValue
}

func (e *EMA) CountRemain() int64 {
	return e.windowLength - e.dataCount
}
