package utils

// 查找两个列表中的相同元素
func Intersect[T comparable](a, b []T) []T {
	seen := make(map[T]struct{})
	result := make([]T, 0)

	for _, v := range a {
		seen[v] = struct{}{}
	}
	for _, v := range b {
		if _, ok := seen[v]; ok {
			result = append(result, v)
			delete(seen, v) // 可选：去重（如果你想保留重复项就去掉这行）
		}
	}
	return result
}

// 列表转换为map
func SliceToMap[T comparable](slice []T) map[T]struct{} {
	m := make(map[T]struct{}, len(slice))
	for _, v := range slice {
		m[v] = struct{}{}
	}
	return m
}

// 反转列表
func ReverseSlice[T any](s []T) []T {
	reversed := make([]T, len(s))
	for i, j := 0, len(s)-1; i < len(s); i, j = i+1, j-1 {
		reversed[i] = s[j]
	}
	return reversed
}
