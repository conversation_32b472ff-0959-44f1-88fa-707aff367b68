package utils

import (
	"errors"
	"fmt"
	"strconv"
)

// MustParseFloat64 尝试解析 float64，失败会记录 error
func MustParseFloat64(str string, errs *[]error) float64 {
	v, err := strconv.ParseFloat(str, 64)
	if err != nil {
		*errs = append(*errs, fmt.<PERSON>rrorf("MustParseFloat error: %w", err))
	}
	return v
}

// MustParseInt64 尝试解析 int64，失败会记录 error
func MustParseInt64(str string, errs *[]error) int64 {
	v, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		*errs = append(*errs, fmt.Errorf("MustParseInt error: %w", err))
	}
	return v
}

// CollectErrors 合并所有 error
func CollectErrors(errs []error) error {
	if len(errs) == 0 {
		return nil
	}
	return errors.Join(errs...)
}
