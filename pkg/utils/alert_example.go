package utils

import (
	"fmt"
	"log/slog"
	"os"
)

// 示例：如何使用告警通知工具

func ExampleAlertUsage() {
	// 创建日志记录器
	logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	}))

	// 创建告警客户端
	alertClient := NewAlertClient(logger)

	// 示例1：发送基本告警
	basicAlert := &AlertRequest{
		Type:    AlertTypeVoice,
		User:    BuildUsers(UserLiquid, UserJiaoyin), // 通知liquid和jiaoyin
		Content: "系统检测到异常，请及时处理",
		Channel: ChannelWarning,
	}
	
	resp, err := alertClient.SendAlert(basicAlert)
	if err != nil {
		logger.Error("Failed to send basic alert", "error", err)
	} else {
		logger.Info("Basic alert sent successfully", "response", resp)
	}

	// 示例2：发送语音告警
	resp, err = alertClient.SendVoiceAlert(
		string(UserAll), // 通知所有用户
		"交易策略出现重大异常，需要立即处理",
		ChannelError,
	)
	if err != nil {
		logger.Error("Failed to send voice alert", "error", err)
	}

	// 示例3：发送短信告警
	resp, err = alertClient.SendSMSAlert(
		BuildUsers(UserSmall, User007),
		"价格差异超过阈值，请关注",
		ChannelArbitrage,
	)
	if err != nil {
		logger.Error("Failed to send SMS alert", "error", err)
	}

	// 示例4：发送带标签的告警（防重复）
	taggedAlert := &AlertRequest{
		Type:    AlertTypeVoice,
		User:    string(UserLiquid),
		Content: "连接断开，正在重连...",
		Channel: ChannelWarning,
	}
	
	resp, err = alertClient.SendAlertWithTag(taggedAlert, "connection_lost", 300) // 5分钟内不重复
	if err != nil {
		logger.Error("Failed to send tagged alert", "error", err)
	}

	// 示例5：使用便捷函数发送错误告警
	resp, err = alertClient.SendErrorAlert("系统崩溃，需要立即重启", UserLiquid, UserJiaoyin)
	if err != nil {
		logger.Error("Failed to send error alert", "error", err)
	}

	// 示例6：使用便捷函数发送警告告警
	resp, err = alertClient.SendWarningAlert("内存使用率超过90%", UserSmall)
	if err != nil {
		logger.Error("Failed to send warning alert", "error", err)
	}

	// 示例7：使用便捷函数发送策略告警
	resp, err = alertClient.SendStrategyAlert("新策略部署完成", UserAll)
	if err != nil {
		logger.Error("Failed to send strategy alert", "error", err)
	}

	// 示例8：使用便捷函数发送套利机会告警
	resp, err = alertClient.SendArbitrageAlert("发现套利机会：BTC价差3%", UserLiquid, User007)
	if err != nil {
		logger.Error("Failed to send arbitrage alert", "error", err)
	}

	// 示例9：发送带防重复的唯一告警
	resp, err = alertClient.SendUniqueAlert(
		"数据库连接异常",
		ChannelError,
		"db_connection_error",
		10, // 10分钟内不重复
		UserAll,
	)
	if err != nil {
		logger.Error("Failed to send unique alert", "error", err)
	}
}

// 实际使用场景示例

// 交易异常监控
func MonitorTradingAnomalies(alertClient *AlertClient, logger *slog.Logger) {
	// 模拟检测到交易异常
	anomalyDetected := true
	
	if anomalyDetected {
		content := fmt.Sprintf("交易异常检测：时间 %s，异常类型：价格波动超过5%%", 
			fmt.Sprintf("%d", **********))
		
		// 发送到错误频道，通知相关人员
		_, err := alertClient.SendErrorAlert(content, UserLiquid, UserJiaoyin, UserTanli)
		if err != nil {
			logger.Error("Failed to send trading anomaly alert", "error", err)
		}
	}
}

// 系统健康监控
func MonitorSystemHealth(alertClient *AlertClient, logger *slog.Logger) {
	// 模拟系统健康检查
	cpuUsage := 95.0
	memoryUsage := 88.0
	
	if cpuUsage > 90 {
		content := fmt.Sprintf("CPU使用率告警：当前使用率 %.1f%%，超过阈值90%%", cpuUsage)
		
		// 使用标签防止频繁告警
		req := &AlertRequest{
			Type:    AlertTypeVoice,
			User:    BuildUsers(UserSmall, User007),
			Content: content,
			Channel: ChannelWarning,
		}
		
		_, err := alertClient.SendAlertWithTag(req, "high_cpu_usage", 600) // 10分钟内不重复
		if err != nil {
			logger.Error("Failed to send CPU usage alert", "error", err)
		}
	}
	
	if memoryUsage > 85 {
		content := fmt.Sprintf("内存使用率告警：当前使用率 %.1f%%，超过阈值85%%", memoryUsage)
		
		_, err := alertClient.SendWarningAlert(content, UserSmall)
		if err != nil {
			logger.Error("Failed to send memory usage alert", "error", err)
		}
	}
}

// 套利机会通知
func NotifyArbitrageOpportunity(alertClient *AlertClient, logger *slog.Logger, symbol string, priceDiff float64) {
	if priceDiff > 2.0 { // 价差超过2%
		content := fmt.Sprintf("套利机会：%s 价差 %.2f%%，建议关注", symbol, priceDiff)
		
		_, err := alertClient.SendArbitrageAlert(content, UserLiquid, User007)
		if err != nil {
			logger.Error("Failed to send arbitrage opportunity alert", "error", err)
		}
	}
}

// 策略状态通知
func NotifyStrategyStatus(alertClient *AlertClient, logger *slog.Logger, strategyName string, status string) {
	content := fmt.Sprintf("策略状态更新：%s - %s", strategyName, status)
	
	var err error
	switch status {
	case "启动":
		_, err = alertClient.SendStrategyAlert(content, UserAll)
	case "停止":
		_, err = alertClient.SendStrategyAlert(content, UserLiquid, UserJiaoyin)
	case "异常":
		_, err = alertClient.SendErrorAlert(content, UserAll)
	default:
		_, err = alertClient.SendStrategyAlert(content, UserLiquid)
	}
	
	if err != nil {
		logger.Error("Failed to send strategy status alert", "error", err, "strategy", strategyName, "status", status)
	}
}
