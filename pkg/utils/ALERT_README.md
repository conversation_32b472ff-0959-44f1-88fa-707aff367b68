# 告警通知工具 (Alert Notification Tool)

这是一个用于发送告警通知的Go工具包，支持语音和短信通知，集成了飞书通知功能。

## 功能特性

- 支持语音和短信两种通知类型
- 支持多用户通知（单个用户、多个用户、全部用户）
- 支持多个预定义的通知频道
- 支持防重复通知（通过标签和时间控制）
- 提供便捷的快捷方法
- 完整的错误处理和日志记录
- 支持自定义超时时间

## 安装和导入

```go
import "GoTrader/pkg/utils"
```

## 基本用法

### 1. 创建告警客户端

```go
import (
    "log/slog"
    "os"
    "GoTrader/pkg/utils"
)

// 创建日志记录器
logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
    Level: slog.LevelInfo,
}))

// 创建告警客户端（默认30秒超时）
alertClient := utils.NewAlertClient(logger)

// 或者创建带自定义超时的客户端
alertClient := utils.NewAlertClientWithTimeout(60*time.Second, logger)
```

### 2. 发送基本告警

```go
// 创建告警请求
req := &utils.AlertRequest{
    Type:    utils.AlertTypeVoice,                    // 语音通知
    User:    utils.BuildUsers(utils.UserLiquid, utils.UserJiaoyin), // 通知liquid和jiaoyin
    Content: "系统检测到异常，请及时处理",
    Channel: utils.ChannelWarning,                    // 警告频道
}

// 发送告警
resp, err := alertClient.SendAlert(req)
if err != nil {
    logger.Error("Failed to send alert", "error", err)
} else {
    logger.Info("Alert sent successfully", "response", resp)
}
```

### 3. 使用便捷方法

```go
// 发送错误告警到零容忍频道
resp, err := alertClient.SendErrorAlert("系统崩溃，需要立即重启", utils.UserLiquid, utils.UserJiaoyin)

// 发送警告告警到异常报警频道
resp, err := alertClient.SendWarningAlert("内存使用率超过90%", utils.UserSmall)

// 发送策略告警到策略研发频道
resp, err := alertClient.SendStrategyAlert("新策略部署完成", utils.UserAll)

// 发送套利机会告警到套利频道
resp, err := alertClient.SendArbitrageAlert("发现套利机会：BTC价差3%", utils.UserLiquid, utils.User007)
```

### 4. 防重复通知

```go
// 使用标签防止重复通知
req := &utils.AlertRequest{
    Type:    utils.AlertTypeVoice,
    User:    string(utils.UserLiquid),
    Content: "连接断开，正在重连...",
    Channel: utils.ChannelWarning,
}

// 5分钟内不重复发送相同标签的通知
resp, err := alertClient.SendAlertWithTag(req, "connection_lost", 300)

// 或者使用便捷方法
resp, err := alertClient.SendUniqueAlert(
    "数据库连接异常",
    utils.ChannelError,
    "db_connection_error",
    10, // 10分钟内不重复
    utils.UserAll,
)
```

## API 参考

### 告警类型 (AlertType)

- `AlertTypeVoice` ("1"): 语音通知
- `AlertTypeSMS` ("2"): 短信通知

### 用户标识 (AlertUser)

- `UserJiaoyin`: jiaoyin
- `UserLiquid`: liquid
- `UserTanli`: tanli
- `UserSmall`: small
- `User007`: 007
- `UserAll`: all (全部用户)

### 通知频道 (AlertChannel)

- `ChannelWarning`: warning (盈迪苟异常报警)
- `ChannelError`: error (零容忍)
- `ChannelStrategy`: strategy (A01-交易策略研发沟通)
- `ChannelArbitrage`: arbitrage (量化｜套利机会通知群)

### 主要方法

#### AlertClient 方法

- `NewAlertClient(logger *slog.Logger) *AlertClient`: 创建告警客户端
- `NewAlertClientWithTimeout(timeout time.Duration, logger *slog.Logger) *AlertClient`: 创建带超时的告警客户端
- `SendAlert(req *AlertRequest) (*AlertResponse, error)`: 发送告警通知
- `SendVoiceAlert(users string, content string, channel AlertChannel) (*AlertResponse, error)`: 发送语音告警
- `SendSMSAlert(users string, content string, channel AlertChannel) (*AlertResponse, error)`: 发送短信告警
- `SendAlertWithTag(req *AlertRequest, tag string, tagTime int) (*AlertResponse, error)`: 发送带标签的告警

#### 便捷方法

- `SendErrorAlert(content string, users ...AlertUser) (*AlertResponse, error)`: 发送错误告警
- `SendWarningAlert(content string, users ...AlertUser) (*AlertResponse, error)`: 发送警告告警
- `SendStrategyAlert(content string, users ...AlertUser) (*AlertResponse, error)`: 发送策略告警
- `SendArbitrageAlert(content string, users ...AlertUser) (*AlertResponse, error)`: 发送套利告警
- `SendUniqueAlert(content string, channel AlertChannel, tag string, tagTimeMinutes int, users ...AlertUser) (*AlertResponse, error)`: 发送唯一告警

#### 工具函数

- `BuildUsers(users ...AlertUser) string`: 构建用户字符串

## 实际使用场景

### 交易异常监控

```go
func MonitorTradingAnomalies(alertClient *utils.AlertClient, logger *slog.Logger) {
    if anomalyDetected {
        content := fmt.Sprintf("交易异常检测：时间 %s，异常类型：价格波动超过5%%", time.Now().Format("2006-01-02 15:04:05"))
        
        _, err := alertClient.SendErrorAlert(content, utils.UserLiquid, utils.UserJiaoyin, utils.UserTanli)
        if err != nil {
            logger.Error("Failed to send trading anomaly alert", "error", err)
        }
    }
}
```

### 系统健康监控

```go
func MonitorSystemHealth(alertClient *utils.AlertClient, logger *slog.Logger) {
    cpuUsage := getCurrentCPUUsage()
    
    if cpuUsage > 90 {
        content := fmt.Sprintf("CPU使用率告警：当前使用率 %.1f%%，超过阈值90%%", cpuUsage)
        
        req := &utils.AlertRequest{
            Type:    utils.AlertTypeVoice,
            User:    utils.BuildUsers(utils.UserSmall, utils.User007),
            Content: content,
            Channel: utils.ChannelWarning,
        }
        
        _, err := alertClient.SendAlertWithTag(req, "high_cpu_usage", 600) // 10分钟内不重复
        if err != nil {
            logger.Error("Failed to send CPU usage alert", "error", err)
        }
    }
}
```

### 套利机会通知

```go
func NotifyArbitrageOpportunity(alertClient *utils.AlertClient, logger *slog.Logger, symbol string, priceDiff float64) {
    if priceDiff > 2.0 { // 价差超过2%
        content := fmt.Sprintf("套利机会：%s 价差 %.2f%%，建议关注", symbol, priceDiff)
        
        _, err := alertClient.SendArbitrageAlert(content, utils.UserLiquid, utils.User007)
        if err != nil {
            logger.Error("Failed to send arbitrage opportunity alert", "error", err)
        }
    }
}
```

## 错误处理

所有方法都返回 `(*AlertResponse, error)`，请务必检查错误：

```go
resp, err := alertClient.SendAlert(req)
if err != nil {
    // 处理错误
    logger.Error("Alert failed", "error", err)
    return
}

// 检查响应
if !resp.Success {
    logger.Warn("Alert sent but not successful", "message", resp.Message)
}
```

## 测试

运行测试：

```bash
go test ./pkg/utils -v
```

运行基准测试：

```bash
go test ./pkg/utils -bench=.
```

## 注意事项

1. 请确保网络连接正常，API地址可访问
2. 合理使用防重复功能，避免告警风暴
3. 根据告警重要性选择合适的频道和用户
4. 建议在生产环境中设置合适的超时时间
5. 记录告警发送日志，便于问题排查
