package utils

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"
)

// FreqLimiter 是一个次数限制器
type FreqLimiter struct {
	mu       sync.Mutex
	interval time.Duration // 时间窗口，例如10秒
	limit    int           // 限制次数，例如5次
	times    []time.Time   // 调用时间记录
}

// NewFreqLimiter 创建一个新的限制器
// 每间隔 interval 时间，最多允许 limit 次调用
func NewFreqLimiter(limit int, interval time.Duration) *FreqLimiter {
	return &FreqLimiter{
		interval: interval,
		limit:    limit,
		times:    make([]time.Time, 0, limit),
	}
}

func (l *FreqLimiter) Acquire(ctx context.Context) bool {
	for {
		now := time.Now()
		l.mu.Lock()

		// 清理过期的时间
		cutoff := now.Add(-l.interval)
		validStart := 0
		for i, t := range l.times {
			if t.After(cutoff) {
				validStart = i
				break
			}
			validStart = i + 1 // 所有 expired 的都跳过
		}
		l.times = l.times[validStart:]

		// 是否还有名额
		if len(l.times) < l.limit {
			l.times = append(l.times, now)
			l.mu.Unlock()
			return true
		}

		// 需要等待，计算等待时间
		waitTime := l.times[0].Sub(cutoff)
		l.mu.Unlock()

		// 等待或者 context 超时
		timer := time.NewTimer(waitTime)
		select {
		case <-ctx.Done():
			timer.Stop()
			return false
		case <-timer.C:
			// 继续重试循环
		}
	}
}

// 熔断器, 当单位时间执行次数超过一定次数后, 不再执行函数并返回错误
type RateGuard struct {
	mu         sync.Mutex
	triggered  bool
	windowSize time.Duration
	maxCalls   int
	timestamps []time.Time
}

func NewRateGuard(windowSize time.Duration, maxCalls int) *RateGuard {
	return &RateGuard{
		triggered:  false,
		windowSize: windowSize,
		maxCalls:   maxCalls,
		timestamps: make([]time.Time, 0, maxCalls),
	}
}

// 根据函数运行结果判断次数限制
func (rg *RateGuard) RunWithLimit(targetFunc func() bool, errFunc func(error)) {
	rg.mu.Lock()
	defer rg.mu.Unlock()

	// 已经触发过
	if rg.triggered {
		return
	}

	now := time.Now()
	// 清理过期的时间戳
	validTimestamps := make([]time.Time, 0, len(rg.timestamps))
	for _, ts := range rg.timestamps {
		if now.Sub(ts) <= rg.windowSize {
			validTimestamps = append(validTimestamps, ts)
		}
	}
	rg.timestamps = validTimestamps

	// 检查当前窗口内是否超过最大限制
	if len(rg.timestamps) > rg.maxCalls {
		rg.triggered = true
		errFunc(fmt.Errorf("rate limit exceeded: %d calls within %v", rg.maxCalls, rg.windowSize))
	}
	// 执行目标函数
	tried := targetFunc()
	if tried {
		// 记录本次调用时间
		rg.timestamps = append(rg.timestamps, now)
	}
}

// 直接添加调用时间戳
func (rg *RateGuard) AddCall(errFunc func(error)) {
	rg.mu.Lock()
	defer rg.mu.Unlock()

	// 已经触发过
	if rg.triggered {
		return
	}

	now := time.Now()
	// 清理过期的时间戳
	validTimestamps := make([]time.Time, 0, len(rg.timestamps))
	for _, ts := range rg.timestamps {
		if now.Sub(ts) <= rg.windowSize {
			validTimestamps = append(validTimestamps, ts)
		}
	}
	rg.timestamps = validTimestamps

	// 检查当前窗口内是否超过最大限制
	if len(rg.timestamps) > rg.maxCalls {
		rg.triggered = true
		errFunc(fmt.Errorf("rate limit exceeded: %d calls within %v", rg.maxCalls, rg.windowSize))
	}

	// 记录本次调用时间
	rg.timestamps = append(rg.timestamps, now)

}

// FalseLimiter 是一个简单的限流器，基于布尔值的成功/失败统计
// 适用于需要限制连续失败次数的场景
// 当连续失败次数超过允许的最大值时，进入限制状态
// 在限制状态下，所有的 Add 调用都会被忽略，直到经过指定的时间窗口后自动恢复
type FalseLimiter struct {
	mu            sync.Mutex
	windowSize    int           // 统计数量
	allowFailures int           // 允许错误数
	resetDuration time.Duration // 限制状态持续时间

	history      []bool
	failureCount int
	isLimited    bool
	ctx          context.Context
	cancelFunc   context.CancelFunc
}

// NewLimiter 创建一个带 context 的限制器
func NewFalseLimiter(windowSize int, allowFailures int, resetAfter time.Duration) *FalseLimiter {
	ctx, cancel := context.WithCancel(context.Background())
	return &FalseLimiter{
		windowSize:    windowSize,
		allowFailures: allowFailures,
		resetDuration: resetAfter,
		history:       make([]bool, 0, windowSize),
		failureCount:  0,
		ctx:           ctx,
		cancelFunc:    cancel,
	}
}

// Add 添加一个 bool 值（true 成功，false 失败）
func (l *FalseLimiter) Add(success bool) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.isLimited {
		return
	}

	// 如果超出窗口，先移除最早一项
	if len(l.history) == l.windowSize {
		removed := l.history[0]
		l.history = l.history[1:]
		if !removed {
			l.failureCount--
		}
	}

	// 添加新记录
	l.history = append(l.history, success)
	if !success {
		l.failureCount++
	}

	// 判断是否超过限制
	if len(l.history) == l.windowSize && l.failureCount > l.allowFailures {
		l.enterLimitedState()
	}
}

// enterLimitedState 进入限制状态，定时后自动恢复
func (l *FalseLimiter) enterLimitedState() {
	l.isLimited = true

	go func() {
		select {
		case <-time.After(l.resetDuration):
			l.mu.Lock()
			defer l.mu.Unlock()
			l.isLimited = false
			l.failureCount = 0
			l.history = l.history[:0]
		case <-l.ctx.Done():
			// 外部取消
		}
	}()
}

// IsLimited 判断当前是否在限制中
func (l *FalseLimiter) IsLimited() bool {
	l.mu.Lock()
	defer l.mu.Unlock()
	return l.isLimited
}

// Stop 停止限制器（主动释放资源）
func (l *FalseLimiter) Stop() {
	l.cancelFunc()
}

// 每整数分钟频率限制器
type MiniteRateLimiter struct {
	maxWeight  int64
	mu         sync.Mutex
	blockMu    sync.Mutex
	usedWeight int64
	logger     *slog.Logger
}

func NewMiniteRateLimiter(maxWeight int64, logger *slog.Logger) *MiniteRateLimiter {
	rl := &MiniteRateLimiter{maxWeight: maxWeight, usedWeight: 0, logger: logger}
	return rl
}

// 获取到下一分钟的秒数
func GetSecondsToNextMinute() int64 {
	now := time.Now().Unix()
	return 60 - (now % 60)
}

func (rl *MiniteRateLimiter) AllowRequest(weight int64) error {
	if weight <= 0 {
		return fmt.Errorf("weight must be positive, got %d", weight)
	}
	if weight > rl.maxWeight {
		return fmt.Errorf("weight must be less than maxWeight %d", rl.maxWeight)
	}

	rl.mu.Lock()
	rl.blockMu.Lock()
	// 尝试获取到锁之后马上释放
	rl.blockMu.Unlock()
	defer rl.mu.Unlock()

	// 检查权重是否足够, 不够则等待至下一分钟重置权重
	if rl.usedWeight+weight > rl.maxWeight {
		// 权重不够, 等待足够秒数后重置权重
		sec := GetSecondsToNextMinute()
		rl.logger.Info("wait for next minute", "seconds", sec)
		time.Sleep(time.Duration(sec) * time.Second)
		rl.usedWeight = 0
	}

	rl.usedWeight += weight
	return nil
}

// block直至下一分钟
func (rl *MiniteRateLimiter) BlockTillReset() {
	rl.blockMu.Lock()
	sec := GetSecondsToNextMinute()
	rl.logger.Info("block for next minute", "seconds", sec)
	time.Sleep(time.Duration(sec) * time.Second)
	rl.blockMu.Unlock()
}

// 获取当前权重
func (rl *MiniteRateLimiter) GetUsedWeight() int64 {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	return rl.usedWeight
}

// 间隔执行
type DoTimer struct {
	prevTime time.Time
	Interval time.Duration
}

func (td *DoTimer) Do(f func()) {
	now := time.Now()
	if now.Sub(td.prevTime) >= td.Interval {
		f()
		td.prevTime = now
	}
}
