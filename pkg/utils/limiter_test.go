package utils

import (
	"testing"
	"time"
)

func TestFalseLimiter_NormalFlow(t *testing.T) {
	lim := NewFalseLimiter(5, 2, 2*time.Second)

	// 添加 3 个成功
	lim.Add(true)
	lim.Add(true)
	lim.Add(true)

	if lim.IsLimited() {
		t.Error("Limiter should not be limited after only successes")
	}

	// 添加 2 个失败
	lim.Add(false)
	lim.Add(false)

	if lim.IsLimited() {
		t.Error("Limiter should not be limited with only 2 failures")
	}

	// 添加第 6 个 false，超出 window + 错误阈值
	lim.Add(false)

	if !lim.IsLimited() {
		t.<PERSON>rror("Limiter should be in limited state after too many failures")
	}
}

func TestFalseLimiter_Recovery(t *testing.T) {
	lim := NewFalseLimiter(5, 2, 1*time.Second)

	// 填满窗口并超过错误阈值
	lim.Add(false)
	lim.Add(false)
	lim.Add(false)
	lim.Add(false)
	lim.Add(false)

	if !lim.IsLimited() {
		t.Error("Limiter should be in limited state")
	}

	// 等待恢复
	time.Sleep(1100 * time.Millisecond)

	if lim.IsLimited() {
		t.Error("Limiter should have exited limited state after resetDuration")
	}
}

func TestFalseLimiter_WindowSlide(t *testing.T) {
	lim := NewFalseLimiter(3, 1, 5*time.Second)

	// 添加 true, false, false -> 超过限制
	lim.Add(true)
	lim.Add(false)
	lim.Add(false)

	if !lim.IsLimited() {
		t.Error("Limiter should be limited after 2 failures in window of 3")
	}

	// 等待恢复
	time.Sleep(6 * time.Second)

	// 添加 true, true, false -> 应该不被限制
	lim.Add(true)
	lim.Add(true)
	lim.Add(false)

	if lim.IsLimited() {
		t.Error("Limiter should NOT be limited with only 1 failure")
	}
}

func TestFalseLimiter_Stop(t *testing.T) {
	lim := NewFalseLimiter(3, 1, 5*time.Second)

	// 触发限制
	lim.Add(false)
	lim.Add(false)
	lim.Add(false)

	if !lim.IsLimited() {
		t.Error("Limiter should be in limited state")
	}

	// 主动 stop
	lim.Stop()

	// 等待看是否还会恢复（应该不会触发解除）
	time.Sleep(2 * time.Second)

	if !lim.IsLimited() {
		t.Error("Limiter should stay limited after Stop (no reset happens)")
	}
}
