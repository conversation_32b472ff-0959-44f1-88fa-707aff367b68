package utils

import (
	"context"
	"log"
	"time"
)

type TokenBucket struct {
	rate       int             // 每秒生成的令牌数
	capacity   int             // 令牌桶的容量
	ctx        context.Context // 上下文
	tokens     chan struct{}   // 存放令牌的 channel
	lastRefill time.Time       // 上次生成令牌的时间
}

// NewTokenBucket 创建一个新的令牌桶
func NewTokenBucket(ctx context.Context, rate, capacity int) *TokenBucket {
	tb := &TokenBucket{
		rate:       rate,
		capacity:   capacity,
		ctx:        ctx,
		tokens:     make(chan struct{}, capacity), // 使用一个缓冲通道作为令牌桶
		lastRefill: time.Now(),
	}
	// 启动令牌生成的 goroutine
	go tb.generateTokens()
	return tb
}

// generateTokens 每秒定时生成令牌并填充令牌桶
func (tb *TokenBucket) generateTokens() {
	ticker := time.NewTicker(time.Second) // 每秒生成令牌
	defer ticker.Stop()
	for {
		select {
		case <-tb.ctx.Done(): // 当 context 被取消时，退出协程
			log.Println("停止令牌生成协程")
			return
		case <-ticker.C:
			tb.Refill()
		}
	}
}

// Refill 根据时间生成令牌并填充到令牌桶
func (tb *TokenBucket) Refill() {
	// 计算从上次生成令牌以来，经过的秒数
	now := time.Now()
	elapsed := now.Sub(tb.lastRefill)
	tb.lastRefill = now

	// 计算生成的令牌数量
	newTokens := int(elapsed.Seconds()) * tb.rate

	// 填充令牌桶，最多不会超过最大容量
	for i := 0; i < newTokens; i++ {
		select {
		case tb.tokens <- struct{}{}: // 向通道中插入令牌
		default: // 如果通道已满，不再插入
			return
		}
	}
}

// Take 尝试从令牌桶获取令牌，如果没有令牌则阻塞
func (tb *TokenBucket) Take(ctx context.Context, count int, weight int) int {
	for i := 0; i < count; i += 1 {
		for j := 0; j < weight; j += 1 { // 为了提高并发性，每个令牌获取权重
			select {
			case <-ctx.Done(): // 如果 context 超时或被取消
				return i
			case <-tb.tokens: // 获取令牌
				continue
			}
		}
	}
	return count
}
