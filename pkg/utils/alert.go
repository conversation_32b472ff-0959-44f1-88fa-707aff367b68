package utils

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// AlertType 告警类型
type AlertType string

const (
	AlertTypeVoice AlertType = "1" // 语音通知
	AlertTypeSMS   AlertType = "2" // 短信通知
)

// AlertUser 用户标识
type AlertUser string

const (
	UserJiaoyin AlertUser = "jiaoyin"
	UserLiquid  AlertUser = "liquid"
	UserTanli   AlertUser = "tanli"
	UserSmall   AlertUser = "small"
	User007     AlertUser = "007"
	UserAll     AlertUser = "all" // 全部用户
)

// AlertChannel 飞书通知频道
type AlertChannel string

const (
	ChannelWarning   AlertChannel = "warning"   // 盈迪苟异常报警
	ChannelError     AlertChannel = "error"     // 零容忍
	ChannelStrategy  AlertChannel = "strategy"  // A01-交易策略研发沟通
	ChannelArbitrage AlertChannel = "arbitrage" // 量化｜套利机会通知群
)

// AlertRequest 告警请求参数
type AlertRequest struct {
	Type    AlertType    `json:"type"`               // 通知类型，1表示语音，2表示短信
	User    string       `json:"user"`               // 接收用户标识
	Content string       `json:"content"`            // 飞书通知内容
	Channel AlertChannel `json:"channel"`            // 飞书通知频道
	Tag     string       `json:"tag,omitempty"`      // 请求标签，用于标记通知唯一性
	TagTime int          `json:"tag_time,omitempty"` // 标签有效期，默认3600秒
}

// AlertResponse 告警响应
type AlertResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    any    `json:"data,omitempty"`
}

// AlertClient 告警通知客户端
type AlertClient struct {
	baseURL    string
	httpClient *http.Client
	logger     *slog.Logger
}

// NewAlertClient 创建告警通知客户端
func NewAlertClient(logger *slog.Logger) *AlertClient {
	return &AlertClient{
		baseURL: "https://admin.indigo888.com/alert/",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// NewAlertClientWithTimeout 创建带自定义超时的告警通知客户端
func NewAlertClientWithTimeout(timeout time.Duration, logger *slog.Logger) *AlertClient {
	return &AlertClient{
		baseURL: "https://admin.indigo888.com/alert/",
		httpClient: &http.Client{
			Timeout: timeout,
		},
		logger: logger,
	}
}

// BuildUsers 构建用户字符串，支持多个用户用下划线拼接
func BuildUsers(users ...AlertUser) string {
	if len(users) == 0 {
		return string(UserAll)
	}

	userStrs := make([]string, len(users))
	for i, user := range users {
		userStrs[i] = string(user)
	}
	return strings.Join(userStrs, "_")
}

// SendAlert 发送告警通知
func (c *AlertClient) SendAlert(req *AlertRequest) (*AlertResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("alert request cannot be nil")
	}

	// 验证必填参数
	if req.Content == "" {
		return nil, fmt.Errorf("content is required")
	}

	// 设置默认值
	if req.Type == "" {
		req.Type = AlertTypeVoice // 默认语音通知
	}
	if req.User == "" {
		req.User = string(UserAll) // 默认全部用户
	}
	if req.TagTime == 0 && req.Tag != "" {
		req.TagTime = 3600 // 默认1小时
	}

	return c.sendRequest(req)
}

// SendVoiceAlert 发送语音告警
func (c *AlertClient) SendVoiceAlert(users string, content string, channel AlertChannel) (*AlertResponse, error) {
	req := &AlertRequest{
		Type:    AlertTypeVoice,
		User:    users,
		Content: content,
		Channel: channel,
	}
	return c.SendAlert(req)
}

// SendSMSAlert 发送短信告警
func (c *AlertClient) SendSMSAlert(users string, content string, channel AlertChannel) (*AlertResponse, error) {
	req := &AlertRequest{
		Type:    AlertTypeSMS,
		User:    users,
		Content: content,
		Channel: channel,
	}
	return c.SendAlert(req)
}

// SendAlertWithTag 发送带标签的告警通知（防重复）
func (c *AlertClient) SendAlertWithTag(req *AlertRequest, tag string, tagTime int) (*AlertResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("alert request cannot be nil")
	}

	req.Tag = tag
	if tagTime > 0 {
		req.TagTime = tagTime
	} else {
		req.TagTime = 3600 // 默认1小时
	}

	return c.SendAlert(req)
}

// sendRequest 发送HTTP请求
func (c *AlertClient) sendRequest(req *AlertRequest) (*AlertResponse, error) {
	// 构建请求参数
	params := url.Values{}
	params.Set("type", string(req.Type))
	params.Set("user", req.User)
	params.Set("content", req.Content)
	params.Set("channel", string(req.Channel))

	if req.Tag != "" {
		params.Set("tag", req.Tag)
	}
	if req.TagTime > 0 {
		params.Set("tag_time", strconv.Itoa(req.TagTime))
	}

	// 记录请求日志
	logger := c.logger.With(
		"url", c.baseURL,
		"type", req.Type,
		"user", req.User,
		"channel", req.Channel,
		"tag", req.Tag,
		"tag_time", req.TagTime,
	)

	// 发送POST请求
	resp, err := c.httpClient.PostForm(c.baseURL, params)
	if err != nil {
		logger.Error("Failed to send alert request", "error", err)
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error("Failed to read response body", "error", err)
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	logger.Info("Alert request sent",
		"status_code", resp.StatusCode,
		"response", TruncateString(string(body), 200),
	)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var alertResp AlertResponse
	if err := json.Unmarshal(body, &alertResp); err != nil {
		// 如果无法解析为JSON，则认为请求成功，返回原始响应
		logger.Warn("Failed to parse response as JSON, treating as success", "error", err)
		return &AlertResponse{
			Success: true,
			Message: string(body),
		}, nil
	}

	return &alertResp, nil
}

// 便捷函数：发送错误告警到零容忍频道
func (c *AlertClient) SendErrorAlert(content string, users ...AlertUser) (*AlertResponse, error) {
	userStr := BuildUsers(users...)
	return c.SendVoiceAlert(userStr, content, ChannelError)
}

// 便捷函数：发送警告告警到异常报警频道
func (c *AlertClient) SendWarningAlert(content string, users ...AlertUser) (*AlertResponse, error) {
	userStr := BuildUsers(users...)
	return c.SendVoiceAlert(userStr, content, ChannelWarning)
}

// 便捷函数：发送策略告警到策略研发频道
func (c *AlertClient) SendStrategyAlert(content string, users ...AlertUser) (*AlertResponse, error) {
	userStr := BuildUsers(users...)
	return c.SendVoiceAlert(userStr, content, ChannelStrategy)
}

// 便捷函数：发送套利机会告警到套利频道
func (c *AlertClient) SendArbitrageAlert(content string, users ...AlertUser) (*AlertResponse, error) {
	userStr := BuildUsers(users...)
	return c.SendVoiceAlert(userStr, content, ChannelArbitrage)
}

// 便捷函数：发送带防重复标签的告警
func (c *AlertClient) SendUniqueAlert(content string, channel AlertChannel, tag string, tagTimeMinutes int, users ...AlertUser) (*AlertResponse, error) {
	userStr := BuildUsers(users...)
	req := &AlertRequest{
		Type:    AlertTypeVoice,
		User:    userStr,
		Content: content,
		Channel: channel,
		Tag:     tag,
		TagTime: tagTimeMinutes * 60, // 转换为秒
	}
	return c.SendAlert(req)
}
