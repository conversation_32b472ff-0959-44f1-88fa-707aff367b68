package utils

import "math"

// 根据步长保留浮点数
func RoundToStep(f float64, step float64) float64 {
	if step <= 0 {
		return f
	}
	return math.Round(f/step) * step // 四舍五入
}

func CeilToStep(f float64, step float64) float64 {
	if step <= 0 {
		return f
	}
	return math.Ceil(f/step) * step
}

func FloorToStep(f float64, step float64) float64 {
	if step <= 0 {
		return f
	}
	return math.Floor(f/step) * step
}

// 根据步长保留整数
func RoundIntToStep[T ~int | ~int64 | ~int32](d T, step T) T {
	if step <= 0 {
		return d
	}
	mod := d % step
	if mod > step/2 {
		return d + step - mod
	}
	return d - mod
}

func FloorIntToStep[T ~int | ~int64 | ~int32](d T, step T) T {
	if step <= 0 {
		return d
	}
	mod := d % step
	return d - mod
}

// 根据步长判断浮点数是否相等
func StepEqual(a, b, step float64) bool {
	return math.Abs(a-b) < step
}

// 获取单位最大精度步长
func GetMaxPrecisionStep(f float64) float64 {
	precision := int(math.Log10(f))
	if precision <= 0 {
		precision -= 1
	}
	return math.Pow10(precision)
}
