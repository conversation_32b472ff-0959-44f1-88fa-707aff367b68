package factory

import (
	"GoTrader/internal/ok"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	okclient "GoTrader/pkg/client/ok"
	okhandler "GoTrader/pkg/handler/ok"
	"GoTrader/pkg/wsclient"
	"context"
	"io"
	"log/slog"
	"net/http"

	"github.com/gorilla/websocket"
)

// 生成欧意客户端
func NewOkClients(
	ctx context.Context, b *broker.Broker, proxy bool, logger *slog.Logger, dataLogger *slog.Logger,
) (
	dc client.DataClient, ac client.APIClient, err error,
) {
	// 默认api或代理api
	var apiConfig ok.OkexAPIConfig
	if proxy {
		apiConfig = ok.ProxyOkexAPI
	} else {
		apiConfig = ok.DefaultOkexAPI
	}

	if dataLogger == nil {
		dataLogger = slog.New(slog.NewJSONHandler(io.Discard, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}

	wsConfig := wsclient.NewConfig(
		wsclient.WithRetryCount(3),
		wsclient.WithRetryInterval(2),
		wsclient.WithPingInterval(5),
		wsclient.WithPingType(websocket.TextMessage),
		wsclient.WithReadTimeout(10),
		wsclient.WithRestartHour(0),
	)
	// 创建数据源监听客户端
	ddc := okclient.NewDataClient(
		apiConfig.WsUrlPublic,
		[]string{ok.Depth400Chan, ok.PriceLimitChan},
		wsclient.DefaultNew(wsConfig, logger),
		dataLogger,
	)
	ddc.AddStreamHandler(okhandler.Depth400, okhandler.DataBindDataClient(ddc, okhandler.Depth400TriggerHandler)).
		AddStreamHandler(okhandler.PriceLimit, okhandler.PriceLimitHandler)

	// 创建标记价格和资金费监听客户端
	fdc := okclient.NewDataClient(
		apiConfig.WsUrlPublic,
		[]string{ok.FundingRateChan, ok.IndexPriceChan},
		wsclient.DefaultNew(wsConfig, logger),
		dataLogger,
	).
		AddStreamHandler(okhandler.FundingRate, okhandler.FundingRateHandler).
		AddStreamHandler(okhandler.IndexPrice, okhandler.IndexPriceHandler)

	// 创建合并数据源监听客户端
	dc = okclient.NewCombinedDataClient(
		ddc,
		fdc,
	)
	err = dc.Run(ctx)

	if err != nil {
		logger.Error("error connect data ws", "error", err)
		return nil, nil, err
	}

	// 创建api客户端
	ac = okclient.NewAPIClient(
		apiConfig.BaseAPIUrl,
		http.DefaultClient,
		logger,
	)

	// 更新broker
	err = ac.UpdateBroker(b)
	if err != nil {
		return nil, nil, err
	}

	// 创建broker监听客户端
	brokerWsConfig := wsclient.NewConfig(
		wsclient.WithRetryCount(3),
		wsclient.WithRetryInterval(2),
		wsclient.WithPingInterval(5),
		wsclient.WithPingType(websocket.TextMessage),
		wsclient.WithReadTimeout(10),
		wsclient.WithIgnoreLostReadMsg(false),
		wsclient.WithRestartHour(0),
		// wsclient.WithCheckInterval(30),
	)
	bc := okclient.NewBrokerClient(
		apiConfig.WsUrlPrivate,
		wsclient.DefaultNew(brokerWsConfig, logger),
		logger,
	).
		AddBroker(b).
		AddEventHandler(okhandler.OrdersUpdate, okhandler.OrdersPositionUpdateHandler).
		AddEventHandler(okhandler.AccountAndPositionUpdate, okhandler.AccountAndPositionUpdateHandler).
		AddEventHandler(okhandler.AccountUpdate, okhandler.AccountUpdateHandler)
	err = bc.Run(ctx)
	if err != nil {
		logger.Error("error connect broker ws", "error", err)
		return nil, nil, err
	}

	return dc, ac, nil
}

// 生成欧意客户端, 只包含基础数据
func NewOkDataClients(
	ctx context.Context, proxy bool, logger *slog.Logger, dataLogger *slog.Logger,
) (
	dc client.DataClient, ac client.APIClient, err error,
) {
	if dataLogger == nil {
		dataLogger = slog.New(slog.NewJSONHandler(io.Discard, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}

	// 默认api或代理api
	var apiConfig ok.OkexAPIConfig
	if proxy {
		apiConfig = ok.ProxyOkexAPI
	} else {
		apiConfig = ok.DefaultOkexAPI
	}

	wsConfig := wsclient.NewConfig(
		wsclient.WithRetryCount(3),
		wsclient.WithRetryInterval(2),
		wsclient.WithPingInterval(5),
		wsclient.WithPingType(websocket.TextMessage),
		wsclient.WithReadTimeout(10),
		wsclient.WithRestartHour(0),
	)

	// 创建数据源监听客户端
	ddc := okclient.NewDataClient(
		apiConfig.WsUrlPublic,
		[]string{ok.Depth5Chan},
		wsclient.DefaultNew(wsConfig, logger),
		dataLogger,
	)
	ddc.AddStreamHandler(okhandler.Depth5, okhandler.Depth5TriggerHandler)

	// 创建资金费监听客户端
	fdc := okclient.NewDataClient(
		apiConfig.WsUrlPublic,
		[]string{ok.FundingRateChan, ok.IndexPriceChan},
		wsclient.DefaultNew(wsConfig, logger),
		dataLogger,
	).
		AddStreamHandler(okhandler.FundingRate, okhandler.FundingRateHandler).
		AddStreamHandler(okhandler.IndexPrice, okhandler.IndexPriceHandler)

	// 创建合并数据源监听客户端
	dc = okclient.NewCombinedDataClient(
		ddc,
		fdc,
	)
	err = dc.Run(ctx)
	if err != nil {
		logger.Error("error connect data ws", "error", err)
		return nil, nil, err
	}

	// 创建api客户端
	ac = okclient.NewAPIClient(
		apiConfig.BaseAPIUrl,
		http.DefaultClient,
		logger,
	)

	return dc, ac, nil
}
