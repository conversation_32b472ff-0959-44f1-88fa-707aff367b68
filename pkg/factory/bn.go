package factory

import (
	"GoTrader/internal/bn"
	api "GoTrader/pkg/api/bn"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	bnclient "GoTrader/pkg/client/bn"
	"GoTrader/pkg/data"
	bnhandler "GoTrader/pkg/handler/bn"
	"GoTrader/pkg/wsclient"
	"context"
	"io"
	"log/slog"
	"net/http"
)

// 生成币安客户端
func NewBnClients(
	ctx context.Context, b *broker.Broker, proxy bool, logger *slog.Logger, dataLogger *slog.Logger, marketLogger *slog.Logger,
) (
	dc client.DataClient, ac client.APIClient, err error,
) {
	// 检查日志
	if dataLogger == nil {
		dataLogger = slog.New(slog.NewJSONHandler(io.Discard, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}
	if marketLogger == nil {
		marketLogger = slog.New(slog.NewJSONHandler(io.Discard, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}
	// 默认api或代理api
	var apiConfig bn.BinanceAPIConfig
	if proxy {
		apiConfig = bn.ProxyBinanceAPI
	} else {
		apiConfig = bn.DefaultBinanceAPI
	}

	// 创建api客户端和账户监听客户端
	switch b.Mode {
	case broker.Costom:
		// 传统api客户端
		_ac := bnclient.NewCostomAPIClient(
			apiConfig.FuturesAPIURL,
			apiConfig.SpotAPIURL,
			api.CostomBrokerAPIs,
			http.DefaultClient,
			logger,
		)
		// 传统账户监听客户端
		_bc := bnclient.NewCostomBrokerClient(
			bnclient.NewBrokerClient(
				apiConfig.FuturesAccountWSURL,
				wsclient.DefaultNew(nil, logger),
				_ac.GetFuturesListenKey,
				_ac.UpdateBroker,
				logger,
			).AddEventHandler(bnhandler.OrderTradeUpdate, bnhandler.OrderTradeUpdateHandler).
				AddEventHandler(bnhandler.AccountUpdate, bnhandler.AccountUpdateHandler).
				AddEventHandler(bnhandler.AccountConfigUpdate, bnhandler.AccountConfigUpdateHandler).
				AddEventHandler(bnhandler.TradeLite, bnhandler.IgnoreHandler),
			bnclient.NewBrokerClient(
				apiConfig.SpotAccountWSURL,
				wsclient.DefaultNew(nil, logger),
				_ac.GetSpotListenKey,
				_ac.UpdateBroker,
				logger,
			).AddEventHandler(bnhandler.ExecutionReport, bnhandler.ExecutionHandler).
				AddEventHandler(bnhandler.OutboundAccountPosition, bnhandler.OutboundAccountPositionHandler).
				AddEventHandler(bnhandler.BalanceUpdate, bnhandler.BalanceUpdateHandler),
		).AddBroker(b)
		err = _bc.Run(ctx)
		if err != nil {
			logger.Error("传统账户连接失败", "error", err)
			return nil, nil, err
		}
		// 创建全市场客户端
		err = bnclient.NewMarketClient(
			apiConfig.FuturesStreamWSURL,
			[]string{bn.MiniTickerChan},
			wsclient.DefaultNew(nil, logger),
			data.NewMarketData(),
			marketLogger,
		).AddStreamHandler(
			bnhandler.MiniTicker, bnhandler.MarketBindBroker(b, bnhandler.FuturesMiniTickerUpdateCostomBrokerHandler),
		).Run(ctx)
		if err != nil {
			logger.Error("市场ws客户端连接失败", "error", err)
			return nil, nil, err
		}

		ac = _ac

	case broker.Portfolio:
		// 统一账户api客户端
		_ac := bnclient.NewPortfolioAPIClient(
			apiConfig.FuturesAPIURL,
			apiConfig.SpotAPIURL,
			apiConfig.PortfolioAPIURL,
			api.PortfolioBrokerAPIs,
			http.DefaultClient,
			logger,
		)
		// 统一账户监听客户端
		_bc := bnclient.NewBrokerClient(
			apiConfig.PortfolioAccountWSURL,
			wsclient.DefaultNew(wsclient.NewConfig(wsclient.WithIgnoreLostReadMsg(false)), logger),
			_ac.GetListenKey,
			_ac.UpdateBroker,
			logger,
		).AddBroker(b).AddEventHandler(bnhandler.OrderTradeUpdate, bnhandler.OrderTradeUpdateHandler).
			AddEventHandler(bnhandler.AccountUpdate, bnhandler.AccountUpdateHandler).
			AddEventHandler(bnhandler.ExecutionReport, bnhandler.ExecutionHandler).
			AddEventHandler(bnhandler.OutboundAccountPosition, bnhandler.OutboundAccountPositionHandler).
			AddEventHandler(bnhandler.AccountConfigUpdate, bnhandler.AccountConfigUpdateHandler).
			AddEventHandler(bnhandler.BalanceUpdate, bnhandler.BalanceUpdateHandler)
		err = _bc.Run(ctx)
		if err != nil {
			logger.Error("统一账户连接失败", "error", err)
			return nil, nil, err
		}

		ac = _ac

		// 创建全市场客户端
		err = bnclient.NewMarketClient(
			apiConfig.FuturesStreamWSURL,
			[]string{bn.MiniTickerChan},
			wsclient.DefaultNew(nil, logger),
			data.NewMarketData(),
			marketLogger,
		).AddStreamHandler(
			bnhandler.MiniTicker, bnhandler.MarketBindBroker(b, bnhandler.FuturesMiniTickerUpdatePortfolioBrokerHandler),
		).Run(ctx)
		if err != nil {
			logger.Error("市场ws客户端连接失败", "error", err)
			return nil, nil, err
		}
	}

	// 创建期货数据源监听客户端
	fc := bnclient.NewDataClient(
		apiConfig.FuturesStreamWSURL,
		[]string{bn.DepthUpdateChan, bn.IndexFundingChan},
		wsclient.DefaultNew(nil, logger),
		dataLogger,
	).
		AddStreamHandler(bnhandler.Depth, bnhandler.DataBindAPIClient(ac, bnhandler.FuturesDepthUpdateTriggerHandler)).
		AddStreamHandler(bnhandler.IndexFunding, bnhandler.IndexFundingRateHandler)

	// 创建现货数据源监听客户端
	sc := bnclient.NewDataClient(
		apiConfig.SpotStreamWSURL,
		[]string{bn.DepthUpdateChan},
		wsclient.DefaultNew(nil, logger),
		dataLogger,
	).
		AddStreamHandler(bnhandler.Depth, bnhandler.DataBindAPIClient(ac, bnhandler.SpotDepthUpdateTriggerHandler))

	// 创建合并数据源监听客户端
	dc = bnclient.NewCombinedDataClient(
		fc, sc,
	)
	err = dc.Run(ctx)
	if err != nil {
		logger.Error("error connect data ws", "error", err)
		return nil, nil, err
	}

	// 更新broker
	if error := ac.UpdateBroker(b); error != nil {
		return nil, nil, error
	}

	return dc, ac, nil
}

// 生成币安数据源客户端
func NewBnDataClients(
	ctx context.Context, proxy bool, logger *slog.Logger, dataLogger *slog.Logger,
) (
	dc client.DataClient, ac client.APIClient, err error,
) {
	// 默认api或代理api
	var apiConfig bn.BinanceAPIConfig
	if proxy {
		apiConfig = bn.ProxyBinanceAPI
	} else {
		apiConfig = bn.DefaultBinanceAPI
	}

	// 传统api客户端
	ac = bnclient.NewCostomAPIClient(
		apiConfig.FuturesAPIURL,
		apiConfig.SpotAPIURL,
		api.CostomBrokerAPIs,
		http.DefaultClient,
		logger,
	)

	// 创建期货数据源监听客户端
	fc := bnclient.NewDataClient(
		apiConfig.FuturesStreamWSURL,
		[]string{bn.Depth5Chan, bn.IndexFundingChan},
		wsclient.DefaultNew(nil, logger),
		dataLogger,
	).
		AddStreamHandler(bnhandler.Depth, bnhandler.FuturesPartialDepthTriggerHandler).
		AddStreamHandler(bnhandler.IndexFunding, bnhandler.IndexFundingRateHandler)

	// 创建现货数据源监听客户端
	sc := bnclient.NewDataClient(
		apiConfig.SpotStreamWSURL,
		[]string{bn.Depth5Chan},
		wsclient.DefaultNew(nil, logger),
		dataLogger,
	).
		AddStreamHandler(bnhandler.Depth, bnhandler.SpotPartialDepthTriggerHandler)

	// 创建合并数据源监听客户端
	dc = bnclient.NewCombinedDataClient(
		fc, sc,
	)
	err = dc.Run(ctx)

	if err != nil {
		logger.Error("error connect data ws", "error", err)
		return nil, nil, err
	}

	return dc, ac, nil
}
