package factory

import (
	"GoTrader/internal/bn"
	"GoTrader/internal/ok"
	api "GoTrader/pkg/api/bn"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	bnclient "GoTrader/pkg/client/bn"
	okclient "GoTrader/pkg/client/ok"
	"GoTrader/pkg/data"
	bnhandler "GoTrader/pkg/handler/bn"
	okhandler "GoTrader/pkg/handler/ok"
	"GoTrader/pkg/wsclient"
	"context"
	"io"
	"log/slog"
	"net/http"

	"github.com/gorilla/websocket"
)

// 生成币安,欧意跨所客户端
func NewCrossClients(
	ctx context.Context, bnb *broker.Broker, okb *broker.Broker, proxy bool,
	logger *slog.Logger, bnDataLogger *slog.Logger, okDataLogger *slog.Logger, bnMarketLogger *slog.Logger,
) (
	bndc client.DataClient, bnac client.APIClient,
	okdc client.DataClient, okac client.APIClient,
	err error,
) {
	// 检查日志
	if bnMarketLogger == nil {
		bnMarketLogger = slog.New(slog.NewJSONHandler(io.Discard, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}
	if bnDataLogger == nil {
		bnDataLogger = slog.New(slog.NewJSONHandler(io.Discard, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}
	if okDataLogger == nil {
		okDataLogger = slog.New(slog.NewJSONHandler(io.Discard, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}

	// 默认api或代理api
	var bnApiConfig bn.BinanceAPIConfig
	if proxy {
		bnApiConfig = bn.ProxyBinanceAPI
	} else {
		bnApiConfig = bn.DefaultBinanceAPI
	}

	// 创建api客户端和账户监听客户端

	// 传统api客户端
	_bnac := bnclient.NewCostomAPIClient(
		bnApiConfig.FuturesAPIURL,
		bnApiConfig.SpotAPIURL,
		api.CostomBrokerAPIs,
		http.DefaultClient,
		logger,
	)

	// 传统账户监听客户端
	bnbc :=
		bnclient.NewBrokerClient(
			bnApiConfig.FuturesAccountWSURL,
			wsclient.DefaultNew(nil, logger),
			_bnac.GetFuturesListenKey,
			_bnac.UpdateBroker,
			logger,
		).AddEventHandler(bnhandler.OrderTradeUpdate, bnhandler.OrderTradeUpdateHandler).
			AddEventHandler(bnhandler.AccountUpdate, bnhandler.AccountUpdateHandler).
			AddEventHandler(bnhandler.AccountConfigUpdate, bnhandler.AccountConfigUpdateHandler).
			AddEventHandler(bnhandler.TradeLite, bnhandler.IgnoreHandler).
			AddBroker(bnb)
	err = bnbc.Run(ctx)
	if err != nil {
		logger.Error("传统账户连接失败", "error", err)
		return nil, nil, nil, nil, err
	}
	// 创建全市场客户端
	bnclient.NewMarketClient(
		bnApiConfig.FuturesStreamWSURL,
		[]string{bn.MiniTickerChan},
		wsclient.DefaultNew(nil, bnMarketLogger),
		data.NewMarketData(),
		bnMarketLogger,
	).AddStreamHandler(
		bnhandler.MiniTicker, bnhandler.MarketBindBroker(bnb, bnhandler.FuturesMiniTickerUpdateCostomBrokerHandler),
	).Run(ctx)

	bnac = _bnac

	// 创建期货数据源监听客户端
	bndc = bnclient.NewDataClient(
		bnApiConfig.FuturesStreamWSURL,
		[]string{bn.DepthUpdateChan, bn.IndexFundingChan},
		wsclient.DefaultNew(nil, bnDataLogger),
		bnDataLogger,
	).
		AddStreamHandler(bnhandler.Depth, bnhandler.DataBindAPIClient(bnac, bnhandler.FuturesDepthUpdateTriggerHandler)).
		AddStreamHandler(bnhandler.IndexFunding, bnhandler.IndexFundingRateHandler)
	err = bndc.Run(ctx)

	// 更新broker
	if err := bnac.UpdateBroker(bnb); err != nil {
		return nil, nil, nil, nil, err
	}

	if err != nil {
		logger.Error("error connect data ws", "error", err)
		return nil, nil, nil, nil, err
	}

	// 创建okex数据源监听客户端
	// 默认api或代理api
	var okApiConfig ok.OkexAPIConfig
	if proxy {
		okApiConfig = ok.ProxyOkexAPI
	} else {
		okApiConfig = ok.DefaultOkexAPI
	}

	okWsConfig := wsclient.NewConfig(
		wsclient.WithRetryCount(3),
		wsclient.WithRetryInterval(2),
		wsclient.WithPingInterval(5),
		wsclient.WithPingType(websocket.TextMessage),
		wsclient.WithReadTimeout(10),
		wsclient.WithRestartHour(0),
	)
	// 创建数据源监听客户端
	ddc := okclient.NewDataClient(
		okApiConfig.WsUrlPublic,
		[]string{ok.Depth400Chan, ok.PriceLimitChan},
		wsclient.DefaultNew(okWsConfig, logger),
		okDataLogger,
	)
	ddc.AddStreamHandler(okhandler.Depth400, okhandler.DataBindDataClient(ddc, okhandler.Depth400TriggerHandler)).
		AddStreamHandler(okhandler.PriceLimit, okhandler.PriceLimitHandler)

	// 创建资金费监听客户端
	fdc := okclient.NewDataClient(
		okApiConfig.WsUrlPublic,
		[]string{ok.FundingRateChan, ok.IndexPriceChan},
		wsclient.DefaultNew(okWsConfig, logger),
		okDataLogger,
	).
		AddStreamHandler(okhandler.FundingRate, okhandler.FundingRateHandler).
		AddStreamHandler(okhandler.IndexPrice, okhandler.IndexPriceHandler)

	// 创建合并数据源监听客户端
	okdc = okclient.NewCombinedDataClient(
		ddc,
		fdc,
	)
	err = okdc.Run(ctx)

	if err != nil {
		logger.Error("error connect data ws", "error", err)
		return nil, nil, nil, nil, err
	}

	// 创建api客户端
	okac = okclient.NewAPIClient(
		okApiConfig.BaseAPIUrl,
		http.DefaultClient,
		logger,
	)

	// 更新broker
	err = okac.UpdateBroker(okb)
	if err != nil {
		return nil, nil, nil, nil, err
	}

	// 创建broker监听客户端
	okBrokerWsConfig := wsclient.NewConfig(
		wsclient.WithRetryCount(3),
		wsclient.WithRetryInterval(2),
		wsclient.WithPingInterval(5),
		wsclient.WithPingType(websocket.TextMessage),
		wsclient.WithReadTimeout(10),
		wsclient.WithIgnoreLostReadMsg(false),
		wsclient.WithRestartHour(0),
		// wsclient.WithCheckInterval(30),
	)
	okbc := okclient.NewBrokerClient(
		okApiConfig.WsUrlPrivate,
		wsclient.DefaultNew(okBrokerWsConfig, logger),
		logger,
	).
		AddBroker(okb).
		AddEventHandler(okhandler.OrdersUpdate, okhandler.OrdersPositionUpdateHandler).
		AddEventHandler(okhandler.AccountAndPositionUpdate, okhandler.AccountAndPositionUpdateHandler).
		AddEventHandler(okhandler.AccountUpdate, okhandler.AccountUpdateHandler)
	err = okbc.Run(ctx)

	if err != nil {
		logger.Error("error connect broker ws", "error", err)
		return nil, nil, nil, nil, err
	}

	return bndc, bnac, okdc, okac, nil
}

// 生成币安,欧意跨所数据源客户端, 只包含期货
func NewCrossFuturesDataClients(
	ctx context.Context, proxy bool, logger *slog.Logger, dataLogger *slog.Logger,
) (
	bndc client.DataClient, bnac client.APIClient,
	okdc client.DataClient, okac client.APIClient,
	err error,
) {
	// 默认api或代理api
	var bnApiConfig bn.BinanceAPIConfig
	if proxy {
		bnApiConfig = bn.ProxyBinanceAPI
	} else {
		bnApiConfig = bn.DefaultBinanceAPI
	}

	// 传统api客户端
	bnac = bnclient.NewCostomAPIClient(
		bnApiConfig.FuturesAPIURL,
		bnApiConfig.SpotAPIURL,
		api.CostomBrokerAPIs,
		http.DefaultClient,
		logger,
	)

	// 创建期货数据源监听客户端
	bndc = bnclient.NewDataClient(
		bnApiConfig.FuturesStreamWSURL,
		[]string{bn.Depth5Chan, bn.IndexFundingChan},
		wsclient.DefaultNew(nil, logger),
		dataLogger,
	).
		AddStreamHandler(bnhandler.Depth, bnhandler.FuturesPartialDepthTriggerHandler).
		AddStreamHandler(bnhandler.IndexFunding, bnhandler.IndexFundingRateHandler)

	err = bndc.Run(ctx)
	if err != nil {
		logger.Error("error connect data ws", "error", err)
		return nil, nil, nil, nil, err
	}

	// 创建okex数据源监听客户端
	// 默认api或代理api
	var okApiConfig ok.OkexAPIConfig
	if proxy {
		okApiConfig = ok.ProxyOkexAPI
	} else {
		okApiConfig = ok.DefaultOkexAPI
	}

	okWsConfig := wsclient.NewConfig(
		wsclient.WithRetryCount(3),
		wsclient.WithRetryInterval(2),
		wsclient.WithPingInterval(5),
		wsclient.WithPingType(websocket.TextMessage),
		wsclient.WithReadTimeout(10),
		wsclient.WithRestartHour(0),
		// wsclient.WithCheckInterval(30),
	)
	// 创建数据源监听客户端
	okddc := okclient.NewDataClient(
		okApiConfig.WsUrlPublic,
		[]string{ok.Depth5Chan},
		wsclient.DefaultNew(okWsConfig, logger),
		dataLogger,
	)
	okddc.AddStreamHandler(okhandler.Depth5, okhandler.Depth5TriggerHandler)

	// 创建资金费监听客户端
	okfdc := okclient.NewDataClient(
		okApiConfig.WsUrlPublic,
		[]string{ok.FundingRateChan, ok.IndexPriceChan},
		wsclient.DefaultNew(okWsConfig, logger),
		dataLogger,
	).
		AddStreamHandler(okhandler.FundingRate, okhandler.FundingRateHandler).
		AddStreamHandler(okhandler.IndexPrice, okhandler.IndexPriceHandler)

	// 创建合并数据源监听客户端
	okdc = okclient.NewCombinedDataClient(
		okddc,
		okfdc,
	)
	err = okdc.Run(ctx)

	if err != nil {
		logger.Error("error connect data ws", "error", err)
		return nil, nil, nil, nil, err
	}

	// 创建api客户端
	okac = okclient.NewAPIClient(
		okApiConfig.BaseAPIUrl,
		http.DefaultClient,
		logger,
	)

	return bndc, bnac, okdc, okac, nil
}
