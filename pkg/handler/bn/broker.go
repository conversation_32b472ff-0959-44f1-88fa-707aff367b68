package bn

import (
	"GoTrader/pkg/broker"
	"encoding/json"
	"fmt"
	"log/slog"
)

// 推送期货订单更新
func OrderTradeUpdateHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	var orderTradeUpdateEvent OrderTradeUpdateEvent
	err = json.Unmarshal(message, &orderTradeUpdateEvent)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	orderTradeUpdateEvent.UpdateBroker(b)
	logger.Info("Futures Order Update", "event", orderTradeUpdateEvent)
	return nil
}

func ExecutionHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	// 推送现货订单更新
	var marginOrderExecutionEvent ExecutionEvent
	// 打印message
	err = json.Unmarshal(message, &marginOrderExecutionEvent)
	if err != nil {
		logger.Error("Margin Error unmarshalling JSON:", "error", err)
		return err
	}
	marginOrderExecutionEvent.UpdateBroker(b)
	logger.Info("Margin Order Update", "event", marginOrderExecutionEvent)
	return nil
}

// 推送仓位更新
func OutboundAccountPositionHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	var outboundAccountPosition OutboundAccountPositionEvent
	err = json.Unmarshal(message, &outboundAccountPosition)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	outboundAccountPosition.UpdateBroker(b)
	logger.Info("Account Balance Update", "event", outboundAccountPosition)
	return nil
}

// 期货账户更新
func AccountUpdateHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	var accountUpdateEvent AccountUpdateEvent
	err = json.Unmarshal(message, &accountUpdateEvent)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	accountUpdateEvent.UpdateBroker(b)
	logger.Info("Account Update", "event", accountUpdateEvent)
	return nil
}

// 账户配置更新
func AccountConfigUpdateHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	var accountConfigUpdateEvent AccountConfigUpdateEvent
	err = json.Unmarshal(message, &accountConfigUpdateEvent)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	accountConfigUpdateEvent.UpdateBroker(b)
	logger.Info("Account Config Update", "event", accountConfigUpdateEvent)
	return nil
}

// listenkey过期
func ListenKeyExpiredHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	logger.Warn("ListenKey Expired !!")
	return fmt.Errorf("listenkey expired")
}

// 现货账户余额更新
func BalanceUpdateHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	var e BalanceUpdateEvent
	err = json.Unmarshal(message, &e)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	e.UpdateBroker(b)
	logger.Info("Account Config Update", "event", e)
	return nil
}

// 忽略动作
func IgnoreHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	logger.Info("Ignore Event", "message", string(message))
	return nil
}
