package bn

import (
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/utils"
	"encoding/json"
	"log/slog"
	"strconv"
	"time"
)

// 将handler绑定api客户端
func DataBindAPIClient(
	apiClient client.APIClient,
	f func(message []byte, apiC client.APIClient, d *data.SymbolData, logger *slog.Logger) error,
) func([]byte, *data.SymbolData, *slog.Logger) error {
	return func(msg []byte, d *data.SymbolData, logger *slog.Logger) error {
		return f(msg, apiClient, d, logger)
	}
}

func FuturesAggTradeHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送聚合行情更新数据
	d.Lock()
	defer func() {
		d.Unlock()
		// 触发更新
		d.TriggerUpdate(logger)
	}()
	var futuresAggTrade AggTradeEventStream
	err = json.Unmarshal(message, &futuresAggTrade)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	fprice, _ := strconv.ParseFloat(futuresAggTrade.Data.Price, 64)
	volume, _ := strconv.ParseFloat(futuresAggTrade.Data.Quantity, 64)
	d.Price.EnQueue(fprice)
	d.Volume.EnQueue(volume)
	d.Time.EnQueue(float64(futuresAggTrade.Data.Time))
	return nil
}

func SpotAggTradeHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送聚合行情更新数据
	d.Lock()
	defer func() {
		d.Unlock()
		// 触发更新
		d.TriggerUpdate(logger)
	}()
	var spotAggTrade AggTradeEventStream
	err = json.Unmarshal(message, &spotAggTrade)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	sprice, _ := strconv.ParseFloat(spotAggTrade.Data.Price, 64)
	volume, _ := strconv.ParseFloat(spotAggTrade.Data.Quantity, 64)
	d.Price.EnQueue(sprice)
	d.Volume.EnQueue(volume)
	d.Time.EnQueue(float64(spotAggTrade.Data.Time))
	return nil
}

func IndexFundingRateHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送标记价格和资金费率更新数据
	d.Lock()
	defer d.Unlock()
	var fundingRate FuturesFundingRateEventStream
	err = json.Unmarshal(message, &fundingRate)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	d.Funding.Rate, _ = strconv.ParseFloat(fundingRate.Data.Rate, 64)
	indexPrice, _ := strconv.ParseFloat(fundingRate.Data.IndexPrice, 64)
	d.Index.EnQueue(indexPrice)
	// 资金费结算时间大于上次结算时间, 说明是新的一轮结算, 更新上一次结算时间
	if fundingRate.Data.FundingTime > d.Funding.Time {
		d.Funding.PrevTime = d.Funding.Time
	}
	d.Funding.Time = fundingRate.Data.FundingTime
	return nil
}

func FuturesPartialDepthHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送部分深度更新数据
	d.Lock()
	defer d.Unlock()
	// logger.Info("FuturesPartialDepthHandler", "message", string(message))
	var partialDepth FuturesPartialDepthEventStream
	err = json.Unmarshal(message, &partialDepth)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}

	bids := []*data.OrderBook{}
	asks := []*data.OrderBook{}
	for _, bid := range partialDepth.Data.Bids {
		bidPrice, _ := strconv.ParseFloat(bid[0], 64)
		bidVolume, _ := strconv.ParseFloat(bid[1], 64)
		bids = append(bids, &data.OrderBook{Price: bidPrice, Size: bidVolume})
	}
	for _, ask := range partialDepth.Data.Asks {
		askPrice, _ := strconv.ParseFloat(ask[0], 64)
		askVolume, _ := strconv.ParseFloat(ask[1], 64)
		asks = append(asks, &data.OrderBook{Price: askPrice, Size: askVolume})
	}
	d.OrderBooks.Bids = bids
	d.OrderBooks.Asks = asks
	d.Time.EnQueue(float64(partialDepth.Data.Time))
	return nil
}

// 处理期货部分深度数据, 并触发数据更新
func FuturesPartialDepthTriggerHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	err = FuturesPartialDepthHandler(message, d, logger)
	if err != nil {
		logger.Error("Error handling futures partial depth:", "error", err)
		return err
	}
	// 触发更新
	d.TriggerUpdate(logger)
	return nil
}

func SpotPartialDepthHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送部分深度更新数据
	d.Lock()
	defer d.Unlock()
	var partialDepth SpotPartialDepthEventStream
	err = json.Unmarshal(message, &partialDepth)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return nil
	}

	bids := []*data.OrderBook{}
	asks := []*data.OrderBook{}
	for _, bid := range partialDepth.Data.Bids {
		bidPrice, _ := strconv.ParseFloat(bid[0], 64)
		bidVolume, _ := strconv.ParseFloat(bid[1], 64)
		bids = append(bids, &data.OrderBook{Price: bidPrice, Size: bidVolume})
	}
	for _, ask := range partialDepth.Data.Asks {
		askPrice, _ := strconv.ParseFloat(ask[0], 64)
		askVolume, _ := strconv.ParseFloat(ask[1], 64)
		asks = append(asks, &data.OrderBook{Price: askPrice, Size: askVolume})
	}
	d.OrderBooks.Bids = bids
	d.OrderBooks.Asks = asks

	// 由于数据不带时间戳, 添加本地时间
	d.Time.EnQueue(float64(time.Now().UnixMilli()))
	return nil
}

// 处理现货部分深度数据, 并触发数据更新
func SpotPartialDepthTriggerHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	err = SpotPartialDepthHandler(message, d, logger)
	if err != nil {
		logger.Error("Error handling spot partial depth:", "error", err)
		return err
	}
	// 触发更新
	d.TriggerUpdate(logger)
	return nil
}

// 期货推送深度更新数据
func FuturesDepthUpdateTriggerHandler(message []byte, apiC client.APIClient, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 检查耗时
	d.OrderBooks.Lock()
	defer d.OrderBooks.Unlock()
	var depthUpdate DepthUpdateEventStream
	logger = logger.With("symbol", d.Symbol)
	err = json.Unmarshal(message, &depthUpdate)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}

	// 增量更新检查
	if !d.OrderBooks.Snap {
		// 增量更新丢包
		if depthUpdate.Data.PrevLastUpdateId != d.OrderBooks.UpdateId {
			logger.Info("Depth update expired, fetching from api", "prev", depthUpdate.Data.PrevLastUpdateId, "current", d.OrderBooks.UpdateId)
			d.OrderBooks.Unlock()
			apiC.UpdateDepth(d)
			d.OrderBooks.Lock()
		}
		// 推送过期, 忽略当前信息
		if depthUpdate.Data.LastUpdateId < d.OrderBooks.UpdateId {
			logger.Info("Depth update outdated", "last", depthUpdate.Data.LastUpdateId, "current", d.OrderBooks.UpdateId)
			return nil
		}
	}

	// 快照更新检查
	if d.OrderBooks.Snap {
		// 快照信息落后, 重新获取快照
		if depthUpdate.Data.FirstUpdateId > d.OrderBooks.UpdateId {
			logger.Info("Depth Snap outdated")
			d.OrderBooks.Snap = false
			return
		}
		// 增量信息落后, 忽略当前信息
		if depthUpdate.Data.LastUpdateId < d.OrderBooks.UpdateId {
			logger.Info("Depth update outdated", "last", depthUpdate.Data.LastUpdateId, "current", d.OrderBooks.UpdateId)
			return
		}
		// 开始增量更新
		d.OrderBooks.Snap = false
	}

	newBids, newAsks := futuresDepthUpdateSnap(depthUpdate, d)

	// 做一次校验, 新的盘口需要满足一定规则
	if !checkOrderBook(newBids, newAsks) {
		logger.Error("Depth update invalid", "oldBids", d.OrderBooks.Bids, "oldAsks", d.OrderBooks.Asks, "updateInfo", depthUpdate.Data, "newBids", newBids, "newAsks", newAsks)
		return nil
	}

	d.OrderBooks.Bids = newBids
	d.OrderBooks.Asks = newAsks
	d.OrderBooks.UpdateId = depthUpdate.Data.LastUpdateId
	d.Time.EnQueue(float64(depthUpdate.Data.EventTime))
	// 触发
	d.TriggerUpdate(logger)
	return nil
}

// 现货推送深度更新数据
func SpotDepthUpdateTriggerHandler(message []byte, apiC client.APIClient, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 检查耗时
	d.OrderBooks.Lock()
	defer d.OrderBooks.Unlock()
	var depthUpdate DepthUpdateEventStream
	logger = logger.With("symbol", d.Symbol)
	err = json.Unmarshal(message, &depthUpdate)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}

	// 增量更新检查
	if !d.OrderBooks.Snap {
		// 增量更新丢包
		if depthUpdate.Data.FirstUpdateId != d.OrderBooks.UpdateId+1 {
			logger.Info("Depth update expired, fetching from api", "first", depthUpdate.Data.FirstUpdateId, "current", d.OrderBooks.UpdateId)
			d.OrderBooks.Unlock()
			apiC.UpdateDepth(d)
			d.OrderBooks.Lock()
		}
		// 推送过期, 忽略当前信息
		if depthUpdate.Data.LastUpdateId < d.OrderBooks.UpdateId {
			logger.Info("Depth update outdated", "last", depthUpdate.Data.LastUpdateId, "current", d.OrderBooks.UpdateId)
			return nil
		}
	}

	// 快照更新检查
	if d.OrderBooks.Snap {
		// 快照信息落后, 重新获取快照
		if depthUpdate.Data.FirstUpdateId > d.OrderBooks.UpdateId+1 {
			logger.Info("Depth Snap outdated")
			d.OrderBooks.Snap = false
			return
		}
		// 增量信息落后, 忽略当前信息
		if depthUpdate.Data.LastUpdateId < d.OrderBooks.UpdateId {
			logger.Info("Depth update outdated", "last", depthUpdate.Data.LastUpdateId, "current", d.OrderBooks.UpdateId)
			return
		}
		// 开始增量更新
		d.OrderBooks.Snap = false
	}

	newBids, newAsks := spotDepthUpdateSnap(depthUpdate, d)

	// 做一次校验, 新的盘口需要满足一定规则
	if !checkOrderBook(newBids, newAsks) {
		logger.Error("Depth update invalid", "oldBids", d.OrderBooks.Bids, "oldAsks", d.OrderBooks.Asks, "updateInfo", depthUpdate.Data, "newBids", newBids, "newAsks", newAsks)
		return nil
	}

	d.OrderBooks.Bids = newBids
	d.OrderBooks.Asks = newAsks
	d.OrderBooks.UpdateId = depthUpdate.Data.LastUpdateId
	d.Time.EnQueue(float64(depthUpdate.Data.EventTime))
	// 触发
	d.TriggerUpdate(logger)
	return nil
}

// 校验盘口数据是否合规
func checkOrderBook(bids []*data.OrderBook, asks []*data.OrderBook) bool {
	if len(bids) == 0 || len(asks) == 0 {
		return false
	}
	if bids[0].Price > asks[0].Price {
		return false
	}
	return true
}

// 期货增量更新快照
// bn期货推送的bid是正序排列, 因此需要从后往前遍历
func futuresDepthUpdateSnap(depthUpdate DepthUpdateEventStream, d *data.SymbolData) (newBids []*data.OrderBook, newAsks []*data.OrderBook) {
	// 旧盘口index
	bidindex := 0
	askindex := 0

	// 更新bid, 并更新bidindex
	// for _, bid := range depthUpdate.Data.Bids {
	for i := len(depthUpdate.Data.Bids) - 1; i >= 0; i-- {
		bid := depthUpdate.Data.Bids[i]
		updateBid := &data.OrderBook{}
		updateBid.Price, _ = strconv.ParseFloat(bid[0], 64)
		updateBid.Size, _ = strconv.ParseFloat(bid[1], 64)
		// snap先耗尽
		if len(d.OrderBooks.Bids) <= bidindex {
			// 剩余的增量盘口直接添加
			if updateBid.Size != 0 {
				newBids = append(newBids, updateBid)
			}
			continue
		}
		// snap未耗尽
		for _, snapBid := range d.OrderBooks.Bids[bidindex:] {
			if snapBid.Price > updateBid.Price {
				// snap价格更高(更浅), 添加当前snap价格
				if snapBid.Size != 0 {
					newBids = append(newBids, snapBid)
				}
				bidindex += 1
				continue
			}
			if snapBid.Price < updateBid.Price && updateBid.Size != 0 {
				// update价格更高(更浅), 添加当前update价格
				newBids = append(newBids, updateBid)
				break
			}
			if snapBid.Price == updateBid.Price && updateBid.Size != 0 {
				// 两者价格一致, 且size不为0, 添加update价格
				newBids = append(newBids, updateBid)
				bidindex += 1
				break
			}
			if snapBid.Price == updateBid.Price && updateBid.Size == 0 {
				// 两者价格一致, 且size为0, 跳过当前价格
				bidindex += 1
				break
			}
		}
	}
	// 剩余的snap添加
	if len(d.OrderBooks.Bids) > bidindex {
		newBids = append(newBids, d.OrderBooks.Bids[bidindex:]...)
	}

	// 更新ask, 并更新askindex
	for _, ask := range depthUpdate.Data.Asks {
		updateAsk := &data.OrderBook{}
		updateAsk.Price, _ = strconv.ParseFloat(ask[0], 64)
		updateAsk.Size, _ = strconv.ParseFloat(ask[1], 64)
		// snap先耗尽
		if len(d.OrderBooks.Asks) <= askindex {
			// 剩余的盘口直接添加
			newAsks = append(newAsks, updateAsk)
			continue
		}
		// snap未耗尽
		for _, snapAsk := range d.OrderBooks.Asks[askindex:] {
			if snapAsk.Price < updateAsk.Price {
				// snap价格更低(更浅), 添加当前snap价格
				if snapAsk.Size != 0 {
					newAsks = append(newAsks, snapAsk)
				}
				askindex += 1
				continue
			}
			if snapAsk.Price > updateAsk.Price && updateAsk.Size != 0 {
				// update价格更低(更浅), 添加当前update价格
				newAsks = append(newAsks, updateAsk)
				break
			}
			if snapAsk.Price == updateAsk.Price && updateAsk.Size != 0 {
				// 两者价格一致, 且size不为0, 添加update价格
				newAsks = append(newAsks, updateAsk)
				askindex += 1
				break
			}
			if snapAsk.Price == updateAsk.Price && updateAsk.Size == 0 {
				// 两者价格一致, 且size为0, 跳过当前价格
				askindex += 1
				break
			}
		}
	}
	// 剩余的snap添加
	if len(d.OrderBooks.Asks) > askindex {
		newAsks = append(newAsks, d.OrderBooks.Asks[askindex:]...)
	}

	return newBids, newAsks
}

// 现货增量更新快照
// bn现货推送的bid是倒序排列, 从前往后遍历
func spotDepthUpdateSnap(depthUpdate DepthUpdateEventStream, d *data.SymbolData) (newBids []*data.OrderBook, newAsks []*data.OrderBook) {
	// 旧盘口index
	bidindex := 0
	askindex := 0

	// 更新bid, 并更新bidindex
	for _, bid := range depthUpdate.Data.Bids {
		updateBid := &data.OrderBook{}
		updateBid.Price, _ = strconv.ParseFloat(bid[0], 64)
		updateBid.Size, _ = strconv.ParseFloat(bid[1], 64)
		// snap先耗尽
		if len(d.OrderBooks.Bids) <= bidindex {
			// 剩余的增量盘口直接添加
			newBids = append(newBids, updateBid)
			continue
		}
		// snap未耗尽
		for _, snapBid := range d.OrderBooks.Bids[bidindex:] {
			if snapBid.Price > updateBid.Price {
				// snap价格更高(更浅), 添加当前snap价格
				if snapBid.Size != 0 {
					newBids = append(newBids, snapBid)
				}
				bidindex += 1
				continue
			}
			if snapBid.Price < updateBid.Price && updateBid.Size != 0 {
				// update价格更高(更浅), 添加当前update价格
				newBids = append(newBids, updateBid)
				break
			}
			if utils.StepEqual(snapBid.Price, updateBid.Price, d.Info.TickSize) && updateBid.Size != 0 {
				// 两者价格一致, 且size不为0, 添加update价格
				newBids = append(newBids, updateBid)
				bidindex += 1
				break
			}
			if utils.StepEqual(snapBid.Price, updateBid.Price, d.Info.TickSize) && updateBid.Size == 0 {
				// 两者价格一致, 且size为0, 跳过当前价格
				bidindex += 1
				break
			}
		}
	}
	// 剩余的snap添加
	if len(d.OrderBooks.Bids) > bidindex {
		newBids = append(newBids, d.OrderBooks.Bids[bidindex:]...)
	}

	// 更新ask, 并更新askindex
	for _, ask := range depthUpdate.Data.Asks {
		updateAsk := &data.OrderBook{}
		updateAsk.Price, _ = strconv.ParseFloat(ask[0], 64)
		updateAsk.Size, _ = strconv.ParseFloat(ask[1], 64)
		// snap先耗尽
		if len(d.OrderBooks.Asks) <= askindex {
			// 剩余的盘口直接添加
			newAsks = append(newAsks, updateAsk)
			continue
		}
		// snap未耗尽
		for _, snapAsk := range d.OrderBooks.Asks[askindex:] {
			if snapAsk.Price < updateAsk.Price {
				// snap价格更低(更浅), 添加当前snap价格
				if snapAsk.Size != 0 {
					newAsks = append(newAsks, snapAsk)
				}
				askindex += 1
				continue
			}
			if snapAsk.Price > updateAsk.Price && updateAsk.Size != 0 {
				// update价格更低(更浅), 添加当前update价格
				newAsks = append(newAsks, updateAsk)
				break
			}
			if utils.StepEqual(snapAsk.Price, updateAsk.Price, d.Info.TickSize) && updateAsk.Size != 0 {
				// 两者价格一致, 且size不为0, 添加update价格
				newAsks = append(newAsks, updateAsk)
				askindex += 1
				break
			}
			if utils.StepEqual(snapAsk.Price, updateAsk.Price, d.Info.TickSize) && updateAsk.Size == 0 {
				// 两者价格一致, 且size为0, 跳过当前价格
				askindex += 1
				break
			}
		}
	}
	// 剩余的snap添加
	if len(d.OrderBooks.Asks) > askindex {
		newAsks = append(newAsks, d.OrderBooks.Asks[askindex:]...)
	}

	return newBids, newAsks
}
