package bn

import "GoTrader/pkg/handler"

const (
	MiniTicker handler.WSStreamType = "!miniTicker@arr" // 全市场精简tiker
)

type MiniTickerEventStream struct {
	Stream string `json:"stream"`
	Data   []struct {
		Event     string `json:"e"`
		EventTime int64  `json:"E"`
		Symbol    string `json:"s"`
		Close     string `json:"c"`
		Open      string `json:"o"`
		High      string `json:"h"`
		Low       string `json:"l"`
		Volume    string `json:"v"`
		Quote     string `json:"q"`
	} `json:"data"`
}
