package bn

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/data"
	"encoding/json"
	"log/slog"
	"strconv"
)

// 将handler绑定broker参数
func MarketBindBroker(
	b *broker.Broker,
	f func([]byte, *broker.Broker, *data.MarketData, *slog.Logger) error,
) func([]byte, *data.MarketData, *slog.Logger) error {
	return func(msg []byte, d *data.MarketData, logger *slog.Logger) error {
		return f(msg, b, d, logger)
	}
}

// 传统账户根据全市场价格更新余额和维持保证金
// 实际余额 = 计算余额 + unrealized pnl
func FuturesMiniTickerUpdateCostomBrokerHandler(message []byte, b *broker.Broker, md *data.MarketData, logger *slog.Logger) (err error) {
	logger = logger.With("handler", "FuturesMiniTickerUpdateCostomBrokerHandler")
	var miniTicker MiniTickerEventStream
	err = json.Unmarshal(message, &miniTicker)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	for _, d := range miniTicker.Data {
		md.Prices[d.Symbol], _ = strconv.ParseFloat(d.Close, 64)
	}
	b.Lock()
	defer b.Unlock()
	balance := b.Account.CashBalance
	// 更新broker equity 和 available
	equity := balance
	marginOpenLoss := 0.0
	for _, position := range b.Positions {
		margin := (position.SizeLong*md.Prices[position.Symbol] - position.SizeShort*md.Prices[position.Symbol]) / float64(b.Leverages[position.Symbol])
		pnl := position.SizeLong*(md.Prices[position.Symbol]-position.OpenPriceLong) + position.SizeShort*(md.Prices[position.Symbol]-position.OpenPriceShort)
		equity += pnl
		marginOpenLoss += margin
	}
	for _, order := range b.FuturesPending.Pending {
		if order.Alive() && order.IsOpen() {
			marginOpenLoss += (order.Size - order.ExecutedSize) * order.Price / float64(b.Leverages[order.Symbol])
		}
	}
	b.Account.MarginOpenLoss = marginOpenLoss
	b.Account.Equity = equity
	b.Account.Available = b.Account.Equity - b.Account.MarginOpenLoss
	// 更新broker maintenance margin
	maintMargin := 0.0
	for _, position := range b.Positions {
		size := position.SizeLong - position.SizeShort
		if size == 0 {
			continue
		}
		maintMarginSymbol := 0.0
		notional := size * md.Prices[position.Symbol]
		for _, tier := range b.PositionTiers[position.Symbol] {
			if notional >= tier.MinNotional && notional <= tier.MaxNotional {
				maintMarginSymbol = notional*tier.MaintMarginRatio - tier.Cum
				break
			}
		}
		if maintMarginSymbol <= 0 {
			// 缺少有效范围
			// logger.Warn("Missing valid range for position size to calculate maintMargin",
			// 	"symbol", position.Symbol,
			// 	"size", size,
			// 	"tier", b.PositionTiers[position.Symbol],
			// 	"maintMarginSymbol", maintMarginSymbol,
			// 	"mdPrice", md.Prices[position.Symbol],
			// )
			maintMarginSymbol = md.Prices[position.Symbol]*size*0.01 - 0.0 // TODO 假设质押率为10%
		}
		maintMargin += maintMarginSymbol
	}
	b.Account.MaintMargin = maintMargin
	return nil
}

// TODO 统一账户根据全市场价格更新余额
func FuturesMiniTickerUpdatePortfolioBrokerHandler(message []byte, b *broker.Broker, md *data.MarketData, logger *slog.Logger) (err error) {
	var miniTicker MiniTickerEventStream
	err = json.Unmarshal(message, &miniTicker)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	for _, d := range miniTicker.Data {
		md.Prices[d.Symbol], _ = strconv.ParseFloat(d.Close, 64)
	}
	b.Lock()
	defer b.Unlock()
	// 更新broker equity
	var equity float64 = 0.0
	var um float64 = 0.0
	for asset, wallet := range b.AssetWallets {
		if asset == "USDT" {
			equity += wallet.Free
			um += wallet.UM
			continue
		}
		symbol := asset + "USDT"
		equity += wallet.Free * md.Prices[symbol] * 0.95 // TODO 假设质押率为95%
		um += wallet.UM * md.Prices[symbol] * 0.95       // TODO 假设质押率为95%
	}
	b.Account.Equity = equity
	// 更新broker available
	available := equity + um
	for _, position := range b.Positions {
		margin := (position.SizeLong*position.OpenPriceLong - position.SizeShort*position.OpenPriceShort) / float64(b.Leverages[position.Symbol])
		pnl := position.SizeLong*(md.Prices[position.Symbol]-position.OpenPriceLong) + position.SizeShort*(md.Prices[position.Symbol]-position.OpenPriceShort)
		available -= margin + pnl
	}
	for _, order := range b.FuturesPending.Pending {
		if order.Alive() && order.IsOpen() {
			margin := (order.Size - order.ExecutedSize) * order.Price / float64(b.Leverages[order.Symbol])
			available -= margin
		}
	}
	b.Account.Available = available
	return nil
}
