package bn

import "GoTrader/pkg/handler"

const (
	AggTrade     handler.WSStreamType = "aggTrade"
	Trade        handler.WSStreamType = "trade"
	IndexFunding handler.WSStreamType = "markPriceUpdate"
	Depth        handler.WSStreamType = "depthUpdate"
)

type AggTradeEventStream struct {
	Stream string `json:"stream"`
	Data   struct {
		Event     string `json:"e"`
		EventTime int64  `json:"E"`
		Symbol    string `json:"s"`
		Price     string `json:"p"`
		Quantity  string `json:"q"`
		Time      int64  `json:"T"`
	} `json:"data"`
}

type FuturesFundingRateEventStream struct {
	Stream string `json:"stream"`
	Data   struct {
		Event                string `json:"e"`
		Time                 int64  `json:"E"`
		Symbol               string `json:"s"`
		MarkPrice            string `json:"p"` // 标记价格
		IndexPrice           string `json:"i"` // 指数价格
		EstimatedSettlePrice string `json:"P"` // 预估结算价
		Rate                 string `json:"r"`
		FundingTime          int64  `json:"T"`
	} `json:"data"`
}

type SpotPartialDepthEventStream struct {
	Stream string `json:"stream"`
	Data   struct {
		Bids [][]string `json:"bids"`
		Asks [][]string `json:"asks"`
	} `json:"data"`
}

type FuturesPartialDepthEventStream struct {
	Stream string `json:"stream"`
	Data   struct {
		Time int64      `json:"T"`
		Bids [][]string `json:"b"`
		Asks [][]string `json:"a"`
	} `json:"data"`
}

type DepthUpdateEventStream struct {
	Stream string `json:"stream"`
	Data   struct {
		Event            string     `json:"e"`
		EventTime        int64      `json:"E"`
		FirstUpdateId    int64      `json:"U"`
		LastUpdateId     int64      `json:"u"`
		PrevLastUpdateId int64      `json:"pu"`
		Bids             [][]string `json:"b"`
		Asks             [][]string `json:"a"`
	} `json:"data"`
}
