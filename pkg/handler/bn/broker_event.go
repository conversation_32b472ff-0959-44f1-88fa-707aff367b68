package bn

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/handler"
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	"fmt"
	"strconv"
	"strings"
)

const (
	ExecutionReport         handler.WSEventType = "executionReport"         // 现货订单更新
	OutboundAccountPosition handler.WSEventType = "outboundAccountPosition" // 现货账户更新
	BalanceUpdate           handler.WSEventType = "balanceUpdate"           // 现货余额更新

	OrderTradeUpdate    handler.WSEventType = "ORDER_TRADE_UPDATE"    // 期货订单更新
	AccountUpdate       handler.WSEventType = "ACCOUNT_UPDATE"        // 期货账户更新
	AccountConfigUpdate handler.WSEventType = "ACCOUNT_CONFIG_UPDATE" // 账户配置更新
	TradeLite           handler.WSEventType = "TRADE_LITE"            // 期货订单轻量推送

	OpenOrderLoss    handler.WSEventType = "openOrderLoss"    // 杠杆账户全仓挂单占用
	ListenKeyExpired handler.WSEventType = "listenKeyExpired" // listenkey过期
)

// 期货订单推送事件
type OrderTradeUpdateEvent struct {
	Event           string `json:"e"`
	EventTime       int64  `json:"E"`
	TransactionTime int64  `json:"T"`
	Order           struct {
		Symbol        string `json:"s"`
		OrderId       int64  `json:"i"`
		ClOrdId       string `json:"c"`
		Side          string `json:"S"`
		OrdType       string `json:"o"`
		TimeInForce   string `json:"f"`
		Size          string `json:"q"`
		Price         string `json:"p"`
		AvgPrice      string `json:"ap"`
		ExecuteType   string `json:"x"`
		Status        string `json:"X"`
		ExecutedSize  string `json:"z"`
		ExecutedTime  int64  `json:"T"`
		TradeId       int64  `json:"t"`
		LastFillPrice string `json:"L"`
		LastFillSize  string `json:"l"`
		Maker         bool   `json:"m"`
		PosSide       string `json:"ps"`
		CommAsset     string `json:"N"`
		Commission    string `json:"n"`
		Profit        string `json:"rp"`
	} `json:"o"`
}

// 期货订单轻量推送事件
type TradeLiteEvent struct {
	Event           string `json:"e"`
	EventTime       int64  `json:"E"`
	TransactionTime int64  `json:"T"`
	Symbol          string `json:"s"`
	Quantity        string `json:"q"`
	Price           string `json:"p"`
	IsMaker         bool   `json:"m"`
	ClOrdId         string `json:"c"`
	Side            string `json:"S"`
	LastFillPrice   string `json:"L"`
	LastFillSize    string `json:"l"`
	TradeId         string `json:"t"`
	OrderId         int64  `json:"i"`
}

// 杠杆账户更新事件
type ExecutionEvent struct {
	Event              string `json:"e"`
	EventTime          int64  `json:"E"`
	Symbol             string `json:"s"`
	OrdId              string `json:"c"`
	Side               string `json:"S"`
	OrdType            string `json:"o"`
	TimeInForce        string `json:"f"`
	Size               string `json:"q"`
	Price              string `json:"p"`
	StopPrice          string `json:"P"`
	ExecuteType        string `json:"x"`
	Status             string `json:"X"`
	ExecutedSize       string `json:"z"`
	ExecutedQuote      string `json:"Z"`
	ExecutedTime       int64  `json:"T"`
	TradeId            int64  `json:"t"`
	LastFillPrice      string `json:"L"`
	LastFillSize       string `json:"l"`
	ExecutionId        int64  `json:"I"`
	Maker              bool   `json:"m"`
	CommAsset          string `json:"N"`
	Commission         string `json:"n"`
	CreatedTime        int64  `json:"O"`
	QuoteOrderQuantity string `json:"Q"`
}

// 统一账户杠杆全仓挂单占用事件
type OpenOrderLossEvent struct {
	Event     string `json:"e"`
	EventTime int64  `json:"E"`
	Orders    []struct {
		Asset  string `json:"a"`
		Amount string `json:"o"`
	} `json:"O"`
}

// 账户更新事件
// 每当帐户余额发生更改时，都会发送一个事件outboundAccountPosition，其中包含可能由生成余额变动的事件而变动的资产。
type OutboundAccountPositionEvent struct {
	Event      string `json:"e"`
	EventTime  int64  `json:"E"`
	UptadeTime int64  `json:"u"`
	Balances   []struct {
		Asset  string `json:"a"`
		Free   string `json:"f"`
		Locked string `json:"l"`
	} `json:"B"`
}

// 账户合约Balance和Position更新事件
type AccountUpdateEvent struct {
	Event           string `json:"e"`
	EventTime       int64  `json:"E"`
	TransactionTime int64  `json:"T"`
	Data            struct {
		Reason   string `json:"m"`
		Balances []struct {
			Asset   string `json:"a"`
			Balance string `json:"wb"`
		} `json:"B"`
		Positions []struct {
			Symbol    string `json:"s"`
			PosSide   string `json:"ps"`
			Size      string `json:"pa"`
			OpenPrice string `json:"ep"`
		} `json:"P"`
	} `json:"a"`
}

// 账户配置更新事件
type AccountConfigUpdateEvent struct {
	Event     string `json:"e"`
	EventTime int64  `json:"E"`
	Account   struct {
		Symbol   string `json:"s"`
		Leverage int64  `json:"l"`
	} `json:"ac"`
}

// 现货账户余额更新事件
// 账户发生充值或提取 或 交易账户之间发生划转(例如 现货向杠杆账户划转)
type BalanceUpdateEvent struct {
	Event        string `json:"e"`
	EventTime    int64  `json:"E"`
	Asset        string `json:"a"`
	BalanceDelta string `json:"d"`
	ClearTime    int64  `json:"T"`
}

// 更新期货订单状态
func (e *OrderTradeUpdateEvent) UpdateBroker(b *broker.Broker) error {
	b.Lock()
	defer b.Unlock()
	o := b.FuturesPending.Pending[e.Order.ClOrdId]
	symbol := e.Order.Symbol
	if o == nil {
		o = &order.FuturesOrder{
			OrdId:       strconv.FormatInt(e.Order.OrderId, 10),
			ClOrdId:     e.Order.ClOrdId,
			Symbol:      symbol,
			Side:        order.Side(e.Order.Side),
			OrdType:     order.OrdType(e.Order.OrdType),
			TimeInForce: order.TimeInForce(e.Order.TimeInForce),
			PosSide:     order.PosSide(e.Order.PosSide),
		}
		b.FuturesPending.Pending[e.Order.ClOrdId] = o
	}
	errs := make([]error, 0)
	o.Status = order.OrdStatus(e.Order.Status)
	o.ExecutedSize = utils.MustParseFloat64(e.Order.ExecutedSize, &errs)
	o.ExecutedTime = e.Order.ExecutedTime
	o.AvgPrice = utils.MustParseFloat64(e.Order.AvgPrice, &errs)
	o.Price = utils.MustParseFloat64(e.Order.Price, &errs)
	o.Size = utils.MustParseFloat64(e.Order.Size, &errs)
	o.CommAsset = e.Order.CommAsset
	o.Commission = utils.MustParseFloat64(e.Order.Commission, &errs)
	o.Profit = utils.MustParseFloat64(e.Order.Profit, &errs)
	o.Maker = e.Order.Maker

	if o.Status == order.FILLED || o.Status == order.CANCELED || o.Status == order.EXPIRED_IN_MATCH || o.Status == order.EXPIRED {
		// 删除订单
		delete(b.FuturesPending.Pending, o.ClOrdId)
	}
	b.FuturesPending.UpdateTime = e.TransactionTime

	fillSize := utils.MustParseFloat64(e.Order.LastFillSize, &errs)
	// 没有成交, 不需要更新
	if fillSize == 0 {
		return nil
	}
	_, exists := b.Positions[symbol]
	if !exists {
		b.Positions[symbol] = &broker.UMPosition{Symbol: symbol}
	}
	position := b.Positions[symbol]

	fillPrice := utils.MustParseFloat64(e.Order.LastFillPrice, &errs)

	if o.Side == order.SELL {
		fillSize = -fillSize
	}

	// 订单更新时间小于等于仓位校准时间则跳过
	updateTime := e.TransactionTime
	if updateTime <= position.CheckTime {
		return nil
	}

	position.UpdateTime = updateTime
	switch o.PosSide {
	case order.LONG:
		newPos := position.SizeLong + fillSize
		if newPos == 0 {
			position.OpenPriceLong = 0
		} else if o.IsOpen() {
			position.OpenPriceLong = (position.SizeLong*position.OpenPriceLong + fillSize*fillPrice) / newPos
		}
		position.SizeLong = newPos
	case order.SHORT:
		newPos := position.SizeShort + fillSize
		if newPos == 0 {
			position.OpenPriceShort = 0
		} else if o.IsOpen() {
			position.OpenPriceShort = (position.SizeShort*position.OpenPriceShort + fillSize*fillPrice) / newPos
		}
		position.SizeShort = newPos
	}

	return nil
}

// 期货订单轻量推送事件
func (e *TradeLiteEvent) UpdateBroker(b *broker.Broker) error {
	return nil
}

// 更新现货订单状态
func (e *ExecutionEvent) UpdateBroker(b *broker.Broker) error {
	b.Lock()
	defer b.Unlock()
	o := b.MarginPending.Pending[e.OrdId]
	symbol := e.Symbol
	if o == nil {
		o = &order.MarginOrder{
			ClOrdId:     e.OrdId,
			Symbol:      symbol,
			Side:        order.Side(e.Side),
			OrdType:     order.OrdType(e.OrdType),
			TimeInForce: order.TimeInForce(e.TimeInForce),
			CreatedTime: e.CreatedTime,
		}
		b.MarginPending.Pending[e.OrdId] = o
	}
	errs := make([]error, 0)
	o.Status = order.OrdStatus(e.Status)
	o.ExecutedSize = utils.MustParseFloat64(e.ExecutedSize, &errs)
	if o.ExecutedSize != 0 {
		o.AvgPrice = utils.MustParseFloat64(e.ExecutedQuote, &errs) / o.ExecutedSize
	}
	o.ExecutedTime = e.ExecutedTime
	o.Price = utils.MustParseFloat64(e.Price, &errs)
	oldOrderSize := o.Size
	o.Size = utils.MustParseFloat64(e.Size, &errs)
	o.CommAsset = e.CommAsset
	o.Commission = utils.MustParseFloat64(e.Commission, &errs)
	o.Maker = e.Maker

	if o.Status == order.FILLED || o.Status == order.CANCELED || o.Status == order.EXPIRED_IN_MATCH || o.Status == order.EXPIRED {
		delete(b.MarginPending.Pending, o.ClOrdId)
	}
	// 更新事件推送时间
	b.MarginPending.UpdateTime = e.ExecutedTime

	asset := strings.TrimSuffix(symbol, "USDT")
	if _, exists := b.AssetWallets[asset]; !exists {
		b.AssetWallets[asset] = &broker.AssetWallet{Asset: asset}
	}
	wallet := b.AssetWallets[asset]

	updateTime := e.ExecutedTime

	// 如果更新时间小于校验时间则跳过此次更新
	if updateTime <= wallet.CheckTime || updateTime < wallet.UpdateTime {
		return nil
	}
	wallet.UpdateTime = updateTime

	// 计算free, locked
	if o.Side == order.SELL {
		wallet.Locked += o.Size - oldOrderSize
		wallet.Free -= o.Size - oldOrderSize
	}

	fillSize := utils.MustParseFloat64(e.LastFillSize, &errs)

	if o.Side == order.BUY {
		wallet.Free += fillSize
	}
	if o.Side == order.SELL {
		wallet.Locked -= fillSize
	}

	// 已结束生命周期的订单释放locked
	if !(o.Status == order.NEW || o.Status == order.PARTIALLY_FILLED) {
		wallet.Locked -= o.Size - o.ExecutedSize
		wallet.Free += o.Size - o.ExecutedSize
	}

	// 手续费扣除, 只考虑本币
	fillFee := utils.MustParseFloat64(e.Commission, &errs)
	if e.CommAsset == asset {
		wallet.Free -= fillFee
	}

	return nil
}

func (e *OpenOrderLossEvent) UpdateBroker(b *broker.Broker) error {
	return nil
}

// 更新账户杠杆仓位
func (e *OutboundAccountPositionEvent) UpdateBroker(b *broker.Broker) error {
	b.Lock()
	defer b.Unlock()
	for _, item := range e.Balances {
		if _, exists := b.AssetWallets[item.Asset]; !exists {
			b.AssetWallets[item.Asset] = &broker.AssetWallet{Asset: item.Asset}
		}
		// 如果上次更新时间大于事件时间则跳过此次更新
		checkTime := e.UptadeTime
		if checkTime < b.AssetWallets[item.Asset].UpdateTime {
			continue
		}
		errs := make([]error, 0)
		b.AssetWallets[item.Asset].CheckTime = checkTime
		b.AssetWallets[item.Asset].Free = utils.MustParseFloat64(item.Free, &errs)
		b.AssetWallets[item.Asset].Locked = utils.MustParseFloat64(item.Locked, &errs)
		err := utils.CollectErrors(errs)
		if err != nil {
			return err
		}
	}
	return nil
}

// 更新账户信息，包括期货仓位
func (e *AccountUpdateEvent) UpdateBroker(b *broker.Broker) error {
	b.Lock()
	defer b.Unlock()
	for _, item := range e.Data.Balances {
		if _, exists := b.AssetWallets[item.Asset]; !exists {
			b.AssetWallets[item.Asset] = &broker.AssetWallet{Asset: item.Asset}
		}
		// 如果上次更新时间大于事件时间则跳过此次更新
		if b.AssetWallets[item.Asset].UpdateTime > e.TransactionTime {
			continue
		}
		b.AssetWallets[item.Asset].UpdateTime = e.TransactionTime
		b.AssetWallets[item.Asset].UM, _ = strconv.ParseFloat(item.Balance, 64)
	}
	b.Account.CashBalance = b.AssetWallets["USDT"].UM

	for _, item := range e.Data.Positions {
		if _, exists := b.Positions[item.Symbol]; !exists {
			b.Positions[item.Symbol] = &broker.UMPosition{Symbol: item.Symbol}
		}
		posObj := b.Positions[item.Symbol]
		// 如果校验时间小于更新时间则跳过此次更新
		checkTime := e.TransactionTime
		if checkTime < b.Positions[item.Symbol].UpdateTime {
			continue
		}
		b.Positions[item.Symbol].CheckTime = checkTime
		switch posType := order.PosSide(item.PosSide); posType {
		case order.LONG:
			b.Positions[item.Symbol].SizeLong, _ = strconv.ParseFloat(item.Size, 64)
		case order.SHORT:
			b.Positions[item.Symbol].SizeShort, _ = strconv.ParseFloat(item.Size, 64)
		case order.BOTH:
			posObj.SizeLong, _ = strconv.ParseFloat(item.Size, 64)
			posObj.SizeShort, _ = strconv.ParseFloat(item.Size, 64)
			posObj.SizeLong = max(posObj.SizeLong, 0)
			posObj.SizeShort = min(posObj.SizeShort, 0)
		}
		// 开仓价格调整
		if posObj.SizeLong == 0 {
			posObj.OpenPriceLong = 0
		} else {
			posObj.OpenPriceLong, _ = strconv.ParseFloat(item.OpenPrice, 64)
		}
		if posObj.SizeShort == 0 {
			posObj.OpenPriceShort = 0
		} else {
			posObj.OpenPriceShort, _ = strconv.ParseFloat(item.OpenPrice, 64)
		}
	}
	return nil
}

// 账户配置更新
func (e *AccountConfigUpdateEvent) UpdateBroker(b *broker.Broker) error {
	b.Lock()
	defer b.Unlock()
	// 更新账户UM合约杠杆
	b.Leverages[e.Account.Symbol] = e.Account.Leverage
	return nil
}

// 账户余额更新
func (e *BalanceUpdateEvent) UpdateBroker(b *broker.Broker) error {
	b.Lock()
	defer b.Unlock()
	if _, exists := b.AssetWallets[e.Asset]; !exists {
		return fmt.Errorf("asset %s not found", e.Asset)
	}
	delta, _ := strconv.ParseFloat(e.BalanceDelta, 64)
	b.AssetWallets[e.Asset].Free += delta
	return nil
}
