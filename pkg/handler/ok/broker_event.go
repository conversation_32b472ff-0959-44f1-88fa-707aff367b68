package ok

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/handler"
	"GoTrader/pkg/order"
	"GoTrader/pkg/utils"
	okUtils "GoTrader/pkg/utils/ok"
	"fmt"
	"strconv"
)

const (
	OrdersUpdate             handler.WSEventType = "orders"
	AccountAndPositionUpdate handler.WSEventType = "balance_and_position"
	AccountUpdate            handler.WSEventType = "account"
)

type OrderWsArg struct {
	Channel  string `json:"channel"`
	InstType string `json:"instType"`
	InstId   string `json:"instId"`
	UID      string `json:"uid"`
}
type OrderWsData struct {
	AccFillSz         string        `json:"accFillSz,omitempty"`
	AmendResult       string        `json:"amendResult,omitempty"`
	AvgPx             string        `json:"avgPx"`
	CTime             string        `json:"cTime"`
	Category          string        `json:"category"`
	Ccy               string        `json:"ccy,omitempty"`
	ClOrdId           string        `json:"clOrdId,omitempty"`
	Code              string        `json:"code"`
	ExecType          string        `json:"execType"`
	Fee               string        `json:"fee"`
	FeeCcy            string        `json:"feeCcy"`
	FillFee           string        `json:"fillFee"`
	FillFeeCcy        string        `json:"fillFeeCcy"`
	FillNotionalUsd   string        `json:"fillNotionalUsd"`
	FillPx            string        `json:"fillPx"`
	FillSz            string        `json:"fillSz"`
	FillPnl           string        `json:"fillPnl"`
	FillTime          string        `json:"fillTime"`
	FillPxVol         string        `json:"fillPxVol,omitempty"`
	FillPxUsd         string        `json:"fillPxUsd,omitempty"`
	FillMarkVol       string        `json:"fillMarkVol,omitempty"`
	FillFwdPx         string        `json:"fillFwdPx,omitempty"`
	FillMarkPx        string        `json:"fillMarkPx,omitempty"`
	InstId            string        `json:"instId"`
	InstType          string        `json:"instType"`
	Lever             string        `json:"lever"`
	Msg               string        `json:"msg,omitempty"`
	NotionalUsd       string        `json:"notionalUsd"`
	OrdId             string        `json:"ordId"`
	OrdType           string        `json:"ordType"`
	Pnl               string        `json:"pnl"`
	PosSide           string        `json:"posSide,omitempty"`
	Px                string        `json:"px"`
	PxUsd             string        `json:"pxUsd,omitempty"`
	PxVol             string        `json:"pxVol,omitempty"`
	PxType            string        `json:"pxType,omitempty"`
	Rebate            string        `json:"rebate"`
	RebateCcy         string        `json:"rebateCcy"`
	ReduceOnly        string        `json:"reduceOnly"`
	ReqId             string        `json:"reqId,omitempty"`
	Side              string        `json:"side"`
	AttachAlgoClOrdId string        `json:"attachAlgoClOrdId,omitempty"`
	SlOrdPx           string        `json:"slOrdPx,omitempty"`
	SlTriggerPx       string        `json:"slTriggerPx,omitempty"`
	SlTriggerPxType   string        `json:"slTriggerPxType,omitempty"`
	Source            string        `json:"source,omitempty"`
	State             string        `json:"state"`
	StpId             string        `json:"stpId,omitempty"`
	StpMode           string        `json:"stpMode,omitempty"`
	Sz                string        `json:"sz"`
	Tag               string        `json:"tag,omitempty"`
	TdMode            string        `json:"tdMode"`
	TgtCcy            string        `json:"tgtCcy,omitempty"`
	TpOrdPx           string        `json:"tpOrdPx,omitempty"`
	TpTriggerPx       string        `json:"tpTriggerPx,omitempty"`
	TpTriggerPxType   string        `json:"tpTriggerPxType,omitempty"`
	TradeId           string        `json:"tradeId"`
	LastPx            string        `json:"lastPx"`
	QuickMgnType      string        `json:"quickMgnType,omitempty"`
	AlgoClOrdId       string        `json:"algoClOrdId,omitempty"`
	AttachAlgoOrds    []string      `json:"attachAlgoOrds,omitempty"`
	AlgoId            string        `json:"algoId,omitempty"`
	AmendSource       string        `json:"amendSource,omitempty"`
	CancelSource      string        `json:"cancelSource,omitempty"`
	IsTpLimit         string        `json:"isTpLimit,omitempty"`
	UTime             string        `json:"uTime"`
	LinkedAlgoOrd     LinkedAlgoOrd `json:"linkedAlgoOrd"`
}
type LinkedAlgoOrd struct {
	AlgoId string `json:"algoId"`
}
type OrderTradeUpdateEvent struct {
	Arg  OrderWsArg
	Data []OrderWsData
}

// 订单更新事件, 同时更新仓位
type OrderTradePositionUpdateEvent struct {
	Arg  OrderWsArg
	Data []OrderWsData
}

// 定义最外层结构体
type AccountAndPositionEvent struct {
	Arg  AccountArgData               `json:"arg"`
	Data []AccountAndPositionDataItem `json:"data"`
}

// arg字段的结构体
type AccountArgData struct {
	Channel string `json:"channel"`
	Uid     string `json:"uid"`
}

// data字段的结构体
type AccountAndPositionDataItem struct {
	PTime     string `json:"pTime"`
	EventType string `json:"eventType"`
	BalData   []struct {
		Ccy        string `json:"ccy"`     // 币种
		CashBal    string `json:"cashBal"` // 币种余额
		UpdateTime string `json:"uTime"`   // 更新时间
	} `json:"balData"`
	PosData []AccountPosDataItem `json:"posData"`
	Trades  []struct {
		InstId  string `json:"instId"`
		TradeId string `json:"tradeId"`
	} `json:"trades"`
}

// posData字段的结构体
type AccountPosDataItem struct {
	PosId    string `json:"posId"`
	TradeId  string `json:"tradeId"`
	InstId   string `json:"instId"`
	InstType string `json:"instType"`
	MgnMode  string `json:"mgnMode"`
	PosSide  string `json:"posSide"`
	Pos      string `json:"pos"`
	Ccy      string `json:"ccy"`
	PosCcy   string `json:"posCcy"`
	AvgPx    string `json:"avgPx"`
	UTime    string `json:"uTime"`
}

// AccountUpdateEvent 用于表示账户事件的数据结构
type AccountUpdateEvent struct {
	Arg struct {
		Channel string `json:"channel"` // 渠道类型
		Uid     string `json:"uid"`     // 用户ID
	} `json:"arg"`
	EventType string `json:"eventType"` // 事件类型
	Data      []struct {
		UpdateTime string               `json:"uTime"`   // 更新时间
		TotalEq    string               `json:"totalEq"` // 美金层面权益
		Details    []AccountBalDataItem `json:"details"` // 账户详细信息
	} `json:"data"`
}

// balData字段的结构体
type AccountBalDataItem struct {
	Ccy        string `json:"ccy"`       // 币种
	Equity     string `json:"eq"`        // 币种总权益
	CashBal    string `json:"cashBal"`   // 币种余额
	UpdateTime string `json:"uTime"`     // 币种余额更新时间
	AvailEq    string `json:"availEq"`   // 可用保证金
	AvailBal   string `json:"availBal"`  // 可用余额
	FrozenBal  string `json:"frozenBal"` // 冻结余额
	OrdFrozen  string `json:"ordFrozen"` // 订单冻结
	MMR        string `json:"mmr"`       // 维持保证金
}

// 更新期货和现货订单状态
func (e *OrderTradeUpdateEvent) UpdateBroker(b *broker.Broker) error {
	b.Lock()
	defer b.Unlock()
	if e.Arg.InstType == "SWAP" {
		for _, ord := range e.Data {
			// 获取唯一ID
			ordId := ord.ClOrdId
			if ordId == "" {
				ordId = ord.OrdId
			}
			symbol := okUtils.InstIdFuturesToSymbol(ord.InstId)
			// 获取张币比值
			// 订单size为张, 需要转换为币
			ctVal, ok := b.CtVals[symbol]
			if !ok {
				return fmt.Errorf("ctval symbol %s not found", symbol)
			}
			o := b.FuturesPending.Pending[ordId]
			if o == nil {
				o = &order.FuturesOrder{
					ClOrdId: ord.ClOrdId,
					Symbol:  symbol,
					Side:    okUtils.UnmarshalSide(ord.Side),
					PosSide: okUtils.UnmarshalPosSide(ord.PosSide),
				}
				o.OrdType, o.TimeInForce = okUtils.UnmarshalOrdType(ord.OrdType)
				b.FuturesPending.Pending[ordId] = o
			}
			errs := make([]error, 0)
			o.Status = okUtils.UnmarshalOrdStatus(ord.State)
			o.ExecutedSize = utils.MustParseFloat64(ord.AccFillSz, &errs) * ctVal // 张币转换
			// 未执行时为空
			o.ExecutedTime, _ = strconv.ParseInt(ord.FillTime, 10, 64)
			o.AvgPrice = utils.MustParseFloat64(ord.AvgPx, &errs)
			// 市价单时为空
			o.Price, _ = strconv.ParseFloat(ord.Px, 64)
			o.Size = utils.MustParseFloat64(ord.Sz, &errs) * ctVal // 张币转换
			o.CommAsset = ord.FeeCcy
			o.Commission = utils.MustParseFloat64(ord.Fee, &errs)
			o.Profit = utils.MustParseFloat64(ord.Pnl, &errs)
			o.Maker = ord.ExecType == "M"
			if o.Status == order.FILLED || o.Status == order.CANCELED || o.Status == order.EXPIRED_IN_MATCH || o.Status == order.EXPIRED {
				// 删除订单
				delete(b.FuturesPending.Pending, ordId)
			}
			// 更新事件推送时间
			b.FuturesPending.UpdateTime = utils.MustParseInt64(ord.UTime, &errs)
			err := utils.CollectErrors(errs)
			if err != nil {
				return err
			}
		}
	}
	if e.Arg.InstType == "SPOT" {
		for _, ord := range e.Data {
			// 获取唯一ID
			ordId := ord.ClOrdId
			if ordId == "" {
				ordId = ord.OrdId
			}
			symbol := okUtils.InstIdSpotToSymbol(ord.InstId)
			o := b.MarginPending.Pending[ordId]
			if o == nil {
				cTime, _ := strconv.ParseInt(ord.CTime, 10, 64)
				o = &order.MarginOrder{
					ClOrdId:     ord.ClOrdId,
					Symbol:      symbol,
					Side:        okUtils.UnmarshalSide(ord.Side),
					CreatedTime: cTime,
				}
				o.OrdType, o.TimeInForce = okUtils.UnmarshalOrdType(ord.OrdType)
				b.MarginPending.Pending[ordId] = o
			}
			o.Status = okUtils.UnmarshalOrdStatus(ord.State)
			errs := make([]error, 0)
			o.ExecutedSize = utils.MustParseFloat64(ord.AccFillSz, &errs)
			// 未执行时为空
			o.ExecutedTime, _ = strconv.ParseInt(ord.FillTime, 10, 64)
			// 市价单时为空
			o.Price, _ = strconv.ParseFloat(ord.Px, 64)
			o.Size = utils.MustParseFloat64(ord.Sz, &errs)
			o.CommAsset = ord.FeeCcy
			o.Commission = utils.MustParseFloat64(ord.Fee, &errs)
			o.Maker = ord.ExecType == "M"
			if o.Status == order.FILLED || o.Status == order.CANCELED || o.Status == order.EXPIRED_IN_MATCH || o.Status == order.EXPIRED {
				delete(b.MarginPending.Pending, ordId)
			}
			// 更新事件推送时间
			b.MarginPending.UpdateTime = utils.MustParseInt64(ord.UTime, &errs)
			err := utils.CollectErrors(errs)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// 更新账户信息，包括期货仓位
func (e *AccountAndPositionEvent) UpdateBroker(b *broker.Broker) error {
	b.Lock()
	defer b.Unlock()
	if e.Data == nil {
		return nil
	}

	for _, data := range e.Data {
		// 更新账户余额
		for _, balItem := range data.BalData {
			if _, exists := b.AssetWallets[balItem.Ccy]; !exists {
				b.AssetWallets[balItem.Ccy] = &broker.AssetWallet{Asset: balItem.Ccy}
			} else {
				b.AssetWallets[balItem.Ccy].Asset = balItem.Ccy
			}
			// updateTime 作为仓位的校验时间
			checkTime, _ := strconv.ParseInt(balItem.UpdateTime, 10, 64)
			// 跳过修正条件:
			// 校验时间小于更新时间
			if checkTime < b.AssetWallets[balItem.Ccy].UpdateTime {
				continue
			}
			// 更新时间作为校验时间
			b.AssetWallets[balItem.Ccy].CheckTime = checkTime
			// snapshot时只有cashBalance, 没有available, 此时用cashBalance更新
			cashBalance, _ := strconv.ParseFloat(balItem.CashBal, 64)
			b.AssetWallets[balItem.Ccy].Free = cashBalance - b.AssetWallets[balItem.Ccy].Locked
		}
		// 更新仓位
		for _, posItem := range data.PosData {
			// 只考虑永续合约
			if posItem.InstType != "SWAP" {
				continue
			}
			symbol := okUtils.InstIdFuturesToSymbol(posItem.InstId)
			if _, exists := b.Positions[symbol]; !exists {
				b.Positions[symbol] = &broker.UMPosition{Symbol: symbol}
			}
			position := b.Positions[symbol]
			checkTime, _ := strconv.ParseInt(posItem.UTime, 10, 64)
			// 当前品种的tradeId
			tradeId := int64(0)
			for _, trade := range data.Trades {
				if trade.InstId == posItem.InstId {
					tradeId, _ = strconv.ParseInt(trade.TradeId, 10, 64)
					break
				}
			}
			// 跳过修正条件
			if checkTime < position.UpdateTime && tradeId < position.UpdateId {
				continue
			}
			position.CheckTime = checkTime
			position.UpdateId = tradeId

			pos, _ := strconv.ParseFloat(posItem.Pos, 64)
			ctval, ok := b.CtVals[symbol]
			if !ok {
				return fmt.Errorf("ctval symbol %s not found", symbol)
			}
			switch okUtils.UnmarshalPosSide(posItem.PosSide) {
			case order.LONG:
				b.Positions[symbol].SizeLong = pos * ctval
			case order.SHORT:
				b.Positions[symbol].SizeShort = -pos * ctval
			case order.BOTH:
				if pos > 0 {
					b.Positions[symbol].SizeLong = pos * ctval
					b.Positions[symbol].SizeShort = 0
				} else {
					b.Positions[symbol].SizeLong = 0
					b.Positions[symbol].SizeShort = pos * ctval
				}
			}
			// 调整开仓价格
			errs := make([]error, 0)
			if b.Positions[symbol].SizeLong == 0 {
				b.Positions[symbol].OpenPriceLong = 0
			} else {
				b.Positions[symbol].OpenPriceLong = utils.MustParseFloat64(posItem.AvgPx, &errs)
			}
			if b.Positions[symbol].SizeShort == 0 {
				b.Positions[symbol].OpenPriceShort = 0
			} else {
				b.Positions[symbol].OpenPriceShort = utils.MustParseFloat64(posItem.AvgPx, &errs)
			}
			err := utils.CollectErrors(errs)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// 更新账户信息保证金余额
func (e *AccountUpdateEvent) UpdateBroker(b *broker.Broker) error {
	b.Lock()
	defer b.Unlock()
	if e.Data == nil {
		return fmt.Errorf("account update event data is nil")
	}
	if len(e.Data) == 0 {
		return fmt.Errorf("account update event data is empty")
	}
	// fmt.Printf("account update event data %+v \n", e)
	for _, item := range e.Data[0].Details {
		if _, exists := b.AssetWallets[item.Ccy]; !exists {
			b.AssetWallets[item.Ccy] = &broker.AssetWallet{Asset: item.Ccy}
		}
		// 如果上次更新时间大于事件时间则跳过此次更新
		uTime, _ := strconv.ParseInt(item.UpdateTime, 10, 64)
		if b.AssetWallets[item.Ccy].UpdateTime > uTime {
			continue
		}
		b.AssetWallets[item.Ccy].UpdateTime = uTime
		// snapshot时只有cashBalance, 没有available, 此时用cashBalance更新
		errs := make([]error, 0)
		cashBalance := utils.MustParseFloat64(item.CashBal, &errs)
		b.AssetWallets[item.Ccy].Locked = utils.MustParseFloat64(item.FrozenBal, &errs)
		b.AssetWallets[item.Ccy].Free = cashBalance - b.AssetWallets[item.Ccy].Locked
		// USDT 用于更新账户权益
		if item.Ccy == "USDT" {
			b.Account.CashBalance = cashBalance
			b.Account.Equity = utils.MustParseFloat64(item.Equity, &errs)
			b.Account.MarginOpenLoss = b.AssetWallets[item.Ccy].Locked
			b.Account.Available = b.Account.Equity - b.Account.MarginOpenLoss
			b.Account.MaintMargin = utils.MustParseFloat64(item.MMR, &errs)
		}
		err := utils.CollectErrors(errs)
		if err != nil {
			return err
		}
	}
	return nil
}

// 更新订单并更新仓位
func (e *OrderTradePositionUpdateEvent) UpdateBroker(b *broker.Broker) error {
	b.Lock()
	defer b.Unlock()
	if e.Arg.InstType == "SWAP" {
		for _, ord := range e.Data {
			// 获取唯一ID
			ordId := ord.ClOrdId
			if ordId == "" {
				ordId = ord.OrdId
			}
			symbol := okUtils.InstIdFuturesToSymbol(ord.InstId)
			// 获取张币比值
			// 订单size为张, 需要转换为币
			ctVal := b.CtVals[symbol]
			o := b.FuturesPending.Pending[ordId]
			if o == nil {
				o = &order.FuturesOrder{
					OrdId:   ord.OrdId,
					ClOrdId: ord.ClOrdId,
					Symbol:  symbol,
					Side:    okUtils.UnmarshalSide(ord.Side),
					PosSide: okUtils.UnmarshalPosSide(ord.PosSide),
				}
				o.OrdType, o.TimeInForce = okUtils.UnmarshalOrdType(ord.OrdType)
				b.FuturesPending.Pending[ordId] = o
			}
			errs := make([]error, 0)
			o.Status = okUtils.UnmarshalOrdStatus(ord.State)
			o.ExecutedSize = utils.MustParseFloat64(ord.AccFillSz, &errs) * ctVal // 张币转换
			// 未执行时为空
			o.ExecutedTime, _ = strconv.ParseInt(ord.FillTime, 10, 64)
			o.AvgPrice = utils.MustParseFloat64(ord.AvgPx, &errs)
			// 市价单时为空
			o.Price, _ = strconv.ParseFloat(ord.Px, 64)
			o.Size = utils.MustParseFloat64(ord.Sz, &errs) * ctVal // 张币转换
			o.CommAsset = ord.FeeCcy
			o.Commission = utils.MustParseFloat64(ord.Fee, &errs)
			o.Profit = utils.MustParseFloat64(ord.Pnl, &errs)
			o.Maker = ord.ExecType == "M"
			// 删除结束订单
			if o.Status == order.FILLED || o.Status == order.CANCELED || o.Status == order.EXPIRED_IN_MATCH || o.Status == order.EXPIRED {
				delete(b.FuturesPending.Pending, ordId)
			}
			// 更新事件推送时间
			b.FuturesPending.UpdateTime = utils.MustParseInt64(ord.UTime, &errs)

			// 更新仓位
			fillSize := utils.MustParseFloat64(ord.FillSz, &errs) * ctVal // 张币转换
			// 没有成交, 不需要更新
			if fillSize == 0 {
				continue
			}
			if _, exists := b.Positions[symbol]; !exists {
				b.Positions[symbol] = &broker.UMPosition{Symbol: symbol}
			}
			position := b.Positions[symbol]

			fillPrice := utils.MustParseFloat64(ord.FillPx, &errs)
			if o.Side == order.SELL {
				fillSize = -fillSize
			}

			updateTime := utils.MustParseInt64(ord.UTime, &errs)
			tradeId := utils.MustParseInt64(ord.TradeId, &errs)
			// 跳过更新条件:
			// 1. 事件推送tradeId小于更新tradeId, 说明该trade已经更新过
			// 2. 事件推送时间小于等于仓位校验时间, 以校验时间为准
			if tradeId < position.UpdateId || updateTime <= position.CheckTime {
				continue
			}
			position.UpdateId = tradeId
			position.UpdateTime = updateTime
			switch o.PosSide {
			case order.LONG:
				newPos := position.SizeLong + fillSize
				if newPos == 0 {
					position.OpenPriceLong = 0
				} else if o.IsOpen() {
					position.OpenPriceLong = (position.SizeLong*position.OpenPriceLong + fillSize*fillPrice) / newPos
				}
				position.SizeLong = newPos
			case order.SHORT:
				newPos := position.SizeShort + fillSize
				if newPos == 0 {
					position.OpenPriceShort = 0
				} else if o.IsOpen() {
					position.OpenPriceShort = (position.SizeShort*position.OpenPriceShort + fillSize*fillPrice) / newPos
				}
				position.SizeShort = newPos
			}

			err := utils.CollectErrors(errs)
			if err != nil {
				return err
			}
		}
	}
	if e.Arg.InstType == "SPOT" {
		for _, ord := range e.Data {
			// 获取唯一ID
			ordId := ord.ClOrdId
			if ordId == "" {
				ordId = ord.OrdId
			}
			symbol := okUtils.InstIdSpotToSymbol(ord.InstId)
			o := b.MarginPending.Pending[ordId]
			if o == nil {
				cTime, _ := strconv.ParseInt(ord.CTime, 10, 64)
				o = &order.MarginOrder{
					OrdId:       ord.OrdId,
					ClOrdId:     ord.ClOrdId,
					Symbol:      symbol,
					Side:        okUtils.UnmarshalSide(ord.Side),
					CreatedTime: cTime,
				}
				o.OrdType, o.TimeInForce = okUtils.UnmarshalOrdType(ord.OrdType)
				b.MarginPending.Pending[ordId] = o
			}
			o.Status = okUtils.UnmarshalOrdStatus(ord.State)
			errs := make([]error, 0)
			prevSize := o.Size
			prevFillSize := o.ExecutedSize
			o.ExecutedSize = utils.MustParseFloat64(ord.AccFillSz, &errs)
			o.LastExecutedSize = utils.MustParseFloat64(ord.FillSz, &errs)
			// 未执行时为空
			o.ExecutedTime, _ = strconv.ParseInt(ord.FillTime, 10, 64)
			// 市价单时为空
			o.Price, _ = strconv.ParseFloat(ord.Px, 64)
			o.Size = utils.MustParseFloat64(ord.Sz, &errs)
			o.CommAsset = ord.FeeCcy
			o.Commission = utils.MustParseFloat64(ord.Fee, &errs)
			o.Maker = ord.ExecType == "M"

			// 删除结束订单
			if o.Status == order.FILLED || o.Status == order.CANCELED || o.Status == order.EXPIRED_IN_MATCH || o.Status == order.EXPIRED {
				delete(b.MarginPending.Pending, ordId)
			}
			// 更新事件推送时间
			b.MarginPending.UpdateTime = utils.MustParseInt64(ord.UTime, &errs)

			asset := okUtils.InstIdSpotToAsset(ord.InstId)
			if _, exists := b.AssetWallets[asset]; !exists {
				b.AssetWallets[asset] = &broker.AssetWallet{Asset: asset}
			}
			wallet := b.AssetWallets[asset]

			updateTime := utils.MustParseInt64(ord.UTime, &errs)

			// 如果更新时间小于校验时间则跳过此次更新
			if updateTime <= wallet.CheckTime {
				continue
			}
			wallet.UpdateTime = updateTime

			// 计算free, locked
			if o.Side == order.SELL {
				// 计算该订单未生效时的free和lock
				wallet.Locked -= prevSize - prevFillSize
				wallet.Free += prevSize - prevFillSize
				// 计算该订单生效后的free和lock
				wallet.Locked += o.Size - o.ExecutedSize
				wallet.Free -= o.Size - o.ExecutedSize
				// 已结束生命周期的卖出挂单释放locked
				if !(o.Status == order.NEW || o.Status == order.PARTIALLY_FILLED) {
					wallet.Locked -= o.Size - o.ExecutedSize
					wallet.Free += o.Size - o.ExecutedSize
				}
			}

			lastFillSize := o.LastExecutedSize

			if o.Side == order.BUY {
				wallet.Free += lastFillSize
			}

			// 手续费扣除, 只考虑本币
			// 手续费为负数
			fillFee := utils.MustParseFloat64(ord.FillFee, &errs)
			if ord.FeeCcy == asset {
				wallet.Free += fillFee
			}

			err := utils.CollectErrors(errs)
			if err != nil {
				return err
			}
		}
	}
	return nil
}
