package ok

import "GoTrader/pkg/handler"

const (
	Trades      handler.WSStreamType = "trades"
	Depth5      handler.WSStreamType = "books5"
	PriceLimit  handler.WSStreamType = "price-limit"
	FundingRate handler.WSStreamType = "funding-rate"
	IndexPrice  handler.WSStreamType = "index-tickers"
	Depth400    handler.WSStreamType = "books"
)

// 最外层结构体
type TradesStream struct {
	Arg  TradesArgData `json:"arg"`
	Data []TradeData   `json:"data"`
}

// arg 字段的结构体
type TradesArgData struct {
	Channel string `json:"channel"`
	InstId  string `json:"instId"`
}

// data 字段的结构体
type TradeData struct {
	InstId  string `json:"instId"`
	TradeId string `json:"tradeId"`
	Px      string `json:"px"`
	Sz      string `json:"sz"`
	Side    string `json:"side"`
	Ts      string `json:"ts"`
	Count   string `json:"count"`
}

type FuturesFundingRateStream struct {
	Stream string `json:"stream"`
	Data   struct {
		Event    string `json:"e"`
		Symbol   string `json:"s"`
		Rate     string `json:"r"`
		Time     int64  `json:"E"`
		NextTime int64  `json:"T"`
	} `json:"data"`
}

type SpotPartialDepthStream struct {
	Stream string `json:"stream"`
	Data   struct {
		Bids [][]string `json:"bids"`
		Asks [][]string `json:"asks"`
	} `json:"data"`
}

// AskBidItem 表示 asks 和 bids 中的单个项目
type AskBidItem struct {
	Price  string `json:"0"` // 价格
	Amount string `json:"1"` // 数量
	Zero   string `json:"2"` // 未使用的字段
	Number string `json:"3"` // 序号
}

// DataItem 表示一个请求中的 data 项
type DataItem struct {
	Asks      [][]string `json:"asks"` // 卖盘数据
	Bids      [][]string `json:"bids"` // 买盘数据
	Timestamp string     `json:"ts"`   // 时间戳
	Checksum  int64      `json:"checksum"`
	PrevSeqId int64      `json:"prevSeqId"`
	SeqId     int64      `json:"seqId"`
}

// Arg 表示请求中的参数部分
type Arg struct {
	Channel string `json:"channel"` // 通道
	InstId  string `json:"instId"`  // 合约对
}

// FuturesPartialDepthStream 表示整个请求数据
type FuturesPartialDepthStream struct {
	Arg    Arg        `json:"arg"`    // 请求参数
	Action string     `json:"action"` // 请求类型
	Data   []DataItem `json:"data"`   // 数据项
}

// 限价频道
type PriceLimitStream struct {
	Arg  Arg `json:"arg"`
	Data []struct {
		InstId   string `json:"instId"`
		MaxPrice string `json:"buyLmt"`
		MinPrice string `json:"sellLmt"`
		Ts       string `json:"ts"`
		Enabled  bool   `json:"enabled"`
	} `json:"data"`
}

// 资金费率更新
type FundingRateStream struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
	Data []struct {
		InstType        string `json:"instType"`
		InstId          string `json:"instId"`
		FundingRate     string `json:"fundingRate"`
		FundingTime     string `json:"fundingTime"`
		NextFundingTime string `json:"nextFundingTime"`
		MaxFundingRate  string `json:"maxFundingRate"`
		MinFundingRate  string `json:"minFundingRate"`
		Ts              string `json:"ts"`
	} `json:"data"`
}

// 深度5更新
type Depth5Stream struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
	Action string     `json:"action"`
	Data   []DataItem `json:"data"`
}

// 指数价格更新
type IndexPriceStream struct {
	Arg struct {
		Channel string `json:"channel"`
		InstId  string `json:"instId"`
	} `json:"arg"`
	Data []struct {
		InstId  string `json:"instId"`
		IndexPx string `json:"idxPx"`
		Ts      string `json:"ts"`
	} `json:"data"`
}

// 400档增量深度更新
type Depth400Stream = Depth5Stream
