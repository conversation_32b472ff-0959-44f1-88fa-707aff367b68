package ok

import (
	"GoTrader/pkg/client"
	okclient "GoTrader/pkg/client/ok"
	"GoTrader/pkg/data"
	"GoTrader/pkg/utils"
	okUtils "GoTrader/pkg/utils/ok"
	"context"
	"encoding/json"
	"log/slog"
	"strconv"
)

// 将handler绑定broker参数
func DataBindDataClient(
	dc client.DataClient,
	f func([]byte, client.DataClient, *data.SymbolData, *slog.Logger) error,
) func([]byte, *data.SymbolData, *slog.Logger) error {
	return func(msg []byte, d *data.SymbolData, logger *slog.Logger) error {
		return f(msg, dc, d, logger)
	}
}

func TradesHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送聚合行情更新数据
	d.Lock()
	defer func() {
		d.<PERSON>()
		// 触发更新
		d.TriggerUpdate(logger)
	}()
	var futuresAggTrade TradesStream
	err = json.Unmarshal(message, &futuresAggTrade)
	if err != nil {
		logger.Error("行情解析消息失败: ", "error", err)
		return err
	}
	for _, trade := range futuresAggTrade.Data {
		fprice, _ := strconv.ParseFloat(trade.Px, 64)
		sz, _ := strconv.ParseFloat(trade.Sz, 64)
		volume := d.Info.CtVal * sz
		d.Price.EnQueue(fprice)
		d.Volume.EnQueue(volume)
		uTime, _ := strconv.ParseInt(trade.Ts, 10, 64)
		d.Time.EnQueue(float64(uTime))
	}
	return nil
}

func PriceLimitHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送限价更新数据
	d.Lock()
	defer d.Unlock()
	var priceLimit PriceLimitStream
	err = json.Unmarshal(message, &priceLimit)
	if err != nil {
		logger.Error("行情解析消息失败: ", "error", err)
		return err
	}
	for _, limit := range priceLimit.Data {
		if !limit.Enabled {
			d.Info.MaxPrice = data.MAX_PRICE
			d.Info.MinPrice = data.MIN_PRICE
		} else {
			d.Info.MaxPrice, _ = strconv.ParseFloat(limit.MaxPrice, 64)
			d.Info.MinPrice, _ = strconv.ParseFloat(limit.MinPrice, 64)
			d.Info.PriceUpdateTime, _ = strconv.ParseInt(limit.Ts, 10, 64)
		}
	}
	return nil
}

func FundingRateHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送资金费率更新数据
	d.Lock()
	defer d.Unlock()
	var fundingRate FundingRateStream
	err = json.Unmarshal(message, &fundingRate)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	for _, data := range fundingRate.Data {
		if data.InstType != "SWAP" {
			continue
		}
		if okUtils.InstIdFuturesToSymbol(data.InstId) != d.Symbol {
			continue
		}
		d.Funding.Rate, _ = strconv.ParseFloat(data.FundingRate, 64)
		fundingTime, _ := strconv.ParseInt(data.FundingTime, 10, 64)
		// 资金费结算时间小于上次结算时间, 说明是新的一轮结算, 更新上一次结算时间
		if d.Funding.Time < fundingTime {
			d.Funding.PrevTime = d.Funding.Time
		}
		d.Funding.Time, _ = strconv.ParseInt(data.FundingTime, 10, 64)
		d.Funding.Cap, _ = strconv.ParseFloat(data.MaxFundingRate, 64)
		d.Funding.Floor, _ = strconv.ParseFloat(data.MinFundingRate, 64)
		// 计算结算时间到当前时间ms的时间差
	}
	return nil
}

func Depth5TriggerHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送深度数据更新数据
	d.Lock()
	defer d.Unlock()
	var depth5 Depth5Stream
	err = json.Unmarshal(message, &depth5)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	errs := make([]error, 0)
	for _, info := range depth5.Data {
		bids := []*data.OrderBook{}
		for _, bid := range info.Bids {
			price := utils.MustParseFloat64(bid[0], &errs)
			amount := utils.MustParseFloat64(bid[1], &errs) * d.Info.CtVal
			bids = append(bids, &data.OrderBook{
				Price: price,
				Size:  amount,
			})
		}
		asks := []*data.OrderBook{}
		for _, ask := range info.Asks {
			price := utils.MustParseFloat64(ask[0], &errs)
			amount := utils.MustParseFloat64(ask[1], &errs) * d.Info.CtVal
			asks = append(asks, &data.OrderBook{
				Price: price,
				Size:  amount,
			})
		}
		ts := utils.MustParseInt64(info.Timestamp, &errs)
		d.Time.EnQueue(float64(ts))
		err := utils.CollectErrors(errs)
		if err != nil {
			return err
		}
		d.OrderBooks.Bids = bids
		d.OrderBooks.Asks = asks
	}
	d.TriggerUpdate(logger)
	return nil
}

func IndexPriceHandler(message []byte, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送标记价格更新数据
	d.Lock()
	defer d.Unlock()
	var indexPrice IndexPriceStream
	err = json.Unmarshal(message, &indexPrice)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	for _, info := range indexPrice.Data {
		if okUtils.InstIdSpotToSymbol(info.InstId) != d.Symbol {
			continue
		}
		indexPrice, _ := strconv.ParseFloat(info.IndexPx, 64)
		d.Index.EnQueue(indexPrice)
	}
	return nil
}

func Depth400TriggerHandler(message []byte, c client.DataClient, d *data.SymbolData, logger *slog.Logger) (err error) {
	// 推送深度数据更新数据
	d.OrderBooks.Lock()
	defer d.OrderBooks.Unlock()
	var depth400 Depth400Stream
	err = json.Unmarshal(message, &depth400)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}

	errs := make([]error, 0)

	// snap, 全量更新
	if depth400.Action == "snapshot" {
		for _, info := range depth400.Data {
			newBids := []*data.OrderBook{}
			for _, bid := range info.Bids {
				price := utils.MustParseFloat64(bid[0], &errs)
				amount := utils.MustParseFloat64(bid[1], &errs) * d.Info.CtVal
				newBids = append(newBids, &data.OrderBook{
					Price: price,
					Size:  amount,
				})
			}
			newAsks := []*data.OrderBook{}
			for _, ask := range info.Asks {
				price := utils.MustParseFloat64(ask[0], &errs)
				amount := utils.MustParseFloat64(ask[1], &errs) * d.Info.CtVal
				newAsks = append(newAsks, &data.OrderBook{
					Price: price,
					Size:  amount,
				})
			}
			ts := utils.MustParseInt64(info.Timestamp, &errs)
			d.Time.EnQueue(float64(ts))
			err := utils.CollectErrors(errs)
			if err != nil {
				return err
			}
			d.OrderBooks.Bids = newBids
			d.OrderBooks.Asks = newAsks
			d.OrderBooks.UpdateId = info.SeqId
			d.OrderBooks.PrevUpdateId = info.PrevSeqId
		}
	}
	// update, 增量更新
	if depth400.Action == "update" {
		for _, info := range depth400.Data {
			// 检验序列
			if info.PrevSeqId != d.OrderBooks.UpdateId {
				logger.Error("序列不连续, 重新获取深度数据", "prev", info.PrevSeqId, "current", d.OrderBooks.UpdateId)
				okC, ok := c.(*okclient.OkDataClient)
				if !ok {
					logger.Error("转换为ok数据源客户端失败")
					return nil
				}
				// TODO 可能失败
				for {
					err := okC.ReSubscribeDepth(context.Background(), d)
					if err != nil {
						logger.Error("重新订阅深度失败", "error", err)
						continue
					}
					logger.Info("重新订阅深度成功")
					break
				}
				return nil
			}

			newBids := []*data.OrderBook{}
			newAsks := []*data.OrderBook{}
			// 旧盘口index
			bidindex := 0
			askindex := 0
			// 更新bid, 并更新bidindex
			for _, bid := range info.Bids {
				updateBid := &data.OrderBook{}
				updateBid.Price = utils.MustParseFloat64(bid[0], &errs)
				updateBid.Size = utils.MustParseFloat64(bid[1], &errs) * d.Info.CtVal
				// snap先耗尽
				if len(d.OrderBooks.Bids) <= bidindex {
					// 剩余的增量盘口直接添加
					newBids = append(newBids, updateBid)
					continue
				}
				// snap未耗尽
				for _, snapBid := range d.OrderBooks.Bids[bidindex:] {
					if snapBid.Price > updateBid.Price {
						// snap价格更高(更浅), 添加当前snap价格
						if snapBid.Size != 0 {
							newBids = append(newBids, snapBid)
						}
						bidindex += 1
						continue
					}
					if snapBid.Price < updateBid.Price && updateBid.Size != 0 {
						// update价格更高(更浅), 添加当前update价格
						newBids = append(newBids, updateBid)
						break
					}
					if utils.StepEqual(snapBid.Price, updateBid.Price, d.Info.TickSize) && updateBid.Size != 0 {
						// 两者价格一致, 且size不为0, 添加update价格
						newBids = append(newBids, updateBid)
						bidindex += 1
						break
					}
					if utils.StepEqual(snapBid.Price, updateBid.Price, d.Info.TickSize) && updateBid.Size == 0 {
						// 两者价格一致, 且size为0, 跳过当前价格
						bidindex += 1
						break
					}
				}
				ts := utils.MustParseInt64(info.Timestamp, &errs)
				d.Time.EnQueue(float64(ts))
			}
			// 剩余的snap添加
			if len(d.OrderBooks.Bids) > bidindex {
				newBids = append(newBids, d.OrderBooks.Bids[bidindex:]...)
			}

			// 更新ask, 并更新askindex
			for _, ask := range info.Asks {
				updateAsk := &data.OrderBook{}
				updateAsk.Price = utils.MustParseFloat64(ask[0], &errs)
				updateAsk.Size = utils.MustParseFloat64(ask[1], &errs) * d.Info.CtVal
				// snap先耗尽
				if len(d.OrderBooks.Asks) <= askindex {
					// 剩余的盘口直接添加
					newAsks = append(newAsks, updateAsk)
					continue
				}
				// snap未耗尽
				for _, snapAsk := range d.OrderBooks.Asks[askindex:] {
					if snapAsk.Price < updateAsk.Price {
						// snap价格更低(更浅), 添加当前snap价格
						if snapAsk.Size != 0 {
							newAsks = append(newAsks, snapAsk)
						}
						askindex += 1
						continue
					}
					if snapAsk.Price > updateAsk.Price && updateAsk.Size != 0 {
						// update价格更低(更浅), 添加当前update价格
						newAsks = append(newAsks, updateAsk)
						break
					}
					if utils.StepEqual(snapAsk.Price, updateAsk.Price, d.Info.TickSize) && updateAsk.Size != 0 {
						// 两者价格一致, 且size不为0, 添加update价格
						newAsks = append(newAsks, updateAsk)
						askindex += 1
						break
					}
					if utils.StepEqual(snapAsk.Price, updateAsk.Price, d.Info.TickSize) && updateAsk.Size == 0 {
						// 两者价格一致, 且size为0, 跳过当前价格
						askindex += 1
						break
					}
				}
			}
			// 剩余的snap添加
			if len(d.OrderBooks.Asks) > askindex {
				newAsks = append(newAsks, d.OrderBooks.Asks[askindex:]...)
			}

			// 校验
			if !checkOrderBook(newBids, newAsks) {
				logger.Error("Depth update invalid", "oldBids", d.OrderBooks.Bids, "oldAsks", d.OrderBooks.Asks, "updateInfo", info, "newBids", newBids, "newAsks", newAsks)
				return nil
			}

			err := utils.CollectErrors(errs)
			if err != nil {
				return err
			}
			ts := utils.MustParseInt64(info.Timestamp, &errs)
			d.Time.EnQueue(float64(ts))
			d.OrderBooks.Bids = newBids
			d.OrderBooks.Asks = newAsks
			d.OrderBooks.UpdateId = info.SeqId
			d.OrderBooks.PrevUpdateId = info.PrevSeqId
		}
	}

	// 校验 TODO 精度问题 暂时不实现

	d.TriggerUpdate(logger)
	return nil
}

func checkOrderBook(bids []*data.OrderBook, asks []*data.OrderBook) bool {
	if len(bids) == 0 || len(asks) == 0 {
		return false
	}
	if bids[0].Price > asks[0].Price {
		return false
	}
	return true
}
