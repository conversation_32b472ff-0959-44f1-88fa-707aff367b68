package ok

import (
	"GoTrader/pkg/broker"
	"encoding/json"
	"log/slog"
)

// // 推送期货和现货订单更新
// func OrdersUpdateHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
// 	var orderTradeUpdateEvent OrderTradeUpdateEvent
// 	err = json.Unmarshal(message, &orderTradeUpdateEvent)
// 	if err != nil {
// 		logger.Info("OrderTradeUpdateEvent Error unmarshalling JSON:", "error", err)
// 		return err
// 	}
// 	err = orderTradeUpdateEvent.UpdateBroker(b)
// 	if err != nil {
// 		logger.Error("Order Update Error", "error", err)
// 		return err
// 	}
// 	// 打印订单更新
// 	logger.Info("Order Update", "event", orderTradeUpdateEvent)
// 	return nil
// }

// 推送期货和现货订单更新, 同时更新仓位
func OrdersPositionUpdateHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	var orderTradeUpdateEvent OrderTradePositionUpdateEvent
	err = json.Unmarshal(message, &orderTradeUpdateEvent)
	if err != nil {
		logger.Info("OrderTradeUpdateEvent Error unmarshalling JSON:", "error", err)
		return err
	}
	err = orderTradeUpdateEvent.UpdateBroker(b)
	if err != nil {
		logger.Error("Order Update Error", "error", err)
		return err
	}
	// 打印订单更新
	logger.Info("Order Update", "event", orderTradeUpdateEvent)
	return nil
}

func AccountAndPositionUpdateHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	var accountAndPositionEvent AccountAndPositionEvent
	err = json.Unmarshal(message, &accountAndPositionEvent)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	err = accountAndPositionEvent.UpdateBroker(b)
	if err != nil {
		logger.Error("Account UM Position Update Error", "error", err)
		return err
	}
	logger.Info("Account UM Position Update", "event", accountAndPositionEvent)
	return nil
}

func AccountUpdateHandler(message []byte, b *broker.Broker, logger *slog.Logger) (err error) {
	var accountUpdateEvent AccountUpdateEvent
	err = json.Unmarshal(message, &accountUpdateEvent)
	if err != nil {
		logger.Error("Error unmarshalling JSON:", "error", err)
		return err
	}
	err = accountUpdateEvent.UpdateBroker(b)
	if err != nil {
		logger.Error("Account Update Error", "error", err)
		return err
	}
	if accountUpdateEvent.EventType != "snapshot" {
		logger.Info("Account Update", "event", accountUpdateEvent)
	}
	return nil
}
