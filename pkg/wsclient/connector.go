package wsclient

import (
	"GoTrader/pkg/utils"
	"context"
	"errors"
	"fmt"
	"log/slog"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
	"golang.org/x/sync/singleflight"
)

// ws管理器接口
type WSManager interface {
	Connect(ctx context.Context, url string, logger *slog.Logger) error // 连接
	ReadMessage() (int, []byte, error)                                  // 读取消息
	WriteMessage(int, []byte) error                                     // 写入消息
	SetReInitFunc(func(context.Context, int64) error)                   // 设置重连后执行的函数
	SetCheckFunc(func(context.Context, int64) error)                    // 设置检查函数
	Stop()                                                              // 停止
	Close()                                                             // 关闭WS
	SubscribeReadChan(string)                                           // 订阅一个读取通道
	UnsubscribeReadChan(string)                                         // 取消订阅一个读取通道
	ReadFromChan(context.Context, string) (int, []byte, error)          // 从订阅通道中读取
	ReconnectManual() error                                             // 手动重连
	GetVersion() int64                                                  // 获取版本号
	IsVersionValid(int64) bool                                          // 验证版本号
}

// 重连 error
var ErrReconnected = errors.New("reconnection successful")

// 读取消息
type WSMessage struct {
	MsgType int
	Msg     []byte
	err     error
}

// context with cancel
type WSContextWithCancel struct {
	ctx    context.Context
	cancel context.CancelFunc
}

// 带版本控制的ws连接
type WsEntry struct {
	conn    *websocket.Conn
	version int64
}

// websocket管理器
// 支持重连
// 独立协程读取
// 支持订阅
type DefaultWSManager struct {
	ctx context.Context
	url string

	// 读取通道
	readChan chan WSMessage

	// 订阅读取通道
	subReadChans map[string]chan WSMessage
	// 订阅通道的context
	subReadChansCtx map[string]WSContextWithCancel
	// 订阅锁
	subReadChansMu sync.RWMutex

	writeMu sync.Mutex

	// ws连接入口
	entry atomic.Pointer[WsEntry]

	once          singleflight.Group
	retryCount    int
	retryInterval time.Duration
	reInitFunc    func(context.Context, int64) error

	restartInterval time.Duration

	// 写超时
	writeTimeout time.Duration

	// ping
	pingInterval time.Duration
	pingType     int

	// 定时检查
	checkFunc     func(context.Context, int64) error
	checkInterval time.Duration

	// 日志
	logger *slog.Logger

	// 取消函数
	cancelFunc context.CancelFunc
	// 是否忽略读取超时
	ignoreLostReadMsg bool
}

func DefaultNew(config *WSManagerConfig, logger *slog.Logger) *DefaultWSManager {
	if config == nil {
		config = NewConfig()
	}
	return &DefaultWSManager{
		readChan:        make(chan WSMessage, 1),
		subReadChans:    make(map[string]chan WSMessage),
		subReadChansCtx: make(map[string]WSContextWithCancel),
		writeMu:         sync.Mutex{},

		once:              singleflight.Group{},
		retryCount:        config.RetryCount,
		retryInterval:     time.Duration(config.RetryIntervalSec) * time.Second,
		reInitFunc:        nil,
		restartInterval:   time.Duration(config.RestartHour) * time.Hour,
		pingInterval:      time.Duration(config.PingIntervalSec) * time.Second,
		pingType:          config.PingType,
		writeTimeout:      time.Duration(config.WriteTimeoutSec) * time.Second,
		checkInterval:     time.Duration(config.CheckIntervalSec) * time.Second,
		logger:            logger,
		cancelFunc:        func() {},
		ignoreLostReadMsg: config.IgnoreLostReadMsg,
	}
}

func (m *DefaultWSManager) Connect(ctx context.Context, url string, logger *slog.Logger) error {
	m.logger = logger

	if m.entry.Load() != nil {
		logger.Info("ws is not nil, skip connect")
		return nil
	}

	wsCtx, cancel := context.WithCancel(ctx)
	m.ctx = wsCtx
	m.cancelFunc = cancel

	wsConn, err := connectWS(wsCtx, url, m.logger)
	if err != nil {
		logger.Error("error connecting to webSocket", "error", err)
		return err
	}
	m.url = url
	ver := m.SetConn(wsConn)
	logger.Info("Connected to WebSocket", "wsVersion", ver)

	logger.Info("start read loop")
	go m.readLoop(wsCtx)

	if m.pingInterval > 0 {
		logger.Info("start ping loop")
		go m.pingLoop(wsCtx)
	}

	if m.restartInterval > 0 {
		logger.Info("start restart loop")
		go m.restartLoop(wsCtx)
	}

	if m.checkInterval > 0 {
		logger.Info("start check loop")
		go m.checkLoop(wsCtx)
	}

	return nil
}

func (m *DefaultWSManager) Stop() {
	if m.entry.Load() == nil {
		return
	}
	// 一定要先关闭ctx, 再关闭ws, 否则会再次重连
	m.cancelFunc()
	m.Close()
	close(m.readChan)
	m.entry.Store(nil)
}

// 关闭ws, 通常用于临时关闭ws
func (m *DefaultWSManager) Close() {
	m.entry.Load().conn.Close()
}

// 读取
func (m *DefaultWSManager) ReadMessage() (int, []byte, error) {
	data, ok := <-m.readChan
	if !ok {
		return 0, nil, fmt.Errorf("read channel closed")
	}
	return data.MsgType, data.Msg, data.err
}

// 订阅读取通道
func (m *DefaultWSManager) SubscribeReadChan(name string) {
	m.subReadChansMu.Lock()
	defer m.subReadChansMu.Unlock()
	m.subReadChans[name] = make(chan WSMessage, 1)
	ctx, cancel := context.WithCancel(m.ctx)
	m.subReadChansCtx[name] = WSContextWithCancel{ctx: ctx, cancel: cancel}
}

// 取消订阅读取通道
func (m *DefaultWSManager) UnsubscribeReadChan(name string) {
	m.subReadChansMu.Lock()
	defer m.subReadChansMu.Unlock()
	m.subReadChansCtx[name].cancel()
	// TODO交给GC回收
	// close(m.subReadChans[name])
	delete(m.subReadChans, name)
	delete(m.subReadChansCtx, name)
}

// 返回所有订阅频道名称
func (m *DefaultWSManager) GetSubscribeReadChans() []string {
	m.subReadChansMu.RLock()
	defer m.subReadChansMu.RUnlock()
	chans := make([]string, 0, len(m.subReadChans))
	for k := range m.subReadChans {
		chans = append(chans, k)
	}
	return chans
}

// 从订阅通道中读取
func (m *DefaultWSManager) ReadFromChan(ctx context.Context, name string) (int, []byte, error) {
	select {
	case <-ctx.Done():
		return 0, nil, ctx.Err()
	case data, ok := <-m.subReadChans[name]:
		if !ok {
			return 0, nil, fmt.Errorf("read subscribe %s channel closed", name)
		}
		return data.MsgType, data.Msg, data.err
	}
}

// 写入
func (m *DefaultWSManager) WriteMessage(messageType int, msg []byte) (err error) {
	wsConn, ver := m.GetConn()
	logger := m.logger.With("wsVersion", ver)
	if wsConn == nil {
		return fmt.Errorf("websocket is not connected")
	}
	// 检查ctx是否已经取消
	if m.ctx.Err() != nil {
		return m.ctx.Err()
	}
	m.writeMu.Lock()
	defer m.writeMu.Unlock()
	// 设置超时时间
	err = wsConn.SetWriteDeadline(time.Now().Add(m.writeTimeout))
	if err != nil {
		logger.Error("Error setting write deadline", "error", err)
		return err
	}
	err = wsConn.WriteMessage(messageType, msg)
	if err != nil {
		logger.Error("Error writing Data WebSocket message", "message", string(msg), "error", err)
		return err
	}
	logger.Info("Write message successfully", "message", string(msg))
	return nil
}

func (m *DefaultWSManager) SetReInitFunc(f func(context.Context, int64) error) {
	m.reInitFunc = f
}

// 单次连接websocket
func connectWS(ctx context.Context, url string, logger *slog.Logger) (*websocket.Conn, error) {
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()
	wsConn, _, err := websocket.DefaultDialer.DialContext(timeoutCtx, url, nil)
	// resp为转换信息, 省略处理
	if err != nil {
		return nil, fmt.Errorf("error connecting to webSocket: %w", err)
	}
	logger = logger.With("newWsId", fmt.Sprintf("%p", wsConn))
	// 设置PingHandler
	wsConn.SetPingHandler(func(appData string) error {
		logger.Info("Received Ping", "data", appData)
		err := wsConn.WriteControl(websocket.PongMessage, []byte(appData), time.Now().Add(time.Second*5))
		if err != nil {
			logger.Error("Error return pong", "error", err)
		} else {
			logger.Info("Return pong successfully")
		}
		return err
	})
	wsConn.SetPongHandler(func(appData string) error {
		logger.Info("Received Pong", "data", appData)
		return nil
	})
	logger.Info("Connected to WebSocket")
	return wsConn, err
}

// 循环读取, 并处理重连逻辑
func (m *DefaultWSManager) readLoop(ctx context.Context) {
	m.logger.Info("readLoop goroutine start")
	for {
		select {
		case <-ctx.Done():
			m.logger.Info("readLoop context canceled")
			utils.SendNonBlocking(m.readChan, WSMessage{MsgType: websocket.TextMessage, Msg: nil, err: ctx.Err()})
			return
		default:
			wsConn, ver := m.GetConn()
			logger := m.logger.With("wsVersion", ver).With("loop", "read")
			_, msg, readErr := wsConn.ReadMessage()
			// 异常处理
			if readErr != nil {
				// 可能由于其他原因主动关闭, 检查ctx是否已经取消
				if ctx.Err() != nil {
					logger.Info("readLoop context canceled")
					return
				}
				logger.Info("Error reading Data WebSocket message, try to reconnect", "error", readErr, "msg", string(msg))
				// 尝试重连
				newWsConn, newVer, recErr := m.reConnect(wsConn, ver, logger)
				if recErr != nil && !errors.Is(recErr, ErrReconnected) {
					logger.Error("Reconnect failed, stop ws", "error", recErr)
					m.Stop()
					return
				} else {
					logger = logger.With("newWsVersion", newVer)
					logger.Info("Reconnect success")
					// 异步重置, 重置时需要接收ws消息
					go m.reInit(newWsConn, newVer, logger)
				}
				logger.Info("Reconnect complete")
				// 重连前的错误信息直接丢弃
				continue
			}
			// 异步发送
			wg := sync.WaitGroup{}
			wg.Add(2)
			// 发送到读取通道
			go func() {
				defer wg.Done()
				for {
					sendErr := utils.SendWithTimeout(m.ctx, m.readChan, WSMessage{MsgType: websocket.TextMessage, Msg: msg, err: readErr}, time.Second*5)
					if sendErr != nil {
						if !m.ignoreLostReadMsg {
							logger.Warn("Sending message to readChan timeout, retry", "error", sendErr)
							continue
						}
					}
					break
				}
			}()
			// 信息放入订阅频道
			go func() {
				defer wg.Done()
				chanNames := m.GetSubscribeReadChans()
				for _, chanName := range chanNames {
					ch, exists := m.subReadChans[chanName]
					if !exists {
						logger.Info("Subscribe channel not exists", "name", chanName)
						continue
					}
					// pong不转发
					if string(msg) == "pong" {
						continue
					}
					ctxWithCancel, exists := m.subReadChansCtx[chanName]
					if !exists {
						logger.Info("Subscribe channel context not exists", "name", chanName)
						continue
					}
					sendErr := utils.SendWithTimeout(ctxWithCancel.ctx, ch, WSMessage{MsgType: websocket.TextMessage, Msg: msg, err: readErr}, time.Second*5)
					if errors.Is(sendErr, context.Canceled) {
						logger.Info("Subscribe channel context canceled", "name", chanName)
						continue
					}
					if sendErr != nil {
						logger.Warn("Sending message to subscribe channel timeout", "error", sendErr, "name", chanName)
					}
				}
			}()
			wg.Wait()
		}
	}
}

// 定时发送ping
func (m *DefaultWSManager) pingLoop(ctx context.Context) {
	ticker := time.NewTicker(m.pingInterval) // 定时发送一次 Ping
	m.logger.Info("PingLoop goroutine start")
	defer func() {
		m.logger.Info("PingLoop goroutine end")
		ticker.Stop()
	}()
	for {
		select {
		case <-ctx.Done():
			m.logger.Info("pingLoop context canceled")
			m.entry.Load().conn.Close()
			return
		case <-ticker.C:
			// 发送 Ping
			m.writeMu.Lock()
			var err error
			deadline := time.Now().Add(m.writeTimeout)
			wsConn, ver := m.GetConn()
			logger := m.logger.With("wsVersion", ver).With("loop", "ping")
			logger.Info("Try to send ping")
			switch m.pingType {
			case websocket.PingMessage:
				err = wsConn.WriteControl(websocket.PingMessage, []byte("ping"), deadline)
			case websocket.TextMessage:
				wsConn.SetWriteDeadline(deadline)
				err = wsConn.WriteMessage(websocket.TextMessage, []byte("ping"))
			}
			m.writeMu.Unlock()
			if err != nil {
				logger.Info("Failed to send ping, try to reconnect", "error", err)
				newWsConn, newVer, recErr := m.reConnect(wsConn, ver, logger)
				if recErr != nil && !errors.Is(recErr, ErrReconnected) {
					logger.Error("Ping Reconnect failed, close ws", "error", recErr)
					wsConn.Close()
				}
				logger = logger.With("newWsVersion", newVer)
				logger.Info("Reconnect success")
				reInitErr := m.reInit(newWsConn, newVer, logger)
				if reInitErr != nil && !errors.Is(reInitErr, ErrReconnected) {
					logger.Error("Ping ReInit failed, close ws", "error", reInitErr)
					newWsConn.Close()
				}
			}
			logger.Info("Send ping successfully")
		}
	}

}

// 定时重启
func (m *DefaultWSManager) restartLoop(ctx context.Context) {
	ticker := time.NewTicker(m.restartInterval)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			m.logger.Info("Context canceled, closing WebSocket restart goroutine")
			m.Close()
			return
		case <-ticker.C:
			// 打印定时重启日志
			wsConn, wsVer := m.GetConn()
			logger := m.logger.With("wsVersion", wsVer).With("loop", "restart")
			logger.Info("Attempt a timed restart ...")
			newWsConn, newVer, recErr := m.reConnect(wsConn, wsVer, logger)
			if recErr != nil && !errors.Is(recErr, ErrReconnected) {
				logger.Error("Restart ReConnect failed, close ws", "error", recErr)
				wsConn.Close()
			}
			logger = logger.With("newWsVersion", newVer)
			logger.Info("Reconnect success")
			reInitErr := m.reInit(newWsConn, newVer, logger)
			if reInitErr != nil && !errors.Is(reInitErr, ErrReconnected) {
				logger.Error("Restart ReInit failed, close ws", "error", reInitErr)
				newWsConn.Close()
			}
			logger.Info("Timed restart complete")
		}
	}
}

// 重连
// 同一时间只执行一次, 其余的等待执行结果
// 重连成功会返回ErrReconnected
func (m *DefaultWSManager) reConnect(oldWsConn *websocket.Conn, oldVer int64, logger *slog.Logger) (newWsConn *websocket.Conn, newVer int64, err error) {
	currWsConn, currVer := m.GetConn()
	if oldVer < currVer {
		logger.Info("WsConn is updated , skip reconnect", "oldWsVersion", oldVer, "currVsVersion", currVer)
		return currWsConn, currVer, nil
	}
	newWsConnPointer, err, shared := m.once.Do("reconnect", func() (any, error) {
		if m.ctx.Err() != nil {
			logger.Info("reconnect context canceled")
			return nil, m.ctx.Err()
		}
		oldWsConn.Close()
		retryInterval := m.retryInterval
		for i := range m.retryCount {
			logger.Info("Attempting to reconnect...", "attempt", i+1, "oldWsVersion", oldVer)
			newWsConn, err = connectWS(m.ctx, m.url, m.logger)
			if err != nil {
				logger.Warn("Reconnect failed", "error", err, "attempt", i+1, "oldWsVersion", oldVer)
				time.Sleep(retryInterval)
				retryInterval *= 2 // 指数退避
				continue
			}
			newVer := m.SetConn(newWsConn)
			logger.Info("Reconnected successfully", "attempt", i+1, "oldWsVersion", oldVer, "newWsVersion", newVer)
			// 替换
			return &WsEntry{conn: newWsConn, version: newVer}, ErrReconnected
		}
		logger.Error("Failed to reconnect", "error", err, "attempt", m.retryCount, "oldWsVersion", oldVer)
		return nil, err // 返回异常
	})
	if shared {
		logger.Info("Reconnected shared", "error", err)
	}
	newEntry := newWsConnPointer.(*WsEntry)
	return newEntry.conn, newEntry.version, err
}

func (m *DefaultWSManager) reInit(_ *websocket.Conn, version int64, logger *slog.Logger) error {
	logger.Info("Attempting to reInit...")
	_, err, shared := m.once.Do("reInit"+strconv.FormatInt(version, 10), func() (any, error) {
		err := m.reInitFunc(m.ctx, version)
		if err != nil && !errors.Is(err, ErrReconnected) {
			logger.Error("ReInit failed", "error", err)
		} else {
			logger.Info("ReInit success")
		}
		return nil, err // 返回异常
	})
	if shared {
		logger.Info("reInit shared", "error", err)
	}
	return err
}

// 循环检查
func (m *DefaultWSManager) checkLoop(ctx context.Context) {
	ticker := time.NewTicker(m.checkInterval)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			m.logger.Info("Context canceled, closing WebSocket check goroutine")
			m.Stop()
			return
		case <-ticker.C:
			if m.checkFunc == nil {
				continue
			}
			wsConn, ver := m.GetConn()
			logger := m.logger.With("wsVersion", ver).With("loop", "check")
			logger.Info("Attempting to check...")
			checkErr := m.checkFunc(ctx, ver)
			if checkErr != nil {
				logger.Error("Check failed, close ws", "error", checkErr)
				newWsConn, newVer, recErr := m.reConnect(wsConn, ver, logger)
				if recErr != nil && !errors.Is(recErr, ErrReconnected) {
					logger.Error("Check ReConnect failed, close ws", "error", recErr)
					wsConn.Close()
				}
				logger = logger.With("newWsVersion", newVer)
				logger.Info("Reconnect success")
				reInitErr := m.reInit(newWsConn, newVer, logger)
				if reInitErr != nil && !errors.Is(reInitErr, ErrReconnected) {
					logger.Error("Check ReInit failed, close ws", "error", reInitErr)
					newWsConn.Close()
				}
				continue
			}
			logger.Info("Check complete")
		}
	}
}

func (m *DefaultWSManager) SetCheckFunc(f func(context.Context, int64) error) {
	m.checkFunc = f
}

func (m *DefaultWSManager) ReconnectManual() error {
	_, _, err := m.reConnect(m.entry.Load().conn, m.entry.Load().version, m.logger)
	return err
}

func (m *DefaultWSManager) SetConn(c *websocket.Conn) int64 {
	old := m.entry.Load()
	var v int64
	if old == nil {
		v = 1
	} else {
		v = old.version + 1
	}
	newEntry := &WsEntry{conn: c, version: v}
	m.entry.Store(newEntry)
	return v
}

func (m *DefaultWSManager) GetConn() (*websocket.Conn, int64) {
	entry := m.entry.Load()
	return entry.conn, entry.version
}

func (m *DefaultWSManager) GetVersion() int64 {
	return m.entry.Load().version
}

// 验证版本号
func (m *DefaultWSManager) IsVersionValid(ver int64) bool {
	return m.entry.Load().version == ver
}
