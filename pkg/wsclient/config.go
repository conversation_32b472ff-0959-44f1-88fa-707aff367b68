package wsclient

import "github.com/gorilla/websocket"

// 参数
type WSManagerConfig struct {
	RetryCount        int  // 重连次数
	RetryIntervalSec  int  // 重连间隔
	PingIntervalSec   int  // ping间隔
	PingTimeoutSec    int  // ping超时
	PingType          int  // ping类型
	ReadTimeoutSec    int  // 读超时
	WriteTimeoutSec   int  // 写超时
	RestartHour       int  // 重启间隔
	CheckIntervalSec  int  // 检查间隔
	IgnoreLostReadMsg bool // 是否忽略丢失数据
}

// 选项函数类型
type Option func(*WSManagerConfig)

// 默认构造函数
func NewConfig(opts ...Option) *WSManagerConfig {
	cfg := &WSManagerConfig{
		RetryCount:        3,
		RetryIntervalSec:  2,
		PingIntervalSec:   15,
		PingTimeoutSec:    5,
		PingType:          websocket.PingMessage,
		ReadTimeoutSec:    10,
		WriteTimeoutSec:   10,
		RestartHour:       23,
		CheckIntervalSec:  0,
		IgnoreLostReadMsg: true,
	}

	// 应用用户自定义的选项
	for _, opt := range opts {
		opt(cfg)
	}

	return cfg
}

func WithRetryCount(count int) Option {
	return func(cfg *WSManagerConfig) {
		cfg.RetryCount = count
	}
}

func WithRetryInterval(interval int) Option {
	return func(cfg *WSManagerConfig) {
		cfg.RetryIntervalSec = interval
	}
}

func WithPingInterval(interval int) Option {
	return func(cfg *WSManagerConfig) {
		cfg.PingIntervalSec = interval
	}
}

func WithPingTimeout(timeout int) Option {
	return func(cfg *WSManagerConfig) {
		cfg.PingTimeoutSec = timeout
	}
}

func WithPingType(pingType int) Option {
	return func(cfg *WSManagerConfig) {
		cfg.PingType = pingType
	}
}

func WithReadTimeout(timeout int) Option {
	return func(cfg *WSManagerConfig) {
		cfg.ReadTimeoutSec = timeout
	}
}

func WithWriteTimeout(timeout int) Option {
	return func(cfg *WSManagerConfig) {
		cfg.WriteTimeoutSec = timeout
	}
}

func WithRestartHour(hour int) Option {
	return func(cfg *WSManagerConfig) {
		cfg.RestartHour = hour
	}
}

func WithCheckInterval(interval int) Option {
	return func(cfg *WSManagerConfig) {
		cfg.CheckIntervalSec = interval
	}
}

func WithIgnoreLostReadMsg(ignore bool) Option {
	return func(cfg *WSManagerConfig) {
		cfg.IgnoreLostReadMsg = ignore
	}
}
