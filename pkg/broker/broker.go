package broker

import (
	"GoTrader/pkg/data"
	"GoTrader/pkg/order"
	"io"
	"log/slog"
	"math"
	"time"

	"github.com/sasha-s/go-deadlock"
)

// 账户模式
type BrokerMode string

const (
	Portfolio BrokerMode = "portfolio" // 统一账户模式, 期货+杠杆, 期货和杠杆资金统一管理
	Costom    BrokerMode = "costom"    // 传统账户模式, 期货+现货, 期货和现货资金分别管理
)

const (
	Min_Cash = 0.000_000_000_1 // 最小金额, 通常用于初始化时防止除0
)

// 账户代理
type Broker struct {
	ApiKey            string                    // api key
	Secret            string                    // secret
	PassPhrase        string                    // passPhrase
	DBIndex           string                    // 数据库索引
	Account           Account                   // 账户信息，包括总资产，可用资产，通常用于计算期货交易
	AssetWallets      map[string]*AssetWallet   // 资产钱包，存放每个资产的总资产，可用资产，冻结资产
	Positions         map[string]*UMPosition    // 期货仓位
	FuturesPending    FuturesPending            // 期货挂单
	MarginPending     MarginPending             // 杠杆挂单
	Leverages         map[string]int64          // 杠杆
	CtVals            map[string]float64        // 张币系数
	PositionTiers     map[string][]PositionTier // 仓位档位
	FuturesCommission Commission                // 期货手续费
	SpotCommission    Commission                // 现货手续费
	Mode              BrokerMode                // 账户模式
	rwMutex           deadlock.RWMutex          // 读写锁
	logger            *slog.Logger              // 日志
}

type FuturesPending struct {
	Pending    map[string]*order.FuturesOrder
	UpdateTime int64
}

type MarginPending struct {
	Pending    map[string]*order.MarginOrder
	UpdateTime int64
}

type Account struct {
	// 基础数据, 必须从交易所获取
	CashBalance float64 // 账户剩余现金, 通常在发生交易时才会变动(手续费, 或平仓的利润结算)

	// 衍生数据, 可从数据计算, 也可以直接通过数据源获取
	Equity         float64 // 权益, CashBalance + UnrealizedPnl
	Available      float64 // 可用保证金, Equity - MarginOpenLoss
	MarginOpenLoss float64 // 占用保证金, Sum(仓位价值 / 杠杆)
	MaintMargin    float64 // 维持保证金, Sum(仓位价值 * 仓位维持保证金率)
}

type AssetWallet struct {
	Asset      string  // 资产名称
	Free       float64 // 可用余额
	Locked     float64 // 冻结余额
	UM         float64 // U本位余额
	CM         float64 // 币本位余额
	UpdateTime int64   // 更新时间戳ms
	CheckTime  int64   // 校验时间戳ms
}

func (a *AssetWallet) Total() float64 {
	return a.Free + a.Locked
}

type UMPosition struct {
	Symbol         string  // 品种
	OpenPriceLong  float64 // 多头开仓均价
	OpenPriceShort float64 // 空头开仓均价
	SizeLong       float64 // 多头
	SizeShort      float64 // 空头
	Leverage       int64   // 杠杆
	UpdateTime     int64   // 更新时间戳ms
	CheckTime      int64   // 校验时间戳ms
	UpdateId       int64   // 更新标记id
}

// 仓位档位
type PositionTier struct {
	Tier             int64   // 档位
	MaxSize          float64 // 最大仓位
	MinSize          float64 // 最小仓位
	MaxNotional      float64 // 最大名义价值
	MinNotional      float64 // 最小名义价值
	Cum              float64 // 累计维持保证金, 用于速算
	MaintMarginRatio float64 // 维持保证金率
	InitialLeverage  float64 // 最大杠杆
}

// 手续费率
type Commission struct {
	Taker float64
	Maker float64
}

func NewBroker(apikey string, secret string, passphrase string, dbIndex string, mode BrokerMode, logger *slog.Logger) *Broker {
	if logger == nil {
		logger = slog.New(slog.NewJSONHandler(io.Discard, &slog.HandlerOptions{
			Level: slog.LevelInfo,
		}))
	}

	deadlock.Opts.DeadlockTimeout = 10 * time.Second
	return &Broker{
		ApiKey:         apikey,
		Secret:         secret,
		PassPhrase:     passphrase,
		DBIndex:        dbIndex,
		Account:        Account{},
		AssetWallets:   map[string]*AssetWallet{},
		Positions:      map[string]*UMPosition{},
		FuturesPending: FuturesPending{UpdateTime: 0, Pending: map[string]*order.FuturesOrder{}},
		MarginPending:  MarginPending{UpdateTime: 0, Pending: map[string]*order.MarginOrder{}},
		Leverages:      map[string]int64{},
		PositionTiers:  map[string][]PositionTier{},
		CtVals:         map[string]float64{},
		rwMutex:        deadlock.RWMutex{},
		Mode:           mode,
		logger:         logger,
	}
}

// 添加期货挂单
func (b *Broker) AddFuturesOrder(o *order.FuturesOrder) {
	b.Lock()
	defer b.Unlock()
	b.FuturesPending.Pending[o.ClOrdId] = o
}

// 添加杠杆挂单
func (b *Broker) AddMarginOrder(o *order.MarginOrder) {
	b.Lock()
	defer b.Unlock()
	b.MarginPending.Pending[o.ClOrdId] = o
}

// 移除期货挂单
func (b *Broker) RemoveFuturesOrder(o *order.FuturesOrder) {
	b.Lock()
	defer b.Unlock()
	delete(b.FuturesPending.Pending, o.ClOrdId)
}

// 移除杠杆挂单
func (b *Broker) RemoveMarginOrder(o *order.MarginOrder) {
	b.Lock()
	defer b.Unlock()
	delete(b.MarginPending.Pending, o.ClOrdId)
}

// 是否存在期货订单
func (b *Broker) HasFuturesOrder(clOrdId string) bool {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	_, exists := b.FuturesPending.Pending[clOrdId]
	return exists
}

// 是否存在杠杆订单
func (b *Broker) HasMarginOrder(clOrdId string) bool {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	_, exists := b.MarginPending.Pending[clOrdId]
	return exists
}

// 是否有挂单
func (b *Broker) HasPending(s string) bool {
	return b.HasFuturesPending(s) || b.HasMarginPending(s)
}

// 是否有期货挂单
func (b *Broker) HasMarginPending(symbol string) bool {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	for _, o := range b.MarginPending.Pending {
		if o.Symbol == symbol {
			return true
		}
	}
	return false
}

// 是否有杠杆挂单
func (b *Broker) HasFuturesPending(symbol string) bool {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	for _, o := range b.FuturesPending.Pending {
		if o.Symbol == symbol {
			return true
		}
	}
	return false
}

// 获取期货挂单
func (b *Broker) GetFuturesOrders(symbol string) []*order.FuturesOrder {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	var orders []*order.FuturesOrder
	for _, o := range b.FuturesPending.Pending {
		if o.Symbol == symbol {
			orders = append(orders, o)
		}
	}
	return orders
}

// 获取杠杆挂单
func (b *Broker) GetMarginOrders(symbol string) []*order.MarginOrder {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	var orders []*order.MarginOrder
	for _, o := range b.MarginPending.Pending {
		if o.Symbol == symbol {
			orders = append(orders, o)
		}
	}
	return orders
}

// 获取数据源对应的仓位, 现货返回杠杆仓位(向下取整), 期货返回期货多头+空头仓位之和
func (b *Broker) GetDataSize(d *data.SymbolData) float64 {
	if d.DataType == data.Futures {
		longSize, shortSize := b.GetFuturesSize(d.Symbol)
		return longSize + shortSize
	} else {
		return b.GetAssetFreeFloor(d.Asset, d.Info)
	}
}

// 获取期货多头+空头仓位
func (b *Broker) GetFuturesSize(symbol string) (longSize float64, shortSize float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if b.Positions[symbol] == nil {
		return 0, 0
	}
	return b.Positions[symbol].SizeLong, b.Positions[symbol].SizeShort
}

// 获取期货多头
func (b *Broker) GetFuturesSizeLong(symbol string) float64 {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if b.Positions[symbol] == nil {
		return 0
	}
	return b.Positions[symbol].SizeLong
}

// 获取期货空头
func (b *Broker) GetFuturesSizeShort(symbol string) float64 {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if b.Positions[symbol] == nil {
		return 0
	}
	return b.Positions[symbol].SizeShort
}

// 获取现货杠杆
func (b *Broker) GetAssetFree(asset string) (size float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if b.AssetWallets[asset] == nil {
		return 0
	}
	return b.AssetWallets[asset].Free
}

// 获取现货可用余额, 向下取整
func (b *Broker) GetAssetFreeFloor(symbol string, d *data.ExchangeInfo) (size float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if b.AssetWallets[symbol] == nil {
		return 0
	}
	return math.Floor(b.AssetWallets[symbol].Free*d.CtVal/d.StepSize) * d.StepSize / d.CtVal
}

// 获取现货可用余额, 向上取整
func (b *Broker) GetAssetFreeCeil(symbol string, d *data.ExchangeInfo) (size float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if b.AssetWallets[symbol] == nil {
		return 0
	}
	return math.Ceil(b.AssetWallets[symbol].Free*d.CtVal/d.StepSize) * d.StepSize / d.CtVal
}

// 获取杠杆总资产, 向下取整
func (b *Broker) GetAssetTotalFloor(symbol string, d *data.ExchangeInfo) (size float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if b.AssetWallets[symbol] == nil {
		return 0
	}
	return math.Floor(b.AssetWallets[symbol].Total()*d.CtVal/d.StepSize) * d.StepSize / d.CtVal
}

// 获取杠杆总资产, 向上取整
func (b *Broker) GetAssetTotalCeil(symbol string, d *data.ExchangeInfo) (size float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if b.AssetWallets[symbol] == nil {
		return 0
	}
	return math.Ceil(b.AssetWallets[symbol].Total()*d.CtVal/d.StepSize) * d.StepSize / d.CtVal
}

// 获取最近的期货挂单
func (b *Broker) LastFuturesOrder(s string) (*order.FuturesOrder, bool) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	var order *order.FuturesOrder
	for _, o := range b.FuturesPending.Pending {
		if o.Symbol == s && (order == nil || o.CreatedTime > order.CreatedTime) {
			order = o
		}
	}
	if order == nil {
		return nil, false
	}
	return order, true
}

// 获取最近的杠杆挂单
func (b *Broker) LastMarginOrder(s string) (*order.MarginOrder, bool) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	var order *order.MarginOrder
	for _, o := range b.MarginPending.Pending {
		if o.Symbol == s && (order == nil || o.CreatedTime > order.CreatedTime) {
			order = o
		}
	}
	if order == nil {
		return nil, false
	}
	return order, true
}

// 某个品种最近的仓位更新时间
func (b *Broker) LastDataUpdateTime(d *data.SymbolData) int64 {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	var pendingUpdateTime int64 = max(b.MarginPending.UpdateTime, b.FuturesPending.UpdateTime)
	var assetUpdateTime int64
	if b.AssetWallets[d.Asset] != nil {
		assetUpdateTime = b.AssetWallets[d.Asset].UpdateTime
	} else {
		assetUpdateTime = 0
	}
	return max(pendingUpdateTime, assetUpdateTime)
}

// 获取USDT余额
func (b *Broker) GetAvailableUSDT() float64 {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if w, ok := b.AssetWallets["USDT"]; !ok {
		return 0
	} else {
		return w.Free
	}
}

// 获取可用保证金
func (b *Broker) GetAvailableMargin() float64 {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	return b.Account.Available
}

// 获取可用余额
func (b *Broker) GetAvailable(d *data.SymbolData) float64 {
	switch d.DataType {
	case data.Futures:
		return b.GetAvailableMargin()
	case data.Spot:
		return b.GetAvailableUSDT()
	default:
		return 0
	}
}

// 获取维持保证金率, TODO 忽略了手续费, 因此会偏大
func (b *Broker) GetMaintenanceMarginRate() float64 {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if b.Account.MaintMargin == 0 {
		return 999
	}
	return b.Account.Equity / b.Account.MaintMargin
}

// 获取开仓均价
func (b *Broker) GetAvgPrice(d *data.SymbolData) float64 {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if d.DataType != data.Futures {
		return 0
	}
	if pos, ok := b.Positions[d.Symbol]; !ok {
		return 0
	} else {
		return max(pos.OpenPriceLong, pos.OpenPriceShort)
	}
}

// 加锁
func (b *Broker) Lock() {
	b.rwMutex.Lock()
}

// 解锁
func (b *Broker) Unlock() {
	b.rwMutex.Unlock()
}

// 获取当前杠杆下的最大仓位
func (b *Broker) TierMaxSize(d *data.SymbolData) (size float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	maxSize := math.Inf(1)
	tiers, ok := b.PositionTiers[d.Symbol]
	if !ok {
		return maxSize
	}
	if b.Leverages[d.Symbol] == 0 {
		return maxSize
	}
	leverage := float64(b.Leverages[d.Symbol])
	for _, t := range tiers {
		if t.InitialLeverage >= leverage {
			maxSize = t.MaxSize
		} else {
			break
		}
	}
	if maxSize == 0 {
		return math.Inf(1)
	}
	return maxSize
}

// 获取当前杠杆下的最大名义价值
func (b *Broker) TierMaxNotional(d *data.SymbolData) (notional float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	maxNotional := math.Inf(1)
	tiers, ok := b.PositionTiers[d.Symbol]
	if !ok {
		return maxNotional
	}
	if b.Leverages[d.Symbol] == 0 {
		return maxNotional
	}
	leverage := float64(b.Leverages[d.Symbol])
	for _, t := range tiers {
		if t.InitialLeverage >= leverage {
			maxNotional = t.MaxNotional
		} else {
			break
		}
	}
	if maxNotional == 0 {
		return math.Inf(1)
	}
	return maxNotional
}

func (b *Broker) DataLeverage(d *data.SymbolData) (leverage float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	if d.DataType == data.Futures {
		leverage = float64(b.Leverages[d.Symbol])
	} else {
		leverage = 1
	}
	return leverage
}

// 获取资金费率下限
func (b *Broker) FundingFloor(d *data.SymbolData) (floor float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	// 假如下限是负无穷, 则根据杠杆倍率计算
	if d.Funding.Floor == math.Inf(-1) {
		mmr := b.PositionTiers[d.Symbol][0].MaintMarginRatio
		return -0.75 * mmr
	}
	return d.Funding.Floor
}

// 获取资金费率上限
func (b *Broker) FundingCap(d *data.SymbolData) (cap float64) {
	b.rwMutex.RLock()
	defer b.rwMutex.RUnlock()
	// 假如上限是正无穷, 则根据杠杆倍率计算
	if d.Funding.Cap == math.Inf(1) {
		mmr := b.PositionTiers[d.Symbol][0].MaintMarginRatio
		return 0.75 * mmr
	}
	return d.Funding.Cap
}
