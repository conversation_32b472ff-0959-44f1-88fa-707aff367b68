package book

import (
	"GoTrader/strategy"
	"GoTrader/strategy/book"
	"encoding/json"
	"strconv"
)

// redis存储的大额订单盘口任务参数
type BookTaskRedisParams struct {
	Symbol             string              `json:"s"`  // 交易品种
	Status             strategy.TaskStatus `json:"S"`  // 状态
	Leverage           int64               `json:"g"`  // 杠杆
	Exchange           string              `json:"ex"` // 交易所
	QuoteCoeff         float64             `json:"qc"` // 24h成交额系数
	OpenIntCoeff       float64             `json:"oc"` // 持仓量系数
	CapitalLimit       float64             `json:"cl"` // 资金分配上限
	PriceLevel         int64               `json:"pl"` // 价格档位数量
	MinLargeOrderValue float64             `json:"ml"` // 最小大额订单阈值
	CreateTime         int64               `json:"c"`  // 创建时间
	Updatetime         int64               `json:"u"`  // 更新时间
}

func NewTaskRedisParams(jsonString string) BookTaskRedisParams {
	taskparams := BookTaskRedisParams{}
	jsonmap := make(map[string]string)
	json.Unmarshal([]byte(jsonString), &jsonmap)

	taskparams.Symbol = jsonmap["s"]
	taskparams.Leverage, _ = strconv.ParseInt(jsonmap["g"], 10, 64)
	taskparams.Status, _ = strategy.ParseTaskStatus(jsonmap["S"])
	taskparams.Exchange = jsonmap["ex"]
	taskparams.QuoteCoeff, _ = strconv.ParseFloat(jsonmap["qc"], 64)
	taskparams.OpenIntCoeff, _ = strconv.ParseFloat(jsonmap["oc"], 64)
	taskparams.CapitalLimit, _ = strconv.ParseFloat(jsonmap["cl"], 64)
	taskparams.PriceLevel, _ = strconv.ParseInt(jsonmap["pl"], 10, 64)
	taskparams.CreateTime, _ = strconv.ParseInt(jsonmap["c"], 10, 64)
	taskparams.Updatetime, _ = strconv.ParseInt(jsonmap["u"], 10, 64)
	taskparams.MinLargeOrderValue, _ = strconv.ParseFloat(jsonmap["ml"], 64)

	// 默认杠杆
	if taskparams.Leverage <= 0 {
		taskparams.Leverage = 3
	}

	return taskparams
}

func NewBookParams(tp BookTaskRedisParams, ID string) (p book.BookParams) {
	p = book.BookParams{
		ID:                 ID,
		Symbol:             tp.Symbol,
		Leverage:           tp.Leverage,
		Exchange:           tp.Exchange,
		QuoteCoeff:         tp.QuoteCoeff,
		OpenIntCoeff:       tp.OpenIntCoeff,
		CapitalLimit:       tp.CapitalLimit,
		PriceLevel:         tp.PriceLevel,
		MinLargeOrderValue: tp.MinLargeOrderValue,
		Status:             tp.Status,
	}
	return p
}
