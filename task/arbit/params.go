package arbit

import (
	"GoTrader/pkg/data"
	"GoTrader/strategy"
	"GoTrader/strategy/arbit"
	"encoding/json"
	"math"
	"strconv"
)

// redis存储的套利任务参数
type ArbitTaskRedisParams struct {
	Symbol           string              `json:"s"`            // 交易品种
	Status           strategy.TaskStatus `json:"S"`            // 状态
	Leverage         int64               `json:"g"`            // 杠杆
	AutoSeconds      int64               `json:"a"`            // 自动调仓频率
	OpenDiffLow      float64             `json:"il,omitempty"` // 价差开仓阈值
	OpenDiffHigh     float64             `json:"ih,omitempty"` // 价差开仓高档位
	CloseDiff        float64             `json:"o,omitempty"`  // 价差平仓阈值
	LeftExchange     string              `json:"l"`            // 左侧的交易所
	LeftAssetType    string              `json:"L"`            // 左侧的品种类型, 期货or现货
	RightExchange    string              `json:"r"`            // 右侧的交易所
	RightAssetType   string              `json:"R"`            // 右侧的品种类型, 期货or现货
	OnceOpenValue    float64             `json:"v"`            // 开仓价值
	ProfitSpaceLow   float64             `json:"pl"`           // 利润空间
	ProfitSpaceHigh  float64             `json:"ph"`           // 利润空间
	CapitalLimitLow  float64             `json:"cl"`           // 资金分配上限
	CapitalLimitHigh float64             `json:"ch"`           // 资金分配上限
	CreateTime       int64               `json:"c"`            // 创建时间
	Updatetime       int64               `json:"u"`            // 更新时间

	// 扩展参数
	RightOpenDiff  float64 `json:"ri,omitempty"` // 右侧价差开仓阈值
	RightCloseDiff float64 `json:"ro,omitempty"` // 右侧价差平仓阈值
}

func NewTaskRedisParams(jsonString string) ArbitTaskRedisParams {
	taskparams := ArbitTaskRedisParams{}
	jsonmap := make(map[string]string)
	json.Unmarshal([]byte(jsonString), &jsonmap)

	taskparams.Symbol = jsonmap["s"]
	taskparams.Leverage, _ = strconv.ParseInt(jsonmap["g"], 10, 64)
	taskparams.Status, _ = strategy.ParseTaskStatus(jsonmap["S"])
	taskparams.AutoSeconds, _ = strconv.ParseInt(jsonmap["a"], 10, 64)
	taskparams.OpenDiffLow, _ = strconv.ParseFloat(jsonmap["il"], 64)
	taskparams.OpenDiffHigh, _ = strconv.ParseFloat(jsonmap["ih"], 64)
	taskparams.CloseDiff, _ = strconv.ParseFloat(jsonmap["o"], 64)
	taskparams.LeftExchange = jsonmap["l"]
	taskparams.LeftAssetType = jsonmap["L"]
	taskparams.RightExchange = jsonmap["r"]
	taskparams.RightAssetType = jsonmap["R"]
	taskparams.OnceOpenValue, _ = strconv.ParseFloat(jsonmap["v"], 64)
	taskparams.ProfitSpaceLow, _ = strconv.ParseFloat(jsonmap["pl"], 64)
	taskparams.ProfitSpaceHigh, _ = strconv.ParseFloat(jsonmap["ph"], 64)
	taskparams.CapitalLimitLow, _ = strconv.ParseFloat(jsonmap["cl"], 64)
	taskparams.CapitalLimitHigh, _ = strconv.ParseFloat(jsonmap["ch"], 64)

	// 扩展参数
	taskparams.RightOpenDiff, _ = strconv.ParseFloat(jsonmap["ri"], 64)
	taskparams.RightCloseDiff, _ = strconv.ParseFloat(jsonmap["ro"], 64)

	// 转换为百分比
	taskparams.OpenDiffLow = taskparams.OpenDiffLow * 100
	taskparams.OpenDiffHigh = taskparams.OpenDiffHigh * 100
	taskparams.CloseDiff = taskparams.CloseDiff * 100
	taskparams.ProfitSpaceLow = taskparams.ProfitSpaceLow * 100
	taskparams.ProfitSpaceHigh = taskparams.ProfitSpaceHigh * 100
	taskparams.RightOpenDiff = taskparams.RightOpenDiff * 100
	taskparams.RightCloseDiff = taskparams.RightCloseDiff * 100

	// 自动调整设置参数

	if taskparams.AutoSeconds > 0 {
		taskparams.CloseDiff = math.Inf(-1)
	}

	// 默认杠杆
	if taskparams.Leverage <= 0 {
		taskparams.Leverage = 3
	}

	// 资金上限
	if taskparams.CapitalLimitLow <= 0 {
		taskparams.CapitalLimitLow = math.Inf(1)
	}
	if taskparams.CapitalLimitHigh <= 0 {
		taskparams.CapitalLimitHigh = math.Inf(1)
	}

	// 调整不合理值
	taskparams.OpenDiffHigh = max(taskparams.OpenDiffHigh, taskparams.OpenDiffLow)
	taskparams.ProfitSpaceHigh = max(taskparams.ProfitSpaceHigh, taskparams.ProfitSpaceLow)
	taskparams.CapitalLimitHigh = max(taskparams.CapitalLimitHigh, taskparams.CapitalLimitLow)

	return taskparams
}

func NewArbitParams(tp ArbitTaskRedisParams, ID string) (p arbit.ArbitrageParams) {
	p = arbit.ArbitrageParams{
		ID:               ID,
		Symbol:           tp.Symbol,
		Leverage:         tp.Leverage,
		AutoSeconds:      tp.AutoSeconds,
		LeftExchange:     tp.LeftExchange,
		RightExchange:    tp.RightExchange,
		OpenDiffLow:      tp.OpenDiffLow,
		OpenDiffHigh:     tp.OpenDiffHigh,
		CloseDiff:        tp.CloseDiff,
		OnceOpenValue:    tp.OnceOpenValue,
		ProfitSpaceLow:   tp.ProfitSpaceLow,
		ProfitSpaceHigh:  tp.ProfitSpaceHigh,
		MinAvailable:     200,
		Status:           tp.Status,
		CapitalLimitLow:  tp.CapitalLimitLow,
		CapitalLimitHigh: tp.CapitalLimitHigh,
		Extra: arbit.ExtraParams{
			RightOpenDiff:  tp.RightOpenDiff,
			RightCloseDiff: tp.RightCloseDiff,
		},
	}
	if tp.LeftAssetType == "S" {
		p.LeftType = data.Spot
	} else {
		p.LeftType = data.Futures
	}
	if tp.RightAssetType == "F" {
		p.RightType = data.Futures
	} else {
		p.RightType = data.Spot
	}
	return p
}
