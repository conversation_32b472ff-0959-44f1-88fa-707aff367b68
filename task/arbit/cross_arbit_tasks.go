package arbit

import (
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/logs"
	"GoTrader/strategy"
	"GoTrader/strategy/arbit"
	"context"
	"io"
	"log/slog"
	"maps"
	"os"
	"path/filepath"
	"time"

	"github.com/natefinch/lumberjack"
	"github.com/redis/go-redis/v9"
)

// 跨所套利运行任务
func RunCrossArbitTasks(
	ticker *time.Ticker,
	signalChan chan os.Signal,
	rdb *redis.Client,
	taskTable string,
	infoTable string,
	logDir string,
	taskName string,
	lEx string, rEx string,
	keepAvailable float64,

	lac client.APIClient,
	ldc client.DataClient,
	lb *broker.Broker,

	rac client.APIClient,
	rdc client.DataClient,
	rb *broker.Broker,

	feishuWriter *logs.FeishuWriter,
	taskLogger *slog.Logger,
	notifyLogger *slog.Logger,
) {

	// 策略列表 ID: 策略
	// 策略列表理论上只存放正在运行的策略
	strategyMap := map[string]*arbit.Arbitrage{}

	hsetChan := make(chan map[string]string, 1000)

	// 定时接受channel信息批量写入
	go func() {
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-signalChan:
				return
			case <-ticker.C:
				// 从chan中获取数据
				hsetData := map[string]string{}
				for range 1000 {
					select {
					case data := <-hsetChan:
						maps.Copy(hsetData, data)
					default:
						if len(hsetData) > 0 {
							ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
							err := rdb.HSet(ctx, infoTable, hsetData).Err()
							if err != nil {
								taskLogger.Warn("write redis failed", "error", err)
							}
							cancel()
						}
					}
				}
			}
		}
	}()

	// 循环
	for {
		// 参数列表, 从redis读取 ID: 参数
		paramsMap := map[string]arbit.ArbitrageParams{}

		// 过滤机会参数
		taskLogger.Info("开始获取套利任务")
		pre := time.Now()
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
		tasksMap, err := rdb.HGetAll(ctx, taskTable).Result()
		if err != nil {
			taskLogger.Warn("获取套利任务失败", "error", err)
			continue
		}
		taskLogger.Info("获取套利任务成功", "tasks", len(tasksMap), "elapsed", time.Since(pre).String())
		cancel()
		for ID, jsonString := range tasksMap {
			taskparams := NewTaskRedisParams(jsonString)
			p := NewArbitParams(taskparams, ID)
			// 只接收两端为OK的任务
			if !(p.LeftExchange == lEx && p.RightExchange == rEx) {
				continue
			}
			p.MinAvailable = keepAvailable
			// 存入参数列表
			paramsMap[ID] = p
		}

		// 对现有任务进行判断, 并更新
		lUnsubDatas := []*data.SymbolData{}
		rUnsubDatas := []*data.SymbolData{}
		for ID, stra := range strategyMap {
			// 检查是否有已停止任务, 如果有则删除
			if !stra.Running() {
				taskLogger.Info("任务已停止, 取消订阅, 删除任务 ", "id", ID)
				// 取消订阅数据源
				lUnsubDatas = append(lUnsubDatas, stra.LeftLeg.Data)
				rUnsubDatas = append(rUnsubDatas, stra.RightLeg.Data)
				delete(strategyMap, ID)
			}
		}
		// 取消订阅数据源
		if len(lUnsubDatas) > 0 {
			ldc.UnsubscribeDatas(lUnsubDatas)
		}
		if len(rUnsubDatas) > 0 {
			rdc.UnsubscribeDatas(rUnsubDatas)
		}

		// 对现有任务进行判断
		for ID, stra := range strategyMap {
			redisParam, ok := paramsMap[ID]
			switch {
			case !ok:
				// 任务不存在, 停止任务
				strategyMap[ID].Stop()
				taskLogger.Info("不存在表中的任务", "id", ID)
			case (redisParam.Status == strategy.Stop && stra.Params.Status < strategy.Stop):
				// 不存在表中的任务或者stop任务直接停止
				// stra.Params.Status = arbit.strategy.Stop
				strategyMap[ID].Stop()
				taskLogger.Warn("停止任务", "id", ID)
			case redisParam.Status == strategy.Reduce && stra.Params.Status < strategy.Reduce:
				// 正在运行的任务转换需要为减仓任务
				strategyMap[ID].Reduce()
				taskLogger.Info("减仓任务: ", "id", ID)
			case redisParam.Status == strategy.Pause && stra.Params.Status < strategy.Pause && stra.Running():
				// 正在运行的任务需要转换为暂停任务
				strategyMap[ID].Pause()
				taskLogger.Info("暂停任务: ", "id", ID)
			}

		}

		// 启动新任务
		for ID, redisParam := range paramsMap {
			select {
			case <-signalChan:
				taskLogger.Info("Received signal to exit")
				for _, stra := range strategyMap {
					stra.Pause()
				}
				return
			default:
			}

			// 任务不存在, 新建任务
			if _, ok := strategyMap[ID]; !ok && redisParam.Status <= strategy.Reduce {
				symbol := redisParam.Symbol
				// 创建 data
				ld := data.NewSymbolData(symbol, redisParam.LeftType)
				rd := data.NewSymbolData(symbol, redisParam.RightType)
				// 更新数据
				err := lac.UpdateDatas([]*data.SymbolData{ld})
				if err != nil {
					taskLogger.Error("更新数据失败", "error", err)
					continue
				}
				err = rac.UpdateDatas([]*data.SymbolData{rd})
				if err != nil {
					taskLogger.Error("更新数据失败", "error", err)
					continue
				}
				// 订阅数据
				err = ldc.SubscribeDatas([]*data.SymbolData{ld})
				if err != nil {
					taskLogger.Error("订阅数据失败", "error", err)
					continue
				}
				err = rdc.SubscribeDatas([]*data.SymbolData{rd})
				if err != nil {
					taskLogger.Error("订阅数据失败", "error", err)
					continue
				}

				// 创建leg
				lg := &arbit.ArbitLeg{
					Client: lac,
					Broker: lb,
					Data:   ld,
				}
				rg := &arbit.ArbitLeg{
					Client: rac,
					Broker: rb,
					Data:   rd,
				}

				// 策略logger
				straPath := filepath.Join(logDir, taskName, ID+".log")
				straFile := &lumberjack.Logger{
					Filename:   straPath, // 日志文件的位置
					MaxSize:    10,       // 文件最大尺寸（以MB为单位）
					MaxBackups: 10,       // 保留的最大旧文件数量
					MaxAge:     7,        // 保留旧文件的最大天数
					Compress:   true,     // 是否压缩/归档旧文件
					LocalTime:  true,     // 使用本地时间创建时间戳
				}
				defer straFile.Close()
				straLogger := slog.New(slog.NewJSONHandler(io.MultiWriter(straFile, feishuWriter), &slog.HandlerOptions{
					Level: slog.LevelInfo,
				}))

				redisCtx := &strategy.RedisCtx{Client: rdb, Table: infoTable}
				strategyMap[ID] = arbit.NewArbitrage(lg, rg, redisParam, redisCtx, hsetChan, straLogger, notifyLogger)
				strategyMap[ID].Start()
				taskLogger.Info("启动任务", slog.String("symbol", symbol), slog.String("params", redisParam.String()))
				continue
			}
		}

		// 未匹配任务的币种强行平仓
		for _, pos := range rb.Positions {
			s := pos.Symbol
			exist := false
			// 任务中查找是否存在该品种, 存在则跳过
			for _, params := range paramsMap {
				if params.Symbol == s {
					exist = true
					break
				}
			}
			if exist {
				continue
			}
			if pos.SizeLong == 0 && pos.SizeShort == 0 {
				continue
			}
			// 不存在的任务视为stop进行平仓操作
			taskLogger.Warn("未匹配任务的币种强行平仓", "symbol", s)
			ld := data.NewSymbolData(s, data.Futures)
			rd := data.NewSymbolData(s, data.Futures)
			lac.UpdateDatas([]*data.SymbolData{ld})
			rac.UpdateDatas([]*data.SymbolData{rd})
			lac.UpdateFuturesLeverageSingle(lb, ld)
			rac.UpdateFuturesLeverageSingle(rb, rd)
			err := lac.CloseFutures(lb, ld)
			if err != nil {
				taskLogger.Error("强行平仓左腿失败", "symbol", s, "err", err)
				continue
			}
			err = rac.CloseMargin(rb, rd)
			if err != nil {
				taskLogger.Error("强行平仓右腿失败", "symbol", s, "err", err)
				continue
			}
		}

		// 结束进程
		select {
		case <-signalChan:
			taskLogger.Info("Received signal to exit")
			for _, stra := range strategyMap {
				stra.Pause()
			}
			return
		case <-ticker.C:
			continue
		}
	}
}
