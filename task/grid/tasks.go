package grid

import (
	config "GoTrader/internal"
	"GoTrader/pkg/broker"
	"GoTrader/pkg/client"
	"GoTrader/pkg/data"
	"GoTrader/pkg/logs"
	"GoTrader/strategy"
	"GoTrader/strategy/grid"
	"context"
	"io"
	"log/slog"
	"maps"
	"os"
	"path/filepath"
	"time"

	"github.com/natefinch/lumberjack"
	"github.com/redis/go-redis/v9"
)

// 网格运行任务
func RunGridTasks(
	ticker *time.Ticker,
	signalChan chan os.Signal,
	rdb *redis.Client,
	taskTable string,
	logDir string,
	taskName string,
	ex string,
	ac client.APIClient,
	dc client.DataClient,
	b *broker.Broker,
	feishuWriter *logs.FeishuWriter,
	taskLogger *slog.Logger,
) {

	// 策略列表 ID: 策略
	// 策略列表理论上只存放正在运行的策略
	strategyMap := map[string]*grid.GridStrategy{}

	hsetChan := make(chan map[string]string, 1000)

	// 定时接受channel信息批量写入
	go func() {
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-signalChan:
				return
			case <-ticker.C:
				// 从chan中获取数据
				hsetData := map[string]string{}
				for range 1000 {
					select {
					case data := <-hsetChan:
						maps.Copy(hsetData, data)
					default:
						if len(hsetData) > 0 {
							ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
							err := rdb.HSet(ctx, config.InfoTable, hsetData).Err()
							if err != nil {
								taskLogger.Warn("write redis failed", "error", err)
							}
							cancel()
						}
					}
				}
			}
		}
	}()

	// 循环
	for {
		// 参数列表, 从redis读取 ID: 参数
		paramsMap := map[string]grid.GridParams{}

		// 过滤机会参数
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
		tasksMap, err := rdb.HGetAll(ctx, taskTable).Result()
		if err != nil {
			taskLogger.Warn("获取套利任务失败", "error", err)
			continue
		}
		cancel()
		for ID, jsonString := range tasksMap {
			taskparams := NewTaskRedisParams(jsonString)
			p := NewGridParams(taskparams, ID)
			if p.Exchange != ex {
				continue
			}
			// 存入参数列表
			paramsMap[ID] = p
		}

		// 对现有任务进行判断, 取消订阅, 并删除任务
		unsubDatas := []*data.SymbolData{}
		for ID, stra := range strategyMap {
			// 检查是否有已停止任务, 如果有则删除
			if !stra.Running() {
				taskLogger.Info("任务已停止, 取消订阅, 删除任务 ", "id", ID)
				// 取消订阅数据源
				unsubDatas = append(unsubDatas, stra.Data)
				delete(strategyMap, ID)
			}
		}
		if len(unsubDatas) > 0 {
			dc.UnsubscribeDatas(unsubDatas)
		}

		// 对现有任务进行判断, 并更新
		for ID, stra := range strategyMap {
			redisParam, ok := paramsMap[ID]
			switch {
			case !ok:
				// 任务不存在, 停止任务
				strategyMap[ID].Stop()
				taskLogger.Info("未找到任务", "id", ID)
			case redisParam.Status == strategy.Stop && stra.Status() < strategy.Stop:
				// 不存在表中的任务或者stop任务直接停止
				strategyMap[ID].Stop()
				taskLogger.Info("停止任务", "id", ID)
			case redisParam.Status == strategy.Reduce && stra.Status() < strategy.Reduce:
				// 正在运行的任务转换需要为减仓任务
				strategyMap[ID].Reduce()
				taskLogger.Info("减仓任务: ", "id", ID)
			case redisParam.Status == strategy.Pause && stra.Status() < strategy.Pause && stra.Running():
				// 正在运行的任务需要转换为暂停任务
				strategyMap[ID].Pause()
				taskLogger.Info("暂停任务: ", "id", ID)
			}
		}

		// 启动新任务
		for ID, redisParam := range paramsMap {
			select {
			case <-signalChan:
				taskLogger.Info("Received signal to exit")
				for _, stra := range strategyMap {
					stra.Pause()
				}
				return
			default:
			}

			// 任务不存在, 新建任务
			if _, ok := strategyMap[ID]; !ok && redisParam.Status <= strategy.Reduce {
				symbol := redisParam.Symbol
				// 创建 data
				d := data.NewSymbolData(symbol, data.Futures)
				// 更新数据
				err := ac.UpdateDatas([]*data.SymbolData{d})
				if err != nil {
					taskLogger.Error("更新数据失败", "error", err)
					continue
				}
				// 订阅数据
				err = dc.SubscribeDatas([]*data.SymbolData{d})
				if err != nil {
					taskLogger.Error("订阅数据失败", "error", err)
					continue
				}

				// 策略logger
				straPath := filepath.Join(logDir, taskName, ID+".log")
				straFile := &lumberjack.Logger{
					Filename:   straPath, // 日志文件的位置
					MaxSize:    10,       // 文件最大尺寸（以MB为单位）
					MaxBackups: 10,       // 保留的最大旧文件数量
					MaxAge:     7,        // 保留旧文件的最大天数
					Compress:   true,     // 是否压缩/归档旧文件
					LocalTime:  true,     // 使用本地时间创建时间戳
				}
				defer straFile.Close()
				straLogger := slog.New(slog.NewJSONHandler(io.MultiWriter(straFile, feishuWriter), &slog.HandlerOptions{
					Level: slog.LevelInfo,
				}))

				redisCtx := &strategy.RedisCtx{Client: rdb, Table: config.InfoTable}
				strategyMap[ID] = grid.NewGridStrategy(d, b, ac, redisParam, redisCtx, hsetChan, straLogger)
				strategyMap[ID].Start()
				taskLogger.Info("启动任务", slog.String("symbol", symbol), slog.String("params", redisParam.String()))
				continue
			}
		}

		// 未匹配任务的币种强行平仓
		for _, pos := range b.Positions {
			s := pos.Symbol
			exist := false
			// 任务中查找是否存在该品种, 存在则跳过
			for _, params := range paramsMap {
				if params.Symbol == s {
					exist = true
					break
				}
			}
			if exist {
				continue
			}
			if pos.SizeLong == 0 && pos.SizeShort == 0 {
				continue
			}
			// 不存在的任务视为stop进行平仓操作
			taskLogger.Info("未匹配任务的币种强行平仓", "symbol", s)
			d := data.NewSymbolData(s, data.Futures)
			ac.UpdateDatas([]*data.SymbolData{d})
			ac.UpdateFuturesLeverageSingle(b, d)
			err := ac.CloseFutures(b, d)
			if err != nil {
				taskLogger.Error("强行平仓期货失败", "symbol", s, "err", err)
				continue
			}
		}

		// 结束进程
		select {
		case <-signalChan:
			taskLogger.Info("Received signal to exit")
			for _, stra := range strategyMap {
				stra.Pause()
			}
			return
		case <-ticker.C:
			continue
		}
	}
}
