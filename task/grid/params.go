package grid

import (
	"GoTrader/strategy"
	"GoTrader/strategy/grid"
	"encoding/json"
	"strconv"
)

// redis存储的网格任务参数
type GridTaskRedisParams struct {
	Symbol       string              `json:"s"`  // 交易品种
	Status       strategy.TaskStatus `json:"S"`  // 状态
	Leverage     int64               `json:"g"`  // 杠杆
	Exchange     string              `json:"ex"` // 交易所
	CapitalLimit float64             `json:"cl"` // 资金分配上限
	PriceLevel   int64               `json:"pl"` // 价格档位数量
	PriceUpper   float64             `json:"up"` // 价格上限
	PriceLower   float64             `json:"lo"` // 价格下限
	TriggerPrice float64             `json:"tp"` // 触发价格
	MinAvailable float64             `json:"ma"` // 最小可用余额
	CreateTime   int64               `json:"c"`  // 创建时间
	Updatetime   int64               `json:"u"`  // 更新时间
}

func NewTaskRedisParams(jsonString string) GridTaskRedisParams {
	taskparams := GridTaskRedisParams{}
	jsonmap := make(map[string]string)
	json.Unmarshal([]byte(jsonString), &jsonmap)

	taskparams.Symbol = jsonmap["s"]
	taskparams.Leverage, _ = strconv.ParseInt(jsonmap["g"], 10, 64)
	taskparams.Status, _ = strategy.ParseTaskStatus(jsonmap["S"])
	taskparams.Exchange = jsonmap["ex"]
	taskparams.CapitalLimit, _ = strconv.ParseFloat(jsonmap["cl"], 64)
	taskparams.PriceLevel, _ = strconv.ParseInt(jsonmap["pl"], 10, 64)
	taskparams.PriceUpper, _ = strconv.ParseFloat(jsonmap["up"], 64)
	taskparams.PriceLower, _ = strconv.ParseFloat(jsonmap["lo"], 64)
	taskparams.TriggerPrice, _ = strconv.ParseFloat(jsonmap["tp"], 64)
	taskparams.MinAvailable, _ = strconv.ParseFloat(jsonmap["ma"], 64)
	taskparams.CreateTime, _ = strconv.ParseInt(jsonmap["c"], 10, 64)
	taskparams.Updatetime, _ = strconv.ParseInt(jsonmap["u"], 10, 64)

	// 默认杠杆
	if taskparams.Leverage <= 0 {
		taskparams.Leverage = 3
	}

	return taskparams
}

func NewGridParams(tp GridTaskRedisParams, ID string) (p grid.GridParams) {
	p = grid.GridParams{
		ID:           ID,
		Symbol:       tp.Symbol,
		Leverage:     tp.Leverage,
		Exchange:     tp.Exchange,
		CapitalLimit: tp.CapitalLimit,
		PriceLevel:   tp.PriceLevel,
		PriceUpper:   tp.PriceUpper,
		PriceLower:   tp.PriceLower,
		TriggerPrice: tp.TriggerPrice,
		MinAvailable: tp.MinAvailable,
		Status:       tp.Status,
	}
	return p
}
